{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\hooks\\\\useAuth.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authService } from '../services/auth';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n  useEffect(() => {\n    const initializeAuth = async () => {\n      const savedToken = localStorage.getItem('token');\n      const savedUser = localStorage.getItem('user');\n      if (savedToken && savedUser) {\n        try {\n          const userData = JSON.parse(savedUser);\n          setUser(userData);\n          setToken(savedToken);\n        } catch (error) {\n          // Clear invalid stored data\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          setToken(null);\n        }\n      }\n      setLoading(false);\n    };\n    initializeAuth();\n  }, []);\n  const login = async (email, password) => {\n    try {\n      const response = await authService.login(email, password);\n\n      // The API client returns the response body directly, so response.data contains the actual data\n      const {\n        token: newToken,\n        user: userData\n      } = response.data;\n\n      // Normalize user data to match frontend expectations (lowercase properties)\n      const normalizedUser = {\n        id: userData.UserID,\n        email: userData.Email,\n        name: userData.Name,\n        firstName: userData.FirstName,\n        lastName: userData.LastName,\n        role: userData.Role,\n        // This is the key property for RBAC\n        isActive: userData.IsActive,\n        emailVerified: userData.EmailVerified\n      };\n      localStorage.setItem('token', newToken);\n      localStorage.setItem('user', JSON.stringify(normalizedUser));\n      setToken(newToken);\n      setUser(normalizedUser);\n      return {\n        success: true,\n        user: normalizedUser\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      return {\n        success: false,\n        error: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message || 'Login failed'\n      };\n    }\n  };\n  const register = async userData => {\n    try {\n      const response = await authService.register(userData);\n      const {\n        token: newToken,\n        user: newUser\n      } = response;\n      localStorage.setItem('token', newToken);\n      setToken(newToken);\n      setUser(newUser);\n      return {\n        success: true,\n        user: newUser\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('Registration failed:', error);\n      return {\n        success: false,\n        error: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Registration failed'\n      };\n    }\n  };\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setToken(null);\n    setUser(null);\n  };\n  const updateProfile = async profileData => {\n    try {\n      const response = await authService.updateProfile(profileData);\n      setUser(response.user);\n      return {\n        success: true,\n        user: response.user\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('Profile update failed:', error);\n      return {\n        success: false,\n        error: ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Profile update failed'\n      };\n    }\n  };\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    register,\n    logout,\n    updateProfile,\n    isAuthenticated: !!user\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 9\n  }, this);\n};\n_s2(AuthProvider, \"/pbUqy0QsBvMqKPYubk3+KKKH8I=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "authService", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "token", "setToken", "localStorage", "getItem", "initializeAuth", "savedToken", "savedUser", "userData", "JSON", "parse", "error", "removeItem", "login", "email", "password", "response", "newToken", "data", "normalizedUser", "id", "UserID", "Email", "name", "Name", "firstName", "FirstName", "lastName", "LastName", "role", "Role", "isActive", "IsActive", "emailVerified", "EmailVerified", "setItem", "stringify", "success", "_error$response", "_error$response$data", "message", "register", "newUser", "_error$response2", "_error$response2$data", "console", "logout", "updateProfile", "profileData", "_error$response3", "_error$response3$data", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/hooks/useAuth.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authService } from '../services/auth';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n    const context = useContext(AuthContext);\n    if (!context) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n    const [user, setUser] = useState(null);\n    const [loading, setLoading] = useState(true);\n    const [token, setToken] = useState(localStorage.getItem('token'));\n\n    useEffect(() => {\n        const initializeAuth = async () => {\n            const savedToken = localStorage.getItem('token');\n            const savedUser = localStorage.getItem('user');\n            if (savedToken && savedUser) {\n                try {\n                    const userData = JSON.parse(savedUser);\n                    setUser(userData);\n                    setToken(savedToken);\n                } catch (error) {\n                    // Clear invalid stored data\n                    localStorage.removeItem('token');\n                    localStorage.removeItem('user');\n                    setToken(null);\n                }\n            }\n            setLoading(false);\n        };\n\n        initializeAuth();\n    }, []);\n\n    const login = async (email, password) => {\n        try {\n            const response = await authService.login(email, password);\n\n            // The API client returns the response body directly, so response.data contains the actual data\n            const { token: newToken, user: userData } = response.data;\n\n            // Normalize user data to match frontend expectations (lowercase properties)\n            const normalizedUser = {\n                id: userData.UserID,\n                email: userData.Email,\n                name: userData.Name,\n                firstName: userData.FirstName,\n                lastName: userData.LastName,\n                role: userData.Role, // This is the key property for RBAC\n                isActive: userData.IsActive,\n                emailVerified: userData.EmailVerified\n            };\n\n            localStorage.setItem('token', newToken);\n            localStorage.setItem('user', JSON.stringify(normalizedUser));\n            setToken(newToken);\n            setUser(normalizedUser);\n\n            return { success: true, user: normalizedUser };\n        } catch (error) {\n            return {\n                success: false,\n                error: error.response?.data?.message || error.message || 'Login failed'\n            };\n        }\n    };\n\n    const register = async (userData) => {\n        try {\n            const response = await authService.register(userData);\n            const { token: newToken, user: newUser } = response;\n            \n            localStorage.setItem('token', newToken);\n            setToken(newToken);\n            setUser(newUser);\n            \n            return { success: true, user: newUser };\n        } catch (error) {\n            console.error('Registration failed:', error);\n            return { \n                success: false, \n                error: error.response?.data?.message || 'Registration failed' \n            };\n        }\n    };\n\n    const logout = () => {\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n        setToken(null);\n        setUser(null);\n    };\n\n    const updateProfile = async (profileData) => {\n        try {\n            const response = await authService.updateProfile(profileData);\n            setUser(response.user);\n            return { success: true, user: response.user };\n        } catch (error) {\n            console.error('Profile update failed:', error);\n            return { \n                success: false, \n                error: error.response?.data?.message || 'Profile update failed' \n            };\n        }\n    };\n\n    const value = {\n        user,\n        token,\n        loading,\n        login,\n        register,\n        logout,\n        updateProfile,\n        isAuthenticated: !!user\n    };\n\n    return (\n        <AuthContext.Provider value={value}>\n            {children}\n        </AuthContext.Provider>\n    );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACV,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAClE;EACA,OAAOD,OAAO;AAClB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC1C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAACmB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;EAEjEnB,SAAS,CAAC,MAAM;IACZ,MAAMoB,cAAc,GAAG,MAAAA,CAAA,KAAY;MAC/B,MAAMC,UAAU,GAAGH,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAChD,MAAMG,SAAS,GAAGJ,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC9C,IAAIE,UAAU,IAAIC,SAAS,EAAE;QACzB,IAAI;UACA,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACH,SAAS,CAAC;UACtCT,OAAO,CAACU,QAAQ,CAAC;UACjBN,QAAQ,CAACI,UAAU,CAAC;QACxB,CAAC,CAAC,OAAOK,KAAK,EAAE;UACZ;UACAR,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;UAChCT,YAAY,CAACS,UAAU,CAAC,MAAM,CAAC;UAC/BV,QAAQ,CAAC,IAAI,CAAC;QAClB;MACJ;MACAF,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC;IAEDK,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACrC,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAM9B,WAAW,CAAC2B,KAAK,CAACC,KAAK,EAAEC,QAAQ,CAAC;;MAEzD;MACA,MAAM;QAAEd,KAAK,EAAEgB,QAAQ;QAAEpB,IAAI,EAAEW;MAAS,CAAC,GAAGQ,QAAQ,CAACE,IAAI;;MAEzD;MACA,MAAMC,cAAc,GAAG;QACnBC,EAAE,EAAEZ,QAAQ,CAACa,MAAM;QACnBP,KAAK,EAAEN,QAAQ,CAACc,KAAK;QACrBC,IAAI,EAAEf,QAAQ,CAACgB,IAAI;QACnBC,SAAS,EAAEjB,QAAQ,CAACkB,SAAS;QAC7BC,QAAQ,EAAEnB,QAAQ,CAACoB,QAAQ;QAC3BC,IAAI,EAAErB,QAAQ,CAACsB,IAAI;QAAE;QACrBC,QAAQ,EAAEvB,QAAQ,CAACwB,QAAQ;QAC3BC,aAAa,EAAEzB,QAAQ,CAAC0B;MAC5B,CAAC;MAED/B,YAAY,CAACgC,OAAO,CAAC,OAAO,EAAElB,QAAQ,CAAC;MACvCd,YAAY,CAACgC,OAAO,CAAC,MAAM,EAAE1B,IAAI,CAAC2B,SAAS,CAACjB,cAAc,CAAC,CAAC;MAC5DjB,QAAQ,CAACe,QAAQ,CAAC;MAClBnB,OAAO,CAACqB,cAAc,CAAC;MAEvB,OAAO;QAAEkB,OAAO,EAAE,IAAI;QAAExC,IAAI,EAAEsB;MAAe,CAAC;IAClD,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAA2B,eAAA,EAAAC,oBAAA;MACZ,OAAO;QACHF,OAAO,EAAE,KAAK;QACd1B,KAAK,EAAE,EAAA2B,eAAA,GAAA3B,KAAK,CAACK,QAAQ,cAAAsB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBpB,IAAI,cAAAqB,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI7B,KAAK,CAAC6B,OAAO,IAAI;MAC7D,CAAC;IACL;EACJ,CAAC;EAED,MAAMC,QAAQ,GAAG,MAAOjC,QAAQ,IAAK;IACjC,IAAI;MACA,MAAMQ,QAAQ,GAAG,MAAM9B,WAAW,CAACuD,QAAQ,CAACjC,QAAQ,CAAC;MACrD,MAAM;QAAEP,KAAK,EAAEgB,QAAQ;QAAEpB,IAAI,EAAE6C;MAAQ,CAAC,GAAG1B,QAAQ;MAEnDb,YAAY,CAACgC,OAAO,CAAC,OAAO,EAAElB,QAAQ,CAAC;MACvCf,QAAQ,CAACe,QAAQ,CAAC;MAClBnB,OAAO,CAAC4C,OAAO,CAAC;MAEhB,OAAO;QAAEL,OAAO,EAAE,IAAI;QAAExC,IAAI,EAAE6C;MAAQ,CAAC;IAC3C,CAAC,CAAC,OAAO/B,KAAK,EAAE;MAAA,IAAAgC,gBAAA,EAAAC,qBAAA;MACZC,OAAO,CAAClC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,OAAO;QACH0B,OAAO,EAAE,KAAK;QACd1B,KAAK,EAAE,EAAAgC,gBAAA,GAAAhC,KAAK,CAACK,QAAQ,cAAA2B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzB,IAAI,cAAA0B,qBAAA,uBAApBA,qBAAA,CAAsBJ,OAAO,KAAI;MAC5C,CAAC;IACL;EACJ,CAAC;EAED,MAAMM,MAAM,GAAGA,CAAA,KAAM;IACjB3C,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;IAChCT,YAAY,CAACS,UAAU,CAAC,MAAM,CAAC;IAC/BV,QAAQ,CAAC,IAAI,CAAC;IACdJ,OAAO,CAAC,IAAI,CAAC;EACjB,CAAC;EAED,MAAMiD,aAAa,GAAG,MAAOC,WAAW,IAAK;IACzC,IAAI;MACA,MAAMhC,QAAQ,GAAG,MAAM9B,WAAW,CAAC6D,aAAa,CAACC,WAAW,CAAC;MAC7DlD,OAAO,CAACkB,QAAQ,CAACnB,IAAI,CAAC;MACtB,OAAO;QAAEwC,OAAO,EAAE,IAAI;QAAExC,IAAI,EAAEmB,QAAQ,CAACnB;MAAK,CAAC;IACjD,CAAC,CAAC,OAAOc,KAAK,EAAE;MAAA,IAAAsC,gBAAA,EAAAC,qBAAA;MACZL,OAAO,CAAClC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAO;QACH0B,OAAO,EAAE,KAAK;QACd1B,KAAK,EAAE,EAAAsC,gBAAA,GAAAtC,KAAK,CAACK,QAAQ,cAAAiC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB/B,IAAI,cAAAgC,qBAAA,uBAApBA,qBAAA,CAAsBV,OAAO,KAAI;MAC5C,CAAC;IACL;EACJ,CAAC;EAED,MAAMW,KAAK,GAAG;IACVtD,IAAI;IACJI,KAAK;IACLF,OAAO;IACPc,KAAK;IACL4B,QAAQ;IACRK,MAAM;IACNC,aAAa;IACbK,eAAe,EAAE,CAAC,CAACvD;EACvB,CAAC;EAED,oBACIT,OAAA,CAACC,WAAW,CAACgE,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAAxD,QAAA,EAC9BA;EAAQ;IAAA2D,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAE/B,CAAC;AAAC7D,GAAA,CApHWF,YAAY;AAAAgE,EAAA,GAAZhE,YAAY;AAAA,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}