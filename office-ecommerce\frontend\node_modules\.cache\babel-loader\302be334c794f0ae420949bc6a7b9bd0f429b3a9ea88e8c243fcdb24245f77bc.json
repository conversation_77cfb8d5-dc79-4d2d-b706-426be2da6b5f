{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\pages\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../hooks/useAuth';\nimport { usePermissions } from '../hooks/usePermissions';\nimport '../styles/pages.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  var _location$state, _location$state$from;\n  const [isLogin, setIsLogin] = useState(true);\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    firstName: '',\n    lastName: '',\n    phone: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const {\n    login,\n    register\n  } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // Get the intended destination from location state\n  const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || '/';\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const getRedirectPath = (user, intendedPath) => {\n    // If user was trying to access admin area, check permissions\n    if (intendedPath.startsWith('/admin')) {\n      return user.role === 'Admin' || user.role === 'Employee' ? intendedPath : '/';\n    }\n\n    // Default redirect based on role\n    if (intendedPath === '/') {\n      return user.role === 'Admin' || user.role === 'Employee' ? '/admin' : '/';\n    }\n    return intendedPath;\n  };\n  const handleLogin = async () => {\n    const result = await login(formData.email, formData.password);\n    if (result.success) {\n      const redirectTo = getRedirectPath(result.user, from);\n      navigate(redirectTo, {\n        replace: true\n      });\n    } else {\n      setError(result.error);\n    }\n  };\n  const handleRegistration = async () => {\n    const result = await register(formData);\n    if (result.success) {\n      navigate('/', {\n        replace: true\n      });\n    } else {\n      setError(result.error);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      if (isLogin) {\n        await handleLogin();\n      } else {\n        await handleRegistration();\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-page-modern\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-hero\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-hero-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"auth-brand\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"brand-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"48\",\n                height: \"48\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M3 21h18\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 93,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M5 21V7l8-4v18\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 94,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M19 21V11l-6-4\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 95,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M9 9v.01\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 96,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M9 12v.01\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M9 15v.01\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              children: \"DesignXcel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Premium Office Furniture Solutions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-main\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-layout\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"auth-form-container\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"auth-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"auth-header-modern\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"auth-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"32\",\n                    height: \"32\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\",\n                      stroke: \"#808080\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 118,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"7\",\n                      r: \"4\",\n                      stroke: \"#808080\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 119,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 117,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  children: isLogin ? 'Welcome Back' : 'Create Account'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"auth-subtitle\",\n                  children: isLogin ? 'Sign in to access your account and continue shopping' : 'Join us for a personalized shopping experience'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"header-underline\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"auth-form-modern\",\n                children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"error-message-modern\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"10\",\n                      stroke: \"#e74c3c\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 136,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M15 9l-6 6M9 9l6 6\",\n                      stroke: \"#e74c3c\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 137,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: error\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 41\n                }, this), !isLogin && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"form-row-modern\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"form-group-modern\",\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        children: \"First Name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 147,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"input-wrapper\",\n                        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                          width: \"18\",\n                          height: \"18\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\",\n                            stroke: \"#808080\",\n                            strokeWidth: \"1.5\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 150,\n                            columnNumber: 61\n                          }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                            cx: \"12\",\n                            cy: \"7\",\n                            r: \"4\",\n                            stroke: \"#808080\",\n                            strokeWidth: \"1.5\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 151,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 149,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                          type: \"text\",\n                          name: \"firstName\",\n                          placeholder: \"Enter your first name\",\n                          value: formData.firstName,\n                          onChange: handleChange,\n                          required: true\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 153,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 148,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 146,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"form-group-modern\",\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        children: \"Last Name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 164,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"input-wrapper\",\n                        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                          width: \"18\",\n                          height: \"18\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\",\n                            stroke: \"#808080\",\n                            strokeWidth: \"1.5\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 167,\n                            columnNumber: 61\n                          }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                            cx: \"12\",\n                            cy: \"7\",\n                            r: \"4\",\n                            stroke: \"#808080\",\n                            strokeWidth: \"1.5\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 168,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 166,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                          type: \"text\",\n                          name: \"lastName\",\n                          placeholder: \"Enter your last name\",\n                          value: formData.lastName,\n                          onChange: handleChange,\n                          required: true\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 170,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 165,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 163,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"form-group-modern\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      children: \"Phone Number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 182,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"input-wrapper\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"18\",\n                        height: \"18\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\",\n                          stroke: \"#808080\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 185,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 184,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"tel\",\n                        name: \"phone\",\n                        placeholder: \"Enter your phone number\",\n                        value: formData.phone,\n                        onChange: handleChange\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 187,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 183,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 181,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group-modern\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Email Address\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"input-wrapper\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"18\",\n                      height: \"18\",\n                      viewBox: \"0 0 24 24\",\n                      fill: \"none\",\n                      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z\",\n                        stroke: \"#808080\",\n                        strokeWidth: \"1.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 203,\n                        columnNumber: 49\n                      }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                        points: \"22,6 12,13 2,6\",\n                        stroke: \"#808080\",\n                        strokeWidth: \"1.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 204,\n                        columnNumber: 49\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 202,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"email\",\n                      name: \"email\",\n                      placeholder: \"Enter your email address\",\n                      value: formData.email,\n                      onChange: handleChange,\n                      required: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 206,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group-modern\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Password\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"input-wrapper\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"18\",\n                      height: \"18\",\n                      viewBox: \"0 0 24 24\",\n                      fill: \"none\",\n                      children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                        x: \"3\",\n                        y: \"11\",\n                        width: \"18\",\n                        height: \"11\",\n                        rx: \"2\",\n                        ry: \"2\",\n                        stroke: \"#808080\",\n                        strokeWidth: \"1.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 221,\n                        columnNumber: 49\n                      }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                        cx: \"12\",\n                        cy: \"16\",\n                        r: \"1\",\n                        fill: \"#808080\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 222,\n                        columnNumber: 49\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M7 11V7a5 5 0 0 1 10 0v4\",\n                        stroke: \"#808080\",\n                        strokeWidth: \"1.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 223,\n                        columnNumber: 49\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 220,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"password\",\n                      name: \"password\",\n                      placeholder: \"Enter your password\",\n                      value: formData.password,\n                      onChange: handleChange,\n                      required: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 225,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 37\n                }, this), isLogin && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-options-modern\",\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/forgot-password\",\n                    className: \"forgot-link-modern\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"16\",\n                      height: \"16\",\n                      viewBox: \"0 0 24 24\",\n                      fill: \"none\",\n                      children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"#F0B21B\",\n                        strokeWidth: \"1.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 240,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M12 6v6l4 2\",\n                        stroke: \"#F0B21B\",\n                        strokeWidth: \"1.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 241,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 239,\n                      columnNumber: 49\n                    }, this), \"Forgot Password?\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"submit\",\n                  className: \"auth-submit-btn-modern\",\n                  disabled: loading,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"btn-content\",\n                    children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"loading-spinner\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 252,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Please wait...\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 253,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: isLogin ? 'Sign In' : 'Create Account'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 257,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"20\",\n                        height: \"20\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M5 12h14M12 5l7 7-7 7\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"2\",\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 259,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 258,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 33\n              }, this), !isLogin && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"terms-notice-modern\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"terms-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"16\",\n                    height: \"16\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12l2 2 4-4\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 271,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"1.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 272,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"By creating an account, you agree to our\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/terms\",\n                    children: \"Terms of Service\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 45\n                  }, this), \" and\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/privacy\",\n                    children: \"Privacy Policy\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"auth-switch-container\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"auth-switch-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"switch-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"64\",\n                  height: \"64\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: isLogin ? /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2M9 7a4 4 0 1 0 8 0 4 4 0 0 0-8 0M22 11l-3-3m0 0l-3 3m3-3h-6\",\n                    stroke: \"#F0B21B\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 45\n                  }, this) : /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4M10 17l5-5-5-5M15 12H3\",\n                    stroke: \"#F0B21B\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: isLogin ? 'New to DesignXcel?' : 'Already have an account?'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: isLogin ? 'Join thousands of satisfied customers who trust us for their office furniture needs. Create an account to unlock exclusive benefits and personalized recommendations.' : 'Welcome back! Sign in to access your saved items, order history, and continue your seamless shopping experience with us.'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"switch-features\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12l2 2 4-4\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 307,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"1.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 308,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: isLogin ? 'Personalized recommendations' : 'Quick checkout process'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12l2 2 4-4\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 314,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"1.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 315,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: isLogin ? 'Exclusive member discounts' : 'Order tracking & history'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12l2 2 4-4\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 321,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"1.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 322,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: isLogin ? 'Priority customer support' : 'Saved favorites & wishlist'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"switch-btn-modern\",\n                onClick: () => {\n                  setIsLogin(!isLogin);\n                  setError('');\n                  setFormData({\n                    email: '',\n                    password: '',\n                    firstName: '',\n                    lastName: '',\n                    phone: ''\n                  });\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: isLogin ? 'Create Account' : 'Sign In'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"20\",\n                  height: \"20\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M5 12h14M12 5l7 7-7 7\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 9\n  }, this);\n};\n_s(Login, \"FcV206aU/wdt35eYJ2M+1KHvxw8=\", false, function () {\n  return [useAuth, useNavigate, useLocation];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "useLocation", "useAuth", "usePermissions", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "_s", "_location$state", "_location$state$from", "is<PERSON>ogin", "setIsLogin", "formData", "setFormData", "email", "password", "firstName", "lastName", "phone", "loading", "setLoading", "error", "setError", "login", "register", "navigate", "location", "from", "state", "pathname", "handleChange", "e", "target", "name", "value", "getRedirectPath", "user", "intendedPath", "startsWith", "role", "handleLogin", "result", "success", "redirectTo", "replace", "handleRegistration", "handleSubmit", "preventDefault", "err", "message", "className", "children", "width", "height", "viewBox", "fill", "d", "stroke", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "cx", "cy", "r", "onSubmit", "type", "placeholder", "onChange", "required", "points", "x", "y", "rx", "ry", "to", "disabled", "strokeLinecap", "strokeLinejoin", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/pages/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\r\nimport { useAuth } from '../hooks/useAuth';\r\nimport { usePermissions } from '../hooks/usePermissions';\r\nimport '../styles/pages.css';\r\n\r\nconst Login = () => {\r\n    const [isLogin, setIsLogin] = useState(true);\r\n    const [formData, setFormData] = useState({\r\n        email: '',\r\n        password: '',\r\n        firstName: '',\r\n        lastName: '',\r\n        phone: ''\r\n    });\r\n    const [loading, setLoading] = useState(false);\r\n    const [error, setError] = useState('');\r\n    \r\n    const { login, register } = useAuth();\r\n    const navigate = useNavigate();\r\n    const location = useLocation();\r\n\r\n    // Get the intended destination from location state\r\n    const from = location.state?.from?.pathname || '/';\r\n\r\n    const handleChange = (e) => {\r\n        setFormData({\r\n            ...formData,\r\n            [e.target.name]: e.target.value\r\n        });\r\n    };\r\n\r\n    const getRedirectPath = (user, intendedPath) => {\r\n        // If user was trying to access admin area, check permissions\r\n        if (intendedPath.startsWith('/admin')) {\r\n            return (user.role === 'Admin' || user.role === 'Employee') ? intendedPath : '/';\r\n        }\r\n\r\n        // Default redirect based on role\r\n        if (intendedPath === '/') {\r\n            return (user.role === 'Admin' || user.role === 'Employee') ? '/admin' : '/';\r\n        }\r\n\r\n        return intendedPath;\r\n    };\r\n\r\n    const handleLogin = async () => {\r\n        const result = await login(formData.email, formData.password);\r\n        if (result.success) {\r\n            const redirectTo = getRedirectPath(result.user, from);\r\n            navigate(redirectTo, { replace: true });\r\n        } else {\r\n            setError(result.error);\r\n        }\r\n    };\r\n\r\n    const handleRegistration = async () => {\r\n        const result = await register(formData);\r\n        if (result.success) {\r\n            navigate('/', { replace: true });\r\n        } else {\r\n            setError(result.error);\r\n        }\r\n    };\r\n\r\n    const handleSubmit = async (e) => {\r\n        e.preventDefault();\r\n        setLoading(true);\r\n        setError('');\r\n\r\n        try {\r\n            if (isLogin) {\r\n                await handleLogin();\r\n            } else {\r\n                await handleRegistration();\r\n            }\r\n        } catch (err) {\r\n            setError(err.message || 'An error occurred');\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"auth-page-modern\">\r\n            {/* Hero Section */}\r\n            <div className=\"auth-hero\">\r\n                <div className=\"container\">\r\n                    <div className=\"auth-hero-content\">\r\n                        <div className=\"auth-brand\">\r\n                            <div className=\"brand-icon\">\r\n                                <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                    <path d=\"M3 21h18\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\r\n                                    <path d=\"M5 21V7l8-4v18\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\r\n                                    <path d=\"M19 21V11l-6-4\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\r\n                                    <path d=\"M9 9v.01\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\r\n                                    <path d=\"M9 12v.01\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\r\n                                    <path d=\"M9 15v.01\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\r\n                                </svg>\r\n                            </div>\r\n                            <h1>DesignXcel</h1>\r\n                            <p>Premium Office Furniture Solutions</p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Main Content */}\r\n            <div className=\"auth-main\">\r\n                <div className=\"container\">\r\n                    <div className=\"auth-layout\">\r\n                        {/* Left Side - Form */}\r\n                        <div className=\"auth-form-container\">\r\n                            <div className=\"auth-card\">\r\n                                <div className=\"auth-header-modern\">\r\n                                    <div className=\"auth-icon\">\r\n                                        <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                            <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\" stroke=\"#808080\" strokeWidth=\"2\"/>\r\n                                            <circle cx=\"12\" cy=\"7\" r=\"4\" stroke=\"#808080\" strokeWidth=\"2\"/>\r\n                                        </svg>\r\n                                    </div>\r\n                                    <h2>{isLogin ? 'Welcome Back' : 'Create Account'}</h2>\r\n                                    <p className=\"auth-subtitle\">\r\n                                        {isLogin\r\n                                            ? 'Sign in to access your account and continue shopping'\r\n                                            : 'Join us for a personalized shopping experience'\r\n                                        }\r\n                                    </p>\r\n                                    <div className=\"header-underline\"></div>\r\n                                </div>\r\n\r\n                                <form onSubmit={handleSubmit} className=\"auth-form-modern\">\r\n                                    {error && (\r\n                                        <div className=\"error-message-modern\">\r\n                                            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                                <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"#e74c3c\" strokeWidth=\"2\"/>\r\n                                                <path d=\"M15 9l-6 6M9 9l6 6\" stroke=\"#e74c3c\" strokeWidth=\"2\"/>\r\n                                            </svg>\r\n                                            <span>{error}</span>\r\n                                        </div>\r\n                                    )}\r\n\r\n                                    {!isLogin && (\r\n                                        <>\r\n                                            <div className=\"form-row-modern\">\r\n                                                <div className=\"form-group-modern\">\r\n                                                    <label>First Name</label>\r\n                                                    <div className=\"input-wrapper\">\r\n                                                        <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                                            <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\" stroke=\"#808080\" strokeWidth=\"1.5\"/>\r\n                                                            <circle cx=\"12\" cy=\"7\" r=\"4\" stroke=\"#808080\" strokeWidth=\"1.5\"/>\r\n                                                        </svg>\r\n                                                        <input\r\n                                                            type=\"text\"\r\n                                                            name=\"firstName\"\r\n                                                            placeholder=\"Enter your first name\"\r\n                                                            value={formData.firstName}\r\n                                                            onChange={handleChange}\r\n                                                            required\r\n                                                        />\r\n                                                    </div>\r\n                                                </div>\r\n                                                <div className=\"form-group-modern\">\r\n                                                    <label>Last Name</label>\r\n                                                    <div className=\"input-wrapper\">\r\n                                                        <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                                            <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\" stroke=\"#808080\" strokeWidth=\"1.5\"/>\r\n                                                            <circle cx=\"12\" cy=\"7\" r=\"4\" stroke=\"#808080\" strokeWidth=\"1.5\"/>\r\n                                                        </svg>\r\n                                                        <input\r\n                                                            type=\"text\"\r\n                                                            name=\"lastName\"\r\n                                                            placeholder=\"Enter your last name\"\r\n                                                            value={formData.lastName}\r\n                                                            onChange={handleChange}\r\n                                                            required\r\n                                                        />\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"form-group-modern\">\r\n                                                <label>Phone Number</label>\r\n                                                <div className=\"input-wrapper\">\r\n                                                    <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                                        <path d=\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\" stroke=\"#808080\" strokeWidth=\"1.5\"/>\r\n                                                    </svg>\r\n                                                    <input\r\n                                                        type=\"tel\"\r\n                                                        name=\"phone\"\r\n                                                        placeholder=\"Enter your phone number\"\r\n                                                        value={formData.phone}\r\n                                                        onChange={handleChange}\r\n                                                    />\r\n                                                </div>\r\n                                            </div>\r\n                                        </>\r\n                                    )}\r\n\r\n                                    <div className=\"form-group-modern\">\r\n                                        <label>Email Address</label>\r\n                                        <div className=\"input-wrapper\">\r\n                                            <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                                <path d=\"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z\" stroke=\"#808080\" strokeWidth=\"1.5\"/>\r\n                                                <polyline points=\"22,6 12,13 2,6\" stroke=\"#808080\" strokeWidth=\"1.5\"/>\r\n                                            </svg>\r\n                                            <input\r\n                                                type=\"email\"\r\n                                                name=\"email\"\r\n                                                placeholder=\"Enter your email address\"\r\n                                                value={formData.email}\r\n                                                onChange={handleChange}\r\n                                                required\r\n                                            />\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    <div className=\"form-group-modern\">\r\n                                        <label>Password</label>\r\n                                        <div className=\"input-wrapper\">\r\n                                            <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                                <rect x=\"3\" y=\"11\" width=\"18\" height=\"11\" rx=\"2\" ry=\"2\" stroke=\"#808080\" strokeWidth=\"1.5\"/>\r\n                                                <circle cx=\"12\" cy=\"16\" r=\"1\" fill=\"#808080\"/>\r\n                                                <path d=\"M7 11V7a5 5 0 0 1 10 0v4\" stroke=\"#808080\" strokeWidth=\"1.5\"/>\r\n                                            </svg>\r\n                                            <input\r\n                                                type=\"password\"\r\n                                                name=\"password\"\r\n                                                placeholder=\"Enter your password\"\r\n                                                value={formData.password}\r\n                                                onChange={handleChange}\r\n                                                required\r\n                                            />\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    {isLogin && (\r\n                                        <div className=\"form-options-modern\">\r\n                                            <Link to=\"/forgot-password\" className=\"forgot-link-modern\">\r\n                                                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                                    <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"#F0B21B\" strokeWidth=\"1.5\"/>\r\n                                                    <path d=\"M12 6v6l4 2\" stroke=\"#F0B21B\" strokeWidth=\"1.5\"/>\r\n                                                </svg>\r\n                                                Forgot Password?\r\n                                            </Link>\r\n                                        </div>\r\n                                    )}\r\n\r\n                                    <button type=\"submit\" className=\"auth-submit-btn-modern\" disabled={loading}>\r\n                                        <div className=\"btn-content\">\r\n                                            {loading ? (\r\n                                                <>\r\n                                                    <div className=\"loading-spinner\"></div>\r\n                                                    <span>Please wait...</span>\r\n                                                </>\r\n                                            ) : (\r\n                                                <>\r\n                                                    <span>{isLogin ? 'Sign In' : 'Create Account'}</span>\r\n                                                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                                        <path d=\"M5 12h14M12 5l7 7-7 7\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                                                    </svg>\r\n                                                </>\r\n                                            )}\r\n                                        </div>\r\n                                    </button>\r\n                                </form>\r\n\r\n                                {!isLogin && (\r\n                                    <div className=\"terms-notice-modern\">\r\n                                        <div className=\"terms-icon\">\r\n                                            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                                <path d=\"M9 12l2 2 4-4\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                                                <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#F0B21B\" strokeWidth=\"1.5\"/>\r\n                                            </svg>\r\n                                        </div>\r\n                                        <p>\r\n                                            By creating an account, you agree to our{' '}\r\n                                            <Link to=\"/terms\">Terms of Service</Link> and{' '}\r\n                                            <Link to=\"/privacy\">Privacy Policy</Link>\r\n                                        </p>\r\n                                    </div>\r\n                                )}\r\n                            </div>\r\n                        </div>\r\n\r\n                        {/* Right Side - Switch */}\r\n                        <div className=\"auth-switch-container\">\r\n                            <div className=\"auth-switch-card\">\r\n                                <div className=\"switch-icon\">\r\n                                    <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                        {isLogin ? (\r\n                                            <path d=\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2M9 7a4 4 0 1 0 8 0 4 4 0 0 0-8 0M22 11l-3-3m0 0l-3 3m3-3h-6\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\r\n                                        ) : (\r\n                                            <path d=\"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4M10 17l5-5-5-5M15 12H3\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\r\n                                        )}\r\n                                    </svg>\r\n                                </div>\r\n                                <h3>{isLogin ? 'New to DesignXcel?' : 'Already have an account?'}</h3>\r\n                                <p>\r\n                                    {isLogin\r\n                                        ? 'Join thousands of satisfied customers who trust us for their office furniture needs. Create an account to unlock exclusive benefits and personalized recommendations.'\r\n                                        : 'Welcome back! Sign in to access your saved items, order history, and continue your seamless shopping experience with us.'\r\n                                    }\r\n                                </p>\r\n                                <div className=\"switch-features\">\r\n                                    <div className=\"feature-item\">\r\n                                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                            <path d=\"M9 12l2 2 4-4\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                                            <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#F0B21B\" strokeWidth=\"1.5\"/>\r\n                                        </svg>\r\n                                        <span>{isLogin ? 'Personalized recommendations' : 'Quick checkout process'}</span>\r\n                                    </div>\r\n                                    <div className=\"feature-item\">\r\n                                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                            <path d=\"M9 12l2 2 4-4\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                                            <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#F0B21B\" strokeWidth=\"1.5\"/>\r\n                                        </svg>\r\n                                        <span>{isLogin ? 'Exclusive member discounts' : 'Order tracking & history'}</span>\r\n                                    </div>\r\n                                    <div className=\"feature-item\">\r\n                                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                            <path d=\"M9 12l2 2 4-4\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                                            <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#F0B21B\" strokeWidth=\"1.5\"/>\r\n                                        </svg>\r\n                                        <span>{isLogin ? 'Priority customer support' : 'Saved favorites & wishlist'}</span>\r\n                                    </div>\r\n                                </div>\r\n                                <button\r\n                                    className=\"switch-btn-modern\"\r\n                                    onClick={() => {\r\n                                        setIsLogin(!isLogin);\r\n                                        setError('');\r\n                                        setFormData({\r\n                                            email: '',\r\n                                            password: '',\r\n                                            firstName: '',\r\n                                            lastName: '',\r\n                                            phone: ''\r\n                                        });\r\n                                    }}\r\n                                >\r\n                                    <span>{isLogin ? 'Create Account' : 'Sign In'}</span>\r\n                                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                        <path d=\"M5 12h14M12 5l7 7-7 7\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                                    </svg>\r\n                                </button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Login;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,cAAc,QAAQ,yBAAyB;AACxD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7B,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,oBAAA;EAChB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACrCkB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM;IAAE2B,KAAK;IAAEC;EAAS,CAAC,GAAGxB,OAAO,CAAC,CAAC;EACrC,MAAMyB,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM4B,QAAQ,GAAG3B,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM4B,IAAI,GAAG,EAAAnB,eAAA,GAAAkB,QAAQ,CAACE,KAAK,cAAApB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBmB,IAAI,cAAAlB,oBAAA,uBAApBA,oBAAA,CAAsBoB,QAAQ,KAAI,GAAG;EAElD,MAAMC,YAAY,GAAIC,CAAC,IAAK;IACxBlB,WAAW,CAAC;MACR,GAAGD,QAAQ;MACX,CAACmB,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC9B,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,eAAe,GAAGA,CAACC,IAAI,EAAEC,YAAY,KAAK;IAC5C;IACA,IAAIA,YAAY,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MACnC,OAAQF,IAAI,CAACG,IAAI,KAAK,OAAO,IAAIH,IAAI,CAACG,IAAI,KAAK,UAAU,GAAIF,YAAY,GAAG,GAAG;IACnF;;IAEA;IACA,IAAIA,YAAY,KAAK,GAAG,EAAE;MACtB,OAAQD,IAAI,CAACG,IAAI,KAAK,OAAO,IAAIH,IAAI,CAACG,IAAI,KAAK,UAAU,GAAI,QAAQ,GAAG,GAAG;IAC/E;IAEA,OAAOF,YAAY;EACvB,CAAC;EAED,MAAMG,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,MAAMC,MAAM,GAAG,MAAMlB,KAAK,CAACX,QAAQ,CAACE,KAAK,EAAEF,QAAQ,CAACG,QAAQ,CAAC;IAC7D,IAAI0B,MAAM,CAACC,OAAO,EAAE;MAChB,MAAMC,UAAU,GAAGR,eAAe,CAACM,MAAM,CAACL,IAAI,EAAET,IAAI,CAAC;MACrDF,QAAQ,CAACkB,UAAU,EAAE;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;IAC3C,CAAC,MAAM;MACHtB,QAAQ,CAACmB,MAAM,CAACpB,KAAK,CAAC;IAC1B;EACJ,CAAC;EAED,MAAMwB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACnC,MAAMJ,MAAM,GAAG,MAAMjB,QAAQ,CAACZ,QAAQ,CAAC;IACvC,IAAI6B,MAAM,CAACC,OAAO,EAAE;MAChBjB,QAAQ,CAAC,GAAG,EAAE;QAAEmB,OAAO,EAAE;MAAK,CAAC,CAAC;IACpC,CAAC,MAAM;MACHtB,QAAQ,CAACmB,MAAM,CAACpB,KAAK,CAAC;IAC1B;EACJ,CAAC;EAED,MAAMyB,YAAY,GAAG,MAAOf,CAAC,IAAK;IAC9BA,CAAC,CAACgB,cAAc,CAAC,CAAC;IAClB3B,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACA,IAAIZ,OAAO,EAAE;QACT,MAAM8B,WAAW,CAAC,CAAC;MACvB,CAAC,MAAM;QACH,MAAMK,kBAAkB,CAAC,CAAC;MAC9B;IACJ,CAAC,CAAC,OAAOG,GAAG,EAAE;MACV1B,QAAQ,CAAC0B,GAAG,CAACC,OAAO,IAAI,mBAAmB,CAAC;IAChD,CAAC,SAAS;MACN7B,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,oBACIjB,OAAA;IAAK+C,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAE7BhD,OAAA;MAAK+C,SAAS,EAAC,WAAW;MAAAC,QAAA,eACtBhD,OAAA;QAAK+C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtBhD,OAAA;UAAK+C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAC9BhD,OAAA;YAAK+C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBhD,OAAA;cAAK+C,SAAS,EAAC,YAAY;cAAAC,QAAA,eACvBhD,OAAA;gBAAKiD,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAJ,QAAA,gBACvDhD,OAAA;kBAAMqD,CAAC,EAAC,UAAU;kBAACC,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACrD3D,OAAA;kBAAMqD,CAAC,EAAC,gBAAgB;kBAACC,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC3D3D,OAAA;kBAAMqD,CAAC,EAAC,gBAAgB;kBAACC,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC3D3D,OAAA;kBAAMqD,CAAC,EAAC,UAAU;kBAACC,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACrD3D,OAAA;kBAAMqD,CAAC,EAAC,WAAW;kBAACC,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACtD3D,OAAA;kBAAMqD,CAAC,EAAC,WAAW;kBAACC,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACN3D,OAAA;cAAAgD,QAAA,EAAI;YAAU;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnB3D,OAAA;cAAAgD,QAAA,EAAG;YAAkC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN3D,OAAA;MAAK+C,SAAS,EAAC,WAAW;MAAAC,QAAA,eACtBhD,OAAA;QAAK+C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtBhD,OAAA;UAAK+C,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAExBhD,OAAA;YAAK+C,SAAS,EAAC,qBAAqB;YAAAC,QAAA,eAChChD,OAAA;cAAK+C,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtBhD,OAAA;gBAAK+C,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBAC/BhD,OAAA;kBAAK+C,SAAS,EAAC,WAAW;kBAAAC,QAAA,eACtBhD,OAAA;oBAAKiD,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAJ,QAAA,gBACvDhD,OAAA;sBAAMqD,CAAC,EAAC,2CAA2C;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACtF3D,OAAA;sBAAQ4D,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,GAAG;sBAACC,CAAC,EAAC,GAAG;sBAACR,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACN3D,OAAA;kBAAAgD,QAAA,EAAKzC,OAAO,GAAG,cAAc,GAAG;gBAAgB;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtD3D,OAAA;kBAAG+C,SAAS,EAAC,eAAe;kBAAAC,QAAA,EACvBzC,OAAO,GACF,sDAAsD,GACtD;gBAAgD;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEvD,CAAC,eACJ3D,OAAA;kBAAK+C,SAAS,EAAC;gBAAkB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eAEN3D,OAAA;gBAAM+D,QAAQ,EAAEpB,YAAa;gBAACI,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,GACrD9B,KAAK,iBACFlB,OAAA;kBAAK+C,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACjChD,OAAA;oBAAKiD,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAJ,QAAA,gBACvDhD,OAAA;sBAAQ4D,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,IAAI;sBAACR,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACjE3D,OAAA;sBAAMqD,CAAC,EAAC,oBAAoB;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC,eACN3D,OAAA;oBAAAgD,QAAA,EAAO9B;kBAAK;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CACR,EAEA,CAACpD,OAAO,iBACLP,OAAA,CAAAE,SAAA;kBAAA8C,QAAA,gBACIhD,OAAA;oBAAK+C,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBAC5BhD,OAAA;sBAAK+C,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAC9BhD,OAAA;wBAAAgD,QAAA,EAAO;sBAAU;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACzB3D,OAAA;wBAAK+C,SAAS,EAAC,eAAe;wBAAAC,QAAA,gBAC1BhD,OAAA;0BAAKiD,KAAK,EAAC,IAAI;0BAACC,MAAM,EAAC,IAAI;0BAACC,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,MAAM;0BAAAJ,QAAA,gBACvDhD,OAAA;4BAAMqD,CAAC,EAAC,2CAA2C;4BAACC,MAAM,EAAC,SAAS;4BAACC,WAAW,EAAC;0BAAK;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC,CAAC,eACxF3D,OAAA;4BAAQ4D,EAAE,EAAC,IAAI;4BAACC,EAAE,EAAC,GAAG;4BAACC,CAAC,EAAC,GAAG;4BAACR,MAAM,EAAC,SAAS;4BAACC,WAAW,EAAC;0BAAK;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChE,CAAC,eACN3D,OAAA;0BACIgE,IAAI,EAAC,MAAM;0BACXlC,IAAI,EAAC,WAAW;0BAChBmC,WAAW,EAAC,uBAAuB;0BACnClC,KAAK,EAAEtB,QAAQ,CAACI,SAAU;0BAC1BqD,QAAQ,EAAEvC,YAAa;0BACvBwC,QAAQ;wBAAA;0BAAAX,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACX,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACN3D,OAAA;sBAAK+C,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAC9BhD,OAAA;wBAAAgD,QAAA,EAAO;sBAAS;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACxB3D,OAAA;wBAAK+C,SAAS,EAAC,eAAe;wBAAAC,QAAA,gBAC1BhD,OAAA;0BAAKiD,KAAK,EAAC,IAAI;0BAACC,MAAM,EAAC,IAAI;0BAACC,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,MAAM;0BAAAJ,QAAA,gBACvDhD,OAAA;4BAAMqD,CAAC,EAAC,2CAA2C;4BAACC,MAAM,EAAC,SAAS;4BAACC,WAAW,EAAC;0BAAK;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC,CAAC,eACxF3D,OAAA;4BAAQ4D,EAAE,EAAC,IAAI;4BAACC,EAAE,EAAC,GAAG;4BAACC,CAAC,EAAC,GAAG;4BAACR,MAAM,EAAC,SAAS;4BAACC,WAAW,EAAC;0BAAK;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChE,CAAC,eACN3D,OAAA;0BACIgE,IAAI,EAAC,MAAM;0BACXlC,IAAI,EAAC,UAAU;0BACfmC,WAAW,EAAC,sBAAsB;0BAClClC,KAAK,EAAEtB,QAAQ,CAACK,QAAS;0BACzBoD,QAAQ,EAAEvC,YAAa;0BACvBwC,QAAQ;wBAAA;0BAAAX,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACX,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACN3D,OAAA;oBAAK+C,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC9BhD,OAAA;sBAAAgD,QAAA,EAAO;oBAAY;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC3B3D,OAAA;sBAAK+C,SAAS,EAAC,eAAe;sBAAAC,QAAA,gBAC1BhD,OAAA;wBAAKiD,KAAK,EAAC,IAAI;wBAACC,MAAM,EAAC,IAAI;wBAACC,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAJ,QAAA,eACvDhD,OAAA;0BAAMqD,CAAC,EAAC,+RAA+R;0BAACC,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC;wBAAK;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3U,CAAC,eACN3D,OAAA;wBACIgE,IAAI,EAAC,KAAK;wBACVlC,IAAI,EAAC,OAAO;wBACZmC,WAAW,EAAC,yBAAyB;wBACrClC,KAAK,EAAEtB,QAAQ,CAACM,KAAM;wBACtBmD,QAAQ,EAAEvC;sBAAa;wBAAA6B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA,eACR,CACL,eAED3D,OAAA;kBAAK+C,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9BhD,OAAA;oBAAAgD,QAAA,EAAO;kBAAa;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5B3D,OAAA;oBAAK+C,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC1BhD,OAAA;sBAAKiD,KAAK,EAAC,IAAI;sBAACC,MAAM,EAAC,IAAI;sBAACC,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,MAAM;sBAAAJ,QAAA,gBACvDhD,OAAA;wBAAMqD,CAAC,EAAC,6EAA6E;wBAACC,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eAC1H3D,OAAA;wBAAUoE,MAAM,EAAC,gBAAgB;wBAACd,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrE,CAAC,eACN3D,OAAA;sBACIgE,IAAI,EAAC,OAAO;sBACZlC,IAAI,EAAC,OAAO;sBACZmC,WAAW,EAAC,0BAA0B;sBACtClC,KAAK,EAAEtB,QAAQ,CAACE,KAAM;sBACtBuD,QAAQ,EAAEvC,YAAa;sBACvBwC,QAAQ;oBAAA;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eAEN3D,OAAA;kBAAK+C,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9BhD,OAAA;oBAAAgD,QAAA,EAAO;kBAAQ;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACvB3D,OAAA;oBAAK+C,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC1BhD,OAAA;sBAAKiD,KAAK,EAAC,IAAI;sBAACC,MAAM,EAAC,IAAI;sBAACC,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,MAAM;sBAAAJ,QAAA,gBACvDhD,OAAA;wBAAMqE,CAAC,EAAC,GAAG;wBAACC,CAAC,EAAC,IAAI;wBAACrB,KAAK,EAAC,IAAI;wBAACC,MAAM,EAAC,IAAI;wBAACqB,EAAE,EAAC,GAAG;wBAACC,EAAE,EAAC,GAAG;wBAAClB,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eAC5F3D,OAAA;wBAAQ4D,EAAE,EAAC,IAAI;wBAACC,EAAE,EAAC,IAAI;wBAACC,CAAC,EAAC,GAAG;wBAACV,IAAI,EAAC;sBAAS;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eAC9C3D,OAAA;wBAAMqD,CAAC,EAAC,0BAA0B;wBAACC,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC,eACN3D,OAAA;sBACIgE,IAAI,EAAC,UAAU;sBACflC,IAAI,EAAC,UAAU;sBACfmC,WAAW,EAAC,qBAAqB;sBACjClC,KAAK,EAAEtB,QAAQ,CAACG,QAAS;sBACzBsD,QAAQ,EAAEvC,YAAa;sBACvBwC,QAAQ;oBAAA;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,EAELpD,OAAO,iBACJP,OAAA;kBAAK+C,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,eAChChD,OAAA,CAACN,IAAI;oBAAC+E,EAAE,EAAC,kBAAkB;oBAAC1B,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,gBACtDhD,OAAA;sBAAKiD,KAAK,EAAC,IAAI;sBAACC,MAAM,EAAC,IAAI;sBAACC,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,MAAM;sBAAAJ,QAAA,gBACvDhD,OAAA;wBAAQ4D,EAAE,EAAC,IAAI;wBAACC,EAAE,EAAC,IAAI;wBAACC,CAAC,EAAC,IAAI;wBAACR,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eACnE3D,OAAA;wBAAMqD,CAAC,EAAC,aAAa;wBAACC,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzD,CAAC,oBAEV;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CACR,eAED3D,OAAA;kBAAQgE,IAAI,EAAC,QAAQ;kBAACjB,SAAS,EAAC,wBAAwB;kBAAC2B,QAAQ,EAAE1D,OAAQ;kBAAAgC,QAAA,eACvEhD,OAAA;oBAAK+C,SAAS,EAAC,aAAa;oBAAAC,QAAA,EACvBhC,OAAO,gBACJhB,OAAA,CAAAE,SAAA;sBAAA8C,QAAA,gBACIhD,OAAA;wBAAK+C,SAAS,EAAC;sBAAiB;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACvC3D,OAAA;wBAAAgD,QAAA,EAAM;sBAAc;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA,eAC7B,CAAC,gBAEH3D,OAAA,CAAAE,SAAA;sBAAA8C,QAAA,gBACIhD,OAAA;wBAAAgD,QAAA,EAAOzC,OAAO,GAAG,SAAS,GAAG;sBAAgB;wBAAAiD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACrD3D,OAAA;wBAAKiD,KAAK,EAAC,IAAI;wBAACC,MAAM,EAAC,IAAI;wBAACC,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAJ,QAAA,eACvDhD,OAAA;0BAAMqD,CAAC,EAAC,uBAAuB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,GAAG;0BAACoB,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC;wBAAO;0BAAApB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnH,CAAC;oBAAA,eACR;kBACL;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,EAEN,CAACpD,OAAO,iBACLP,OAAA;gBAAK+C,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAChChD,OAAA;kBAAK+C,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACvBhD,OAAA;oBAAKiD,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAJ,QAAA,gBACvDhD,OAAA;sBAAMqD,CAAC,EAAC,eAAe;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACoB,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACvG3D,OAAA;sBAAQ4D,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACR,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACN3D,OAAA;kBAAAgD,QAAA,GAAG,0CACyC,EAAC,GAAG,eAC5ChD,OAAA,CAACN,IAAI;oBAAC+E,EAAE,EAAC,QAAQ;oBAAAzB,QAAA,EAAC;kBAAgB;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,QAAI,EAAC,GAAG,eACjD3D,OAAA,CAACN,IAAI;oBAAC+E,EAAE,EAAC,UAAU;oBAAAzB,QAAA,EAAC;kBAAc;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACR;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGN3D,OAAA;YAAK+C,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eAClChD,OAAA;cAAK+C,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC7BhD,OAAA;gBAAK+C,SAAS,EAAC,aAAa;gBAAAC,QAAA,eACxBhD,OAAA;kBAAKiD,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAAJ,QAAA,EACtDzC,OAAO,gBACJP,OAAA;oBAAMqD,CAAC,EAAC,sGAAsG;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,gBAEjJ3D,OAAA;oBAAMqD,CAAC,EAAC,iEAAiE;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAC9G;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACN3D,OAAA;gBAAAgD,QAAA,EAAKzC,OAAO,GAAG,oBAAoB,GAAG;cAA0B;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtE3D,OAAA;gBAAAgD,QAAA,EACKzC,OAAO,GACF,uKAAuK,GACvK;cAA0H;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEjI,CAAC,eACJ3D,OAAA;gBAAK+C,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC5BhD,OAAA;kBAAK+C,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACzBhD,OAAA;oBAAKiD,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAJ,QAAA,gBACvDhD,OAAA;sBAAMqD,CAAC,EAAC,eAAe;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACoB,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACvG3D,OAAA;sBAAQ4D,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACR,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC,eACN3D,OAAA;oBAAAgD,QAAA,EAAOzC,OAAO,GAAG,8BAA8B,GAAG;kBAAwB;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACN3D,OAAA;kBAAK+C,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACzBhD,OAAA;oBAAKiD,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAJ,QAAA,gBACvDhD,OAAA;sBAAMqD,CAAC,EAAC,eAAe;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACoB,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACvG3D,OAAA;sBAAQ4D,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACR,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC,eACN3D,OAAA;oBAAAgD,QAAA,EAAOzC,OAAO,GAAG,4BAA4B,GAAG;kBAA0B;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACN3D,OAAA;kBAAK+C,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACzBhD,OAAA;oBAAKiD,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAJ,QAAA,gBACvDhD,OAAA;sBAAMqD,CAAC,EAAC,eAAe;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACoB,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACvG3D,OAAA;sBAAQ4D,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACR,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC,eACN3D,OAAA;oBAAAgD,QAAA,EAAOzC,OAAO,GAAG,2BAA2B,GAAG;kBAA4B;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACN3D,OAAA;gBACI+C,SAAS,EAAC,mBAAmB;gBAC7B8B,OAAO,EAAEA,CAAA,KAAM;kBACXrE,UAAU,CAAC,CAACD,OAAO,CAAC;kBACpBY,QAAQ,CAAC,EAAE,CAAC;kBACZT,WAAW,CAAC;oBACRC,KAAK,EAAE,EAAE;oBACTC,QAAQ,EAAE,EAAE;oBACZC,SAAS,EAAE,EAAE;oBACbC,QAAQ,EAAE,EAAE;oBACZC,KAAK,EAAE;kBACX,CAAC,CAAC;gBACN,CAAE;gBAAAiC,QAAA,gBAEFhD,OAAA;kBAAAgD,QAAA,EAAOzC,OAAO,GAAG,gBAAgB,GAAG;gBAAS;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrD3D,OAAA;kBAAKiD,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAAJ,QAAA,eACvDhD,OAAA;oBAAMqD,CAAC,EAAC,uBAAuB;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACoB,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACvD,EAAA,CA1VID,KAAK;EAAA,QAYqBN,OAAO,EAClBF,WAAW,EACXC,WAAW;AAAA;AAAAkF,EAAA,GAd1B3E,KAAK;AA4VX,eAAeA,KAAK;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}