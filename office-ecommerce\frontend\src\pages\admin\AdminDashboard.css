/* Admin Dashboard Styles */
.admin-dashboard {
  display: flex;
  min-height: 100vh;
  background-color: #f8f9fa;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  position: relative;
}

/* Sidebar Styles */
.admin-sidebar {
  width: 280px;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.15);
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  z-index: 1000;
}

.admin-header {
  padding: 1.5rem 1.5rem 2rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(240, 178, 27, 0.02) 100%);
  position: relative;
}

.admin-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 1.5rem;
  right: 1.5rem;
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, #F0B21B 50%, transparent 100%);
  opacity: 0.3;
}

/* Logo Styling */
.admin-logo {
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: center;
  padding: 1.2rem;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(240, 178, 27, 0.2);
  position: relative;
  overflow: hidden;
}

.admin-logo::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 0%, rgba(240, 178, 27, 0.05) 50%, transparent 100%);
  pointer-events: none;
}

.admin-logo-component {
  filter: brightness(1.0) contrast(1.1);
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.admin-logo:hover .admin-logo-component {
  transform: scale(1.02);
  filter: brightness(1.05) contrast(1.15);
}

/* Admin Logo specific optimizations */
.admin-logo .design-excellence-logo.admin-optimized {
  drop-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Ensure logo text is crisp and readable */
.admin-logo .design-excellence-logo text {
  text-rendering: optimizeLegibility;
  shape-rendering: crispEdges;
}

.admin-header h2 {
  margin: 0 0 1.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #F0B21B;
  text-align: center;
  letter-spacing: 0.5px;
}

/* Connection Status Styling */
.connection-status {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-icon {
  width: 20px;
  height: 20px;
}

.status-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.admin-user-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 1rem;
  background: rgba(240, 178, 27, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(240, 178, 27, 0.2);
}

.user-name {
  font-weight: 600;
  font-size: 0.95rem;
  color: #F0B21B;
}

.user-role {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* Navigation Styles */
.admin-nav {
  flex: 1;
  padding: 1rem 0;
}

.nav-item {
  width: 100%;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  text-align: left;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.95rem;
  border-left: 3px solid transparent;
  position: relative;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: linear-gradient(90deg, rgba(240, 178, 27, 0.1), rgba(240, 178, 27, 0.05));
  transition: width 0.3s ease;
  z-index: -1;
}

.nav-item:hover::before {
  width: 100%;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.08);
  color: white;
  padding-left: 2rem;
  border-left-color: #F0B21B;
  transform: translateX(4px);
}

.nav-item.active {
  background: rgba(240, 178, 27, 0.15);
  color: #F0B21B;
  border-left-color: #F0B21B;
  font-weight: 600;
  box-shadow: inset 0 0 20px rgba(240, 178, 27, 0.1);
}

.nav-item.active::before {
  width: 100%;
}

.nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.nav-icon-svg {
  width: 20px;
  height: 20px;
  transition: all 0.3s ease;
}

.nav-item:hover .nav-icon-svg {
  transform: scale(1.1);
}

.nav-label {
  font-weight: 500;
  letter-spacing: 0.3px;
}

/* Footer Styles */
.admin-footer {
  padding: 1rem 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.logout-btn {
  width: 100%;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: #e74c3c;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.95rem;
}

.logout-btn:hover {
  background: rgba(231, 76, 60, 0.1);
  padding-left: 2rem;
}

/* Main Content Styles */
.admin-main {
  flex: 1;
  margin-left: 280px;
  min-height: 100vh;
}

.admin-content {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Loading Styles */
.admin-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f8f9fa;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #F0B21B;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Access Denied Styling */
.access-denied {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    text-align: center;
    padding: 2rem;
    background: #f9fafb;
    border-radius: 12px;
    border: 2px dashed #d1d5db;
    margin: 2rem;
}

.access-denied h2 {
    color: #ef4444;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.access-denied p {
    color: #6b7280;
    font-size: 1rem;
    margin: 0.5rem 0;
    line-height: 1.6;
}

.access-denied strong {
    color: #374151;
    font-weight: 600;
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
  .admin-sidebar {
    width: 260px;
  }

  .admin-main {
    margin-left: 260px;
  }
}

@media (max-width: 768px) {
  .admin-dashboard {
    flex-direction: column;
  }

  .admin-sidebar {
    width: 100%;
    position: relative;
    height: auto;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .admin-main {
    margin-left: 0;
  }

  .admin-content {
    padding: 1rem;
  }

  .admin-header {
    padding: 1rem;
    text-align: center;
  }

  .admin-logo {
    margin-bottom: 1rem;
    padding: 1rem;
  }

  .admin-header h2 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
  }

  .connection-status {
    justify-content: center;
    margin-bottom: 1rem;
  }

  .admin-user-info {
    text-align: center;
  }

  .nav-item {
    padding: 0.75rem 1rem;
    justify-content: center;
  }

  .nav-item:hover {
    padding-left: 1rem;
    transform: none;
  }

  .nav-label {
    display: none;
  }

  .nav-icon {
    margin: 0;
  }

  .logout-btn {
    justify-content: center;
  }

  .logout-btn:hover {
    padding-left: 1.5rem;
  }
}

@media (max-width: 480px) {
  .admin-header {
    padding: 0.75rem;
  }

  .admin-header h2 {
    font-size: 1.1rem;
  }

  .admin-logo {
    padding: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .connection-status {
    padding: 0.5rem;
    font-size: 0.8rem;
  }

  .status-icon {
    width: 16px;
    height: 16px;
  }

  .admin-user-info {
    padding: 0.75rem;
    font-size: 0.85rem;
  }

  .nav-item {
    font-size: 0.9rem;
    padding: 0.6rem 1rem;
  }

  .nav-icon-svg {
    width: 18px;
    height: 18px;
  }

  .admin-content {
    padding: 0.75rem;
  }
}

/* Extra Small Mobile (320px+) */
@media (max-width: 320px) {
  .admin-header {
    padding: 0.5rem;
  }

  .admin-header h2 {
    font-size: 1rem;
    margin-bottom: 0.75rem;
  }

  .admin-logo {
    padding: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .connection-status {
    padding: 0.4rem;
    font-size: 0.75rem;
    gap: 0.5rem;
  }

  .status-icon {
    width: 14px;
    height: 14px;
  }

  .admin-user-info {
    padding: 0.5rem;
    font-size: 0.8rem;
  }

  .user-name {
    font-size: 0.8rem;
  }

  .user-role {
    font-size: 0.7rem;
  }

  .nav-item {
    font-size: 0.85rem;
    padding: 0.5rem 0.75rem;
    min-height: 44px;
  }

  .nav-icon-svg {
    width: 16px;
    height: 16px;
  }

  .admin-content {
    padding: 0.5rem;
  }

  .logout-btn {
    font-size: 0.85rem;
    padding: 0.75rem 1rem;
    min-height: 44px;
  }
}

/* Mobile-first navigation improvements */
@media (max-width: 768px) {
  .admin-nav {
    display: flex;
    flex-direction: row;
    overflow-x: auto;
    padding: 0.5rem 0;
    gap: 0.5rem;
  }

  .nav-item {
    flex: 0 0 auto;
    min-width: 60px;
    padding: 0.75rem 0.5rem;
    border-radius: 8px;
    margin: 0 0.25rem;
    border-left: none;
    border-bottom: 3px solid transparent;
  }

  .nav-item.active {
    border-left: none;
    border-bottom-color: #F0B21B;
    background: rgba(240, 178, 27, 0.15);
  }

  .nav-item:hover {
    border-left: none;
    border-bottom-color: #F0B21B;
    padding-left: 0.5rem;
  }
}

/* Card Styles for Admin Components */
.admin-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
}

.admin-card-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

.admin-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

/* Button Styles */
.admin-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.admin-btn-primary {
  background: #F0B21B;
  color: white;
}

.admin-btn-primary:hover {
  background: #d4a017;
  transform: translateY(-1px);
}

.admin-btn-secondary {
  background: #6c757d;
  color: white;
}

.admin-btn-secondary:hover {
  background: #5a6268;
}

.admin-btn-danger {
  background: #e74c3c;
  color: white;
}

.admin-btn-danger:hover {
  background: #c0392b;
}

/* Table Styles */
.admin-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
}

.admin-table th,
.admin-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.admin-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
}

.admin-table tr:hover {
  background: #f8f9fa;
}

/* Form Styles */
.admin-form-group {
  margin-bottom: 1.5rem;
}

.admin-form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #2c3e50;
}

.admin-form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 0.95rem;
  transition: border-color 0.3s ease;
}

.admin-form-input:focus {
  outline: none;
  border-color: #F0B21B;
  box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.1);
}

/* Connection Status Styles */
.connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.status-indicator {
  font-size: 0.8rem;
}

.status-text {
  font-size: 0.8rem;
  color: #bdc3c7;
}

.connection-status .status-text {
  color: #2ecc71;
}

.connection-status.connecting .status-text {
  color: #f39c12;
}

.connection-status.error .status-text {
  color: #e74c3c;
}

/* Notifications Panel Styles */
.notifications-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 350px;
  max-height: 80vh;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  overflow: hidden;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.notifications-header h3 {
  margin: 0;
  font-size: 1rem;
  color: #2c3e50;
}

.clear-all-btn {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.clear-all-btn:hover {
  background: #e9ecef;
}

.notifications-list {
  max-height: 400px;
  overflow-y: auto;
}

.notification {
  display: flex;
  align-items: flex-start;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.notification:hover {
  background: #f8f9fa;
}

.notification:last-child {
  border-bottom: none;
}

.notification.info {
  border-left: 4px solid #3498db;
}

.notification.success {
  border-left: 4px solid #2ecc71;
}

.notification.warning {
  border-left: 4px solid #f39c12;
}

.notification.error {
  border-left: 4px solid #e74c3c;
}

.notification.announcement {
  border-left: 4px solid #9b59b6;
}

.notification-content {
  flex: 1;
}

.notification-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: #2c3e50;
}

.notification-content p {
  margin: 0 0 0.5rem 0;
  font-size: 0.8rem;
  color: #6c757d;
  line-height: 1.4;
}

.notification-time {
  font-size: 0.7rem;
  color: #adb5bd;
}

.notification-close {
  background: none;
  border: none;
  color: #adb5bd;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.notification-close:hover {
  background: #f8f9fa;
  color: #6c757d;
}

/* Mobile Responsive for Notifications */
@media (max-width: 768px) {
  .notifications-panel {
    width: calc(100vw - 40px);
    right: 20px;
    left: 20px;
  }
}
