import React from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation, Navigate } from 'react-router-dom';
import { AuthProvider } from './hooks/useAuth';
import { CartProvider } from './contexts/CartContext';
import { CurrencyProvider } from './contexts/CurrencyContext';
import { LanguageProvider } from './contexts/LanguageContext';
import Header from './components/common/Header';
import Footer from './components/common/Footer';
import Home from './pages/Home';
import Login from './pages/Login';
import ProductCatalog from './pages/ProductCatalog';
import ProductDetail from './pages/ProductDetail';
import Cart from './pages/Cart';
import CheckoutPage from './pages/CheckoutPage';
import OrderSuccessPage from './pages/OrderSuccessPage';
import Payment from './pages/Payment';
import About from './pages/About';
import Gallery from './pages/Gallery';
import ProductCardDemo from './components/demo/ProductCardDemo';
import AdminDashboard from './pages/admin/AdminDashboard';
import Account from './pages/Account';

// Import route protection components
import ProtectedRoute from './components/auth/ProtectedRoute';
import RoleBasedRoute from './components/auth/RoleBasedRoute';
import AdminRoute from './components/auth/AdminRoute';

import './styles/globals.css';

// Layout component to conditionally render Header and Footer
function Layout({ children }) {
    const location = useLocation();
    const isAdminRoute = location.pathname.startsWith('/admin');

    return (
        <div className="App">
            {!isAdminRoute && <Header />}
            <main>
                {children}
            </main>
            {!isAdminRoute && <Footer />}
        </div>
    );
}

function App() {
    return (
        <AuthProvider>
            <CurrencyProvider>
                <LanguageProvider>
                    <CartProvider>
                        <Router>
                            <Layout>
                                <Routes>
                                    {/* Public Routes - Accessible to all users */}
                                    <Route path="/" element={<Home />} />
                                    <Route path="/login" element={<Login />} />
                                    <Route path="/products" element={<ProductCatalog />} />
                                    <Route path="/product/:id" element={<ProductDetail />} />
                                    <Route path="/products/:id" element={<ProductDetail />} />
                                    <Route path="/about" element={<About />} />
                                    <Route path="/gallery" element={<Gallery />} />
                                    <Route path="/demo" element={<ProductCardDemo />} />

                                    {/* Protected Routes - Require authentication */}
                                    <Route
                                        path="/cart"
                                        element={
                                            <ProtectedRoute>
                                                <Cart />
                                            </ProtectedRoute>
                                        }
                                    />
                                    <Route
                                        path="/checkout"
                                        element={<CheckoutPage />}
                                    />
                                    <Route
                                        path="/order-success"
                                        element={
                                            <ProtectedRoute>
                                                <OrderSuccessPage />
                                            </ProtectedRoute>
                                        }
                                    />
                                    <Route
                                        path="/payment"
                                        element={
                                            <ProtectedRoute>
                                                <Payment />
                                            </ProtectedRoute>
                                        }
                                    />
                                    <Route
                                        path="/account"
                                        element={
                                            <ProtectedRoute>
                                                <Account />
                                            </ProtectedRoute>
                                        }
                                    />

                                    {/* Admin Routes - Require Employee or Admin role */}
                                    <Route
                                        path="/admin"
                                        element={
                                            <AdminRoute allowEmployee={true}>
                                                <AdminDashboard />
                                            </AdminRoute>
                                        }
                                    />
                                    {/* Admin sub-routes - redirect to main admin with tab parameter */}
                                    <Route
                                        path="/admin/orders"
                                        element={<Navigate to="/admin?tab=orders" replace />}
                                    />
                                    <Route
                                        path="/admin/inventory"
                                        element={<Navigate to="/admin?tab=inventory" replace />}
                                    />
                                    <Route
                                        path="/admin/products"
                                        element={<Navigate to="/admin?tab=products" replace />}
                                    />
                                    <Route
                                        path="/admin/suppliers"
                                        element={<Navigate to="/admin?tab=suppliers" replace />}
                                    />
                                    <Route
                                        path="/admin/users"
                                        element={<Navigate to="/admin?tab=users" replace />}
                                    />
                                    <Route
                                        path="/admin/analytics"
                                        element={<Navigate to="/admin?tab=analytics" replace />}
                                    />
                                </Routes>
                            </Layout>
                        </Router>
                    </CartProvider>
                </LanguageProvider>
            </CurrencyProvider>
        </AuthProvider>
    );
}

export default App;