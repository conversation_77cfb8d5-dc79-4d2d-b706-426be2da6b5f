/**
 * Application Constants
 * Centralized location for all application constants
 */

// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3
};

// Product Configuration
export const PRODUCT_CONFIG = {
  IMAGES_PER_PRODUCT: 5,
  MAX_IMAGE_SIZE: 5 * 1024 * 1024, // 5MB
  SUPPORTED_IMAGE_FORMATS: ['jpg', 'jpeg', 'png', 'webp'],
  DEFAULT_IMAGE: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
  FALLBACK_IMAGE: '/placeholder-image.jpg'
};

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 12,
  MAX_PAGE_SIZE: 50,
  PAGE_SIZE_OPTIONS: [6, 12, 24, 48]
};

// Price Configuration
export const PRICE_CONFIG = {
  CURRENCY: 'USD',
  LOCALE: 'en-US',
  DECIMAL_PLACES: 2,
  MIN_PRICE: 0,
  MAX_PRICE: 999999
};

// Configuration Options
export const CONFIGURATION_OPTIONS = {
  COLORS: {
    default: { name: 'Default', multiplier: 1 },
    cherry: { name: 'Cherry Wood', multiplier: 1.15 },
    'dark-walnut': { name: 'Dark Walnut', multiplier: 1.10 },
    black: { name: 'Black', multiplier: 1.05 },
    white: { name: 'White', multiplier: 1.05 }
  },
  MATERIALS: {
    default: { name: 'Standard', multiplier: 1 },
    'solid-wood': { name: 'Solid Wood', multiplier: 1.25 },
    metal: { name: 'Metal', multiplier: 1.20 },
    glass: { name: 'Glass', multiplier: 1.15 }
  },
  SIZES: {
    standard: { name: 'Standard', multiplier: 1 },
    compact: { name: 'Compact', multiplier: 0.90 },
    large: { name: 'Large', multiplier: 1.20 },
    xl: { name: 'Extra Large', multiplier: 1.35 }
  }
};

// 3D Configurable Product Types
export const CONFIGURABLE_PRODUCT_TYPES = [
  'table', 'desk', 'chair', 'cabinet', 'storage', 'workstation', 'shelf'
];

// User Roles
export const USER_ROLES = {
  ADMIN: 'admin',
  CUSTOMER: 'customer',
  MANAGER: 'manager'
};

// Order Status
export const ORDER_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  PROCESSING: 'processing',
  SHIPPED: 'shipped',
  DELIVERED: 'delivered',
  CANCELLED: 'cancelled'
};

// Payment Status
export const PAYMENT_STATUS = {
  PENDING: 'pending',
  COMPLETED: 'completed',
  FAILED: 'failed',
  REFUNDED: 'refunded'
};

// Local Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_DATA: 'user_data',
  CART_DATA: 'cart_data',
  LANGUAGE: 'selected_language',
  CURRENCY: 'selected_currency',
  THEME: 'theme_preference'
};

// Validation Rules
export const VALIDATION = {
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE_REGEX: /^[\+]?[1-9][\d]{0,15}$/,
  PASSWORD_MIN_LENGTH: 8,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'Access denied.',
  NOT_FOUND: 'The requested resource was not found.',
  SERVER_ERROR: 'Internal server error. Please try again later.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  TIMEOUT_ERROR: 'Request timed out. Please try again.'
};

// Success Messages
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Login successful!',
  LOGOUT_SUCCESS: 'Logged out successfully.',
  REGISTRATION_SUCCESS: 'Account created successfully!',
  PROFILE_UPDATED: 'Profile updated successfully.',
  ITEM_ADDED_TO_CART: 'Item added to cart.',
  ORDER_PLACED: 'Order placed successfully!',
  PASSWORD_CHANGED: 'Password changed successfully.'
};

// Animation Durations (in milliseconds)
export const ANIMATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  VERY_SLOW: 1000
};

// Breakpoints (matching CSS)
export const BREAKPOINTS = {
  MOBILE: 480,
  TABLET: 768,
  DESKTOP: 1024,
  LARGE_DESKTOP: 1200
};

// Feature Flags
export const FEATURES = {
  ENABLE_3D_CONFIGURATOR: true,
  ENABLE_WISHLIST: true,
  ENABLE_REVIEWS: true,
  ENABLE_CHAT_SUPPORT: false,
  ENABLE_ANALYTICS: true
};

// Contact Information
export const CONTACT_INFO = {
  EMAIL: '<EMAIL>',
  PHONE: '(02) 413-6682',
  ADDRESS: '#1 Binakla Street Cor Biak na Bato Brgy, Manresa, Quezon City',
  BUSINESS_HOURS: 'Mon-Fri: 9AM-6PM, Sat: 10AM-4PM'
};

// Social Media Links
export const SOCIAL_LINKS = {
  FACEBOOK: 'https://facebook.com/designexcellence',
  INSTAGRAM: 'https://instagram.com/designexcellence',
  TWITTER: 'https://twitter.com/designexcellence'
};

// Default Export
export default {
  API_CONFIG,
  PRODUCT_CONFIG,
  PAGINATION,
  PRICE_CONFIG,
  CONFIGURATION_OPTIONS,
  CONFIGURABLE_PRODUCT_TYPES,
  USER_ROLES,
  ORDER_STATUS,
  PAYMENT_STATUS,
  STORAGE_KEYS,
  VALIDATION,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  ANIMATION,
  BREAKPOINTS,
  FEATURES,
  CONTACT_INFO,
  SOCIAL_LINKS
};
