// Centralized API Client
// This service provides a configured axios instance for all API calls

import axios from 'axios';
import apiConfig from './apiConfig';

class ApiClient {
  constructor() {
    // Create axios instance with base configuration
    this.client = axios.create({
      baseURL: apiConfig.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    // Setup request interceptor for authentication
    this.client.interceptors.request.use(
      (config) => {
        // Add auth token if available
        const token = this.getAuthToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Add request timestamp for debugging
        if (apiConfig.debugMode) {
          config.metadata = { startTime: new Date() };
        }

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Setup response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => {
        // Log response time in debug mode
        if (apiConfig.debugMode && response.config.metadata) {
          const duration = new Date() - response.config.metadata.startTime;
          console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`);
        }

        return response;
      },
      (error) => {
        // Handle different error types
        if (error.response) {
          // Server responded with error status
          const { status, data } = error.response;
          
          if (status === 401) {
            // Unauthorized - clear auth token and redirect to login
            this.clearAuthToken();
            if (window.location.pathname !== '/login') {
              window.location.href = '/login';
            }
          } else if (status === 403) {
            // Forbidden - check if it's an invalid token issue
            if (data.message && (data.message.includes('token') || data.message.includes('expired') || data.message.includes('invalid'))) {
              // Invalid/expired token - clear auth and redirect to login
              this.clearAuthToken();
              if (window.location.pathname !== '/login') {
                window.location.href = '/login';
              }
            }
          } else if (status >= 500 && apiConfig.debugMode) {
            // Server error (debug mode only)
            console.error('❌ Server Error:', data.message || 'Internal server error');
          }

          // Log error details in debug mode
          if (apiConfig.debugMode) {
            console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {
              status,
              data,
              headers: error.response.headers
            });
          }
        } else if (error.request && apiConfig.debugMode) {
          // Network error (debug mode only)
          console.error('❌ Network Error:', error.message);
        } else if (apiConfig.debugMode) {
          // Other error (debug mode only)
          console.error('❌ Request Setup Error:', error.message);
        }

        return Promise.reject(error);
      }
    );
  }

  // Get authentication token from localStorage
  getAuthToken() {
    return localStorage.getItem('token') || localStorage.getItem('authToken');
  }

  // Clear authentication token
  clearAuthToken() {
    localStorage.removeItem('token');
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
  }

  // Generic GET request
  async get(url, config = {}) {
    try {
      const response = await this.client.get(url, config);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Generic POST request
  async post(url, data = {}, config = {}) {
    try {
      const response = await this.client.post(url, data, config);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Generic PUT request
  async put(url, data = {}, config = {}) {
    try {
      const response = await this.client.put(url, data, config);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Generic PATCH request
  async patch(url, data = {}, config = {}) {
    try {
      const response = await this.client.patch(url, data, config);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Generic DELETE request
  async delete(url, config = {}) {
    try {
      const response = await this.client.delete(url, config);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Handle and format errors
  handleError(error) {
    if (error.response) {
      // Server responded with error
      const message = error.response.data?.message || error.response.data?.error || 'Server error occurred';
      return new Error(message);
    } else if (error.request) {
      // Network error
      return new Error('Network error - please check your connection');
    } else {
      // Other error
      return new Error(error.message || 'An unexpected error occurred');
    }
  }

  // Health check
  async healthCheck() {
    try {
      const response = await this.get('/health');
      return response;
    } catch (error) {
      if (apiConfig.debugMode) {
        console.error('❌ Backend health check failed:', error.message);
      }
      return null;
    }
  }

  // Test connection to backend
  async testConnection() {
    try {
      const health = await this.healthCheck();
      if (health && apiConfig.debugMode) {
        console.log('✅ Backend connection successful');
      }
      return !!health;
    } catch (error) {
      if (apiConfig.debugMode) {
        console.error('❌ Backend connection failed:', error.message);
      }
      return false;
    }
  }
}

// Create singleton instance
const apiClient = new ApiClient();

export default apiClient;
