{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\pages\\\\ProductDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, Link } from 'react-router-dom';\nimport { getProductById } from '../services/products';\nimport { useCart } from '../contexts/CartContext';\nimport CheckoutModal from '../components/cart/CheckoutModal';\nimport Advanced3DConfigurator from '../components/3d/3DConfigurator';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProductDetail = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const {\n    addToCart\n  } = useCart();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [selectedImage, setSelectedImage] = useState(0);\n  const [show3DConfigurator, setShow3DConfigurator] = useState(false);\n  const [showCheckoutModal, setShowCheckoutModal] = useState(false);\n  const [customization, setCustomization] = useState({\n    color: '#6B7280',\n    material: 'wood',\n    dimensions: {\n      width: 120,\n      height: 75,\n      depth: 60\n    }\n  });\n  const [quantity, setQuantity] = useState(1);\n  useEffect(() => {\n    loadProduct();\n  }, [id]);\n  const loadProduct = async () => {\n    try {\n      const response = await getProductById(id);\n      setProduct(response.product);\n    } catch (error) {\n      setError('Failed to load product details');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatPrice = price => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  };\n\n  // Check if product supports advanced 3D configuration\n  const supportsAdvanced3D = product => {\n    if (!product) return false;\n    const productType = product.name.toLowerCase();\n    const configurableTypes = ['table', 'desk', 'chair', 'cabinet', 'storage', 'workstation', 'shelf'];\n    return configurableTypes.some(type => productType.includes(type));\n  };\n\n  // Handle adding to cart with customization\n  const handleAddToCart = () => {\n    if (!product) return;\n    try {\n      addToCart(product, quantity, customization);\n      setShowCheckoutModal(true);\n    } catch (error) {\n      alert('Failed to add item to cart. Please try again.');\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-detail-page\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading\",\n          children: \"Loading product details...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 13\n    }, this);\n  }\n  if (error || !product) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-detail-page\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: [error || 'Product not found', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: \"btn btn-primary\",\n            children: \"Back to Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 13\n    }, this);\n  }\n  const {\n    name,\n    description,\n    price,\n    discountPrice,\n    images,\n    categoryName,\n    specifications\n  } = product;\n  const displayPrice = discountPrice || price;\n  const hasDiscount = discountPrice && discountPrice < price;\n\n  // Check if this product supports advanced 3D configuration\n  const isConfigurable3D = supportsAdvanced3D(product);\n\n  // If 3D configurator is enabled and it's a configurable product, show the configurator\n  if (show3DConfigurator && isConfigurable3D) {\n    return /*#__PURE__*/_jsxDEV(Advanced3DConfigurator, {\n      product: product,\n      onBack: () => setShow3DConfigurator(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 16\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"product-detail-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"breadcrumb\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          children: \"Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 21\n        }, this), \" /\", /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/products\",\n          children: \"Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 21\n        }, this), \" /\", /*#__PURE__*/_jsxDEV(\"span\", {\n          children: name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-detail\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-media\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"main-media\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"main-image\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: images && images[selectedImage] ? images[selectedImage] : 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600',\n                alt: name,\n                onError: e => {\n                  e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 33\n              }, this), isConfigurable3D && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"image-overlay\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"configurator-btn\",\n                  onClick: () => setShow3DConfigurator(true),\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"16\",\n                    height: \"16\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M12 2L2 7L12 12L22 7L12 2Z\",\n                      fill: \"currentColor\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 140,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M2 17L12 22L22 17\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 141,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M2 12L12 17L22 12\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 142,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 45\n                  }, this), \"3D Configurator\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"media-thumbnails\",\n            children: images && images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"image-thumbnails\",\n              children: images.map((image, index) => /*#__PURE__*/_jsxDEV(\"img\", {\n                src: image,\n                alt: `${name} ${index + 1}`,\n                className: selectedImage === index ? 'active' : '',\n                onClick: () => setSelectedImage(index),\n                onError: e => {\n                  e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=150';\n                }\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-category\",\n            children: categoryName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            children: name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-pricing\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"current-price\",\n              children: formatPrice(displayPrice)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 29\n            }, this), hasDiscount && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"original-price\",\n                children: formatPrice(price)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"discount-badge\",\n                children: [Math.round((price - discountPrice) / price * 100), \"% OFF\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-description\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 25\n          }, this), specifications && Object.keys(specifications).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-specifications\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Specifications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: Object.entries(specifications).map(([key, value]) => /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()), \":\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 45\n                }, this), \" \", value]\n              }, key, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-customization\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Product Options\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"customization-options\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"option-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Quantity\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"quantity-control\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setQuantity(Math.max(1, quantity - 1)),\n                    className: \"quantity-btn\",\n                    children: \"-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"quantity-value\",\n                    children: quantity\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setQuantity(quantity + 1),\n                    className: \"quantity-btn\",\n                    children: \"+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 33\n              }, this), isConfigurable3D && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"option-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Advanced Customization\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"customization-note\",\n                  children: \"Use the 3D Configurator to customize colors, materials, dimensions, and more advanced options.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary btn-large\",\n              onClick: handleAddToCart,\n              children: [\"Add to Cart - \", formatPrice((discountPrice || price) * quantity)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 29\n            }, this), isConfigurable3D && /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-3d-configurator btn-large\",\n              onClick: () => setShow3DConfigurator(true),\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M12 2L2 7L12 12L22 7L12 2Z\",\n                  fill: \"currentColor\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M2 17L12 22L22 17\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M2 12L12 17L22 12\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 37\n              }, this), \"3D Configurator\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"related-products\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Related Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"related-products-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"related-product-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"related-product-image\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300\",\n                alt: \"Related Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"related-product-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Hotel Bed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"$2,299\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"related-product-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"related-product-image\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300\",\n                alt: \"Related Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"related-product-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Conference Table\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"$2,499\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"related-product-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"related-product-image\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300\",\n                alt: \"Related Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"related-product-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Reception Desk\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"$1,899\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(CheckoutModal, {\n      isOpen: showCheckoutModal,\n      onClose: () => setShowCheckoutModal(false),\n      product: product,\n      quantity: quantity\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 9\n  }, this);\n};\n_s(ProductDetail, \"CUI6LraouZxTbceAwWcnm3lxOYs=\", false, function () {\n  return [useParams, useCart];\n});\n_c = ProductDetail;\nexport default ProductDetail;\nvar _c;\n$RefreshReg$(_c, \"ProductDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Link", "getProductById", "useCart", "CheckoutModal", "Advanced3DConfigurator", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductDetail", "_s", "id", "addToCart", "product", "setProduct", "loading", "setLoading", "error", "setError", "selectedImage", "setSelectedImage", "show3DConfigurator", "setShow3DConfigurator", "showCheckoutModal", "setShowCheckoutModal", "customization", "setCustomization", "color", "material", "dimensions", "width", "height", "depth", "quantity", "setQuantity", "loadProduct", "response", "formatPrice", "price", "Intl", "NumberFormat", "style", "currency", "format", "supportsAdvanced3D", "productType", "name", "toLowerCase", "configurableTypes", "some", "type", "includes", "handleAddToCart", "alert", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "description", "discountPrice", "images", "categoryName", "specifications", "displayPrice", "hasDiscount", "isConfigurable3D", "onBack", "src", "alt", "onError", "e", "target", "onClick", "viewBox", "fill", "xmlns", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "length", "map", "image", "index", "Math", "round", "Object", "keys", "entries", "key", "value", "replace", "str", "toUpperCase", "max", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/pages/ProductDetail.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { usePara<PERSON>, Link } from 'react-router-dom';\nimport { getProductById } from '../services/products';\nimport { useCart } from '../contexts/CartContext';\nimport CheckoutModal from '../components/cart/CheckoutModal';\nimport Advanced3DConfigurator from '../components/3d/3DConfigurator';\n\nconst ProductDetail = () => {\n    const { id } = useParams();\n    const { addToCart } = useCart();\n    const [product, setProduct] = useState(null);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState('');\n    const [selectedImage, setSelectedImage] = useState(0);\n    const [show3DConfigurator, setShow3DConfigurator] = useState(false);\n    const [showCheckoutModal, setShowCheckoutModal] = useState(false);\n    const [customization, setCustomization] = useState({\n        color: '#6B7280',\n        material: 'wood',\n        dimensions: { width: 120, height: 75, depth: 60 }\n    });\n    const [quantity, setQuantity] = useState(1);\n\n    useEffect(() => {\n        loadProduct();\n    }, [id]);\n\n    const loadProduct = async () => {\n        try {\n            const response = await getProductById(id);\n            setProduct(response.product);\n        } catch (error) {\n            setError('Failed to load product details');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const formatPrice = (price) => {\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: 'USD'\n        }).format(price);\n    };\n\n    // Check if product supports advanced 3D configuration\n    const supportsAdvanced3D = (product) => {\n        if (!product) return false;\n        const productType = product.name.toLowerCase();\n        const configurableTypes = ['table', 'desk', 'chair', 'cabinet', 'storage', 'workstation', 'shelf'];\n        return configurableTypes.some(type => productType.includes(type));\n    };\n\n    // Handle adding to cart with customization\n    const handleAddToCart = () => {\n        if (!product) return;\n\n        try {\n            addToCart(product, quantity, customization);\n            setShowCheckoutModal(true);\n        } catch (error) {\n            alert('Failed to add item to cart. Please try again.');\n        }\n    };\n\n    if (loading) {\n        return (\n            <div className=\"product-detail-page\">\n                <div className=\"container\">\n                    <div className=\"loading\">Loading product details...</div>\n                </div>\n            </div>\n        );\n    }\n\n    if (error || !product) {\n        return (\n            <div className=\"product-detail-page\">\n                <div className=\"container\">\n                    <div className=\"error-message\">\n                        {error || 'Product not found'}\n                        <Link to=\"/products\" className=\"btn btn-primary\">\n                            Back to Products\n                        </Link>\n                    </div>\n                </div>\n            </div>\n        );\n    }\n\n    const {\n        name,\n        description,\n        price,\n        discountPrice,\n        images,\n        categoryName,\n        specifications\n    } = product;\n\n    const displayPrice = discountPrice || price;\n    const hasDiscount = discountPrice && discountPrice < price;\n\n    // Check if this product supports advanced 3D configuration\n    const isConfigurable3D = supportsAdvanced3D(product);\n\n    // If 3D configurator is enabled and it's a configurable product, show the configurator\n    if (show3DConfigurator && isConfigurable3D) {\n        return <Advanced3DConfigurator product={product} onBack={() => setShow3DConfigurator(false)} />;\n    }\n\n    return (\n        <div className=\"product-detail-page\">\n            <div className=\"container\">\n                <div className=\"breadcrumb\">\n                    <Link to=\"/\">Home</Link> /\n                    <Link to=\"/products\">Products</Link> /\n                    <span>{name}</span>\n                </div>\n\n                <div className=\"product-detail\">\n                    <div className=\"product-media\">\n                        {/* Main Media Display */}\n                        <div className=\"main-media\">\n                            <div className=\"main-image\">\n                                <img\n                                    src={images && images[selectedImage] ? images[selectedImage] : 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600'}\n                                    alt={name}\n                                    onError={(e) => {\n                                        e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600';\n                                    }}\n                                />\n                                {isConfigurable3D && (\n                                    <div className=\"image-overlay\">\n                                        <button\n                                            className=\"configurator-btn\"\n                                            onClick={() => setShow3DConfigurator(true)}\n                                        >\n                                            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                                                <path d=\"M12 2L2 7L12 12L22 7L12 2Z\" fill=\"currentColor\"/>\n                                                <path d=\"M2 17L12 22L22 17\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                                                <path d=\"M2 12L12 17L22 12\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                                            </svg>\n                                            3D Configurator\n                                        </button>\n                                    </div>\n                                )}\n                            </div>\n                        </div>\n\n                        {/* Thumbnails */}\n                        <div className=\"media-thumbnails\">\n                            {images && images.length > 1 && (\n                                <div className=\"image-thumbnails\">\n                                    {images.map((image, index) => (\n                                        <img\n                                            key={index}\n                                            src={image}\n                                            alt={`${name} ${index + 1}`}\n                                            className={selectedImage === index ? 'active' : ''}\n                                            onClick={() => setSelectedImage(index)}\n                                            onError={(e) => {\n                                                e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=150';\n                                            }}\n                                        />\n                                    ))}\n                                </div>\n                            )}\n                        </div>\n                    </div>\n\n                    <div className=\"product-info\">\n                        <div className=\"product-category\">{categoryName}</div>\n                        <h1>{name}</h1>\n\n                        <div className=\"product-pricing\">\n                            <span className=\"current-price\">{formatPrice(displayPrice)}</span>\n                            {hasDiscount && (\n                                <>\n                                    <span className=\"original-price\">{formatPrice(price)}</span>\n                                    <span className=\"discount-badge\">\n                                        {Math.round(((price - discountPrice) / price) * 100)}% OFF\n                                    </span>\n                                </>\n                            )}\n                        </div>\n\n                        <div className=\"product-description\">\n                            <p>{description}</p>\n                        </div>\n\n                        {specifications && Object.keys(specifications).length > 0 && (\n                            <div className=\"product-specifications\">\n                                <h3>Specifications</h3>\n                                <ul>\n                                    {Object.entries(specifications).map(([key, value]) => (\n                                        <li key={key}>\n                                            <strong>{key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}:</strong> {value}\n                                        </li>\n                                    ))}\n                                </ul>\n                            </div>\n                        )}\n\n                        {/* Basic Customization Options */}\n                        <div className=\"product-customization\">\n                            <h3>Product Options</h3>\n\n                            <div className=\"customization-options\">\n                                <div className=\"option-group\">\n                                    <label>Quantity</label>\n                                    <div className=\"quantity-control\">\n                                        <button\n                                            onClick={() => setQuantity(Math.max(1, quantity - 1))}\n                                            className=\"quantity-btn\"\n                                        >\n                                            -\n                                        </button>\n                                        <span className=\"quantity-value\">{quantity}</span>\n                                        <button\n                                            onClick={() => setQuantity(quantity + 1)}\n                                            className=\"quantity-btn\"\n                                        >\n                                            +\n                                        </button>\n                                    </div>\n                                </div>\n\n                                {isConfigurable3D && (\n                                    <div className=\"option-group\">\n                                        <label>Advanced Customization</label>\n                                        <p className=\"customization-note\">\n                                            Use the 3D Configurator to customize colors, materials, dimensions, and more advanced options.\n                                        </p>\n                                    </div>\n                                )}\n                            </div>\n                        </div>\n\n                        <div className=\"product-actions\">\n                            <button\n                                className=\"btn btn-primary btn-large\"\n                                onClick={handleAddToCart}\n                            >\n                                Add to Cart - {formatPrice((discountPrice || price) * quantity)}\n                            </button>\n                            {isConfigurable3D && (\n                                <button\n                                    className=\"btn btn-3d-configurator btn-large\"\n                                    onClick={() => setShow3DConfigurator(true)}\n                                >\n                                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                                        <path d=\"M12 2L2 7L12 12L22 7L12 2Z\" fill=\"currentColor\"/>\n                                        <path d=\"M2 17L12 22L22 17\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                                        <path d=\"M2 12L12 17L22 12\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                                    </svg>\n                                    3D Configurator\n                                </button>\n                            )}\n                        </div>\n                    </div>\n                </div>\n\n                {/* Related Products Section */}\n                <div className=\"related-products\">\n                    <h2>Related Products</h2>\n                    <div className=\"related-products-grid\">\n                        {/* Placeholder for related products */}\n                        <div className=\"related-product-card\">\n                            <div className=\"related-product-image\">\n                                <img src=\"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300\" alt=\"Related Product\" />\n                            </div>\n                            <div className=\"related-product-info\">\n                                <h4>Hotel Bed</h4>\n                                <p>$2,299</p>\n                            </div>\n                        </div>\n                        <div className=\"related-product-card\">\n                            <div className=\"related-product-image\">\n                                <img src=\"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300\" alt=\"Related Product\" />\n                            </div>\n                            <div className=\"related-product-info\">\n                                <h4>Conference Table</h4>\n                                <p>$2,499</p>\n                            </div>\n                        </div>\n                        <div className=\"related-product-card\">\n                            <div className=\"related-product-image\">\n                                <img src=\"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300\" alt=\"Related Product\" />\n                            </div>\n                            <div className=\"related-product-info\">\n                                <h4>Reception Desk</h4>\n                                <p>$1,899</p>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            {/* Checkout Modal */}\n            <CheckoutModal\n                isOpen={showCheckoutModal}\n                onClose={() => setShowCheckoutModal(false)}\n                product={product}\n                quantity={quantity}\n            />\n        </div>\n    );\n};\n\nexport default ProductDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,IAAI,QAAQ,kBAAkB;AAClD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,sBAAsB,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErE,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC;EAAG,CAAC,GAAGZ,SAAS,CAAC,CAAC;EAC1B,MAAM;IAAEa;EAAU,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC/B,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACwB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC0B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC4B,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAC;IAC/C8B,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE;MAAEC,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAG;EACpD,CAAC,CAAC;EACF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC;EAE3CC,SAAS,CAAC,MAAM;IACZqC,WAAW,CAAC,CAAC;EACjB,CAAC,EAAE,CAACxB,EAAE,CAAC,CAAC;EAER,MAAMwB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMnC,cAAc,CAACU,EAAE,CAAC;MACzCG,UAAU,CAACsB,QAAQ,CAACvB,OAAO,CAAC;IAChC,CAAC,CAAC,OAAOI,KAAK,EAAE;MACZC,QAAQ,CAAC,gCAAgC,CAAC;IAC9C,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMqB,WAAW,GAAIC,KAAK,IAAK;IAC3B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MAClCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACd,CAAC,CAAC,CAACC,MAAM,CAACL,KAAK,CAAC;EACpB,CAAC;;EAED;EACA,MAAMM,kBAAkB,GAAI/B,OAAO,IAAK;IACpC,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAC1B,MAAMgC,WAAW,GAAGhC,OAAO,CAACiC,IAAI,CAACC,WAAW,CAAC,CAAC;IAC9C,MAAMC,iBAAiB,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,OAAO,CAAC;IAClG,OAAOA,iBAAiB,CAACC,IAAI,CAACC,IAAI,IAAIL,WAAW,CAACM,QAAQ,CAACD,IAAI,CAAC,CAAC;EACrE,CAAC;;EAED;EACA,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACvC,OAAO,EAAE;IAEd,IAAI;MACAD,SAAS,CAACC,OAAO,EAAEoB,QAAQ,EAAER,aAAa,CAAC;MAC3CD,oBAAoB,CAAC,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOP,KAAK,EAAE;MACZoC,KAAK,CAAC,+CAA+C,CAAC;IAC1D;EACJ,CAAC;EAED,IAAItC,OAAO,EAAE;IACT,oBACIT,OAAA;MAAKgD,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAChCjD,OAAA;QAAKgD,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtBjD,OAAA;UAAKgD,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAI1C,KAAK,IAAI,CAACJ,OAAO,EAAE;IACnB,oBACIP,OAAA;MAAKgD,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAChCjD,OAAA;QAAKgD,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtBjD,OAAA;UAAKgD,SAAS,EAAC,eAAe;UAAAC,QAAA,GACzBtC,KAAK,IAAI,mBAAmB,eAC7BX,OAAA,CAACN,IAAI;YAAC4D,EAAE,EAAC,WAAW;YAACN,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAEjD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,MAAM;IACFb,IAAI;IACJe,WAAW;IACXvB,KAAK;IACLwB,aAAa;IACbC,MAAM;IACNC,YAAY;IACZC;EACJ,CAAC,GAAGpD,OAAO;EAEX,MAAMqD,YAAY,GAAGJ,aAAa,IAAIxB,KAAK;EAC3C,MAAM6B,WAAW,GAAGL,aAAa,IAAIA,aAAa,GAAGxB,KAAK;;EAE1D;EACA,MAAM8B,gBAAgB,GAAGxB,kBAAkB,CAAC/B,OAAO,CAAC;;EAEpD;EACA,IAAIQ,kBAAkB,IAAI+C,gBAAgB,EAAE;IACxC,oBAAO9D,OAAA,CAACF,sBAAsB;MAACS,OAAO,EAAEA,OAAQ;MAACwD,MAAM,EAAEA,CAAA,KAAM/C,qBAAqB,CAAC,KAAK;IAAE;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnG;EAEA,oBACIrD,OAAA;IAAKgD,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAChCjD,OAAA;MAAKgD,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACtBjD,OAAA;QAAKgD,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACvBjD,OAAA,CAACN,IAAI;UAAC4D,EAAE,EAAC,GAAG;UAAAL,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,MACxB,eAAArD,OAAA,CAACN,IAAI;UAAC4D,EAAE,EAAC,WAAW;UAAAL,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,MACpC,eAAArD,OAAA;UAAAiD,QAAA,EAAOT;QAAI;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eAENrD,OAAA;QAAKgD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC3BjD,OAAA;UAAKgD,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAE1BjD,OAAA;YAAKgD,SAAS,EAAC,YAAY;YAAAC,QAAA,eACvBjD,OAAA;cAAKgD,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvBjD,OAAA;gBACIgE,GAAG,EAAEP,MAAM,IAAIA,MAAM,CAAC5C,aAAa,CAAC,GAAG4C,MAAM,CAAC5C,aAAa,CAAC,GAAG,oEAAqE;gBACpIoD,GAAG,EAAEzB,IAAK;gBACV0B,OAAO,EAAGC,CAAC,IAAK;kBACZA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAG,oEAAoE;gBACvF;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EACDS,gBAAgB,iBACb9D,OAAA;gBAAKgD,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC1BjD,OAAA;kBACIgD,SAAS,EAAC,kBAAkB;kBAC5BqB,OAAO,EAAEA,CAAA,KAAMrD,qBAAqB,CAAC,IAAI,CAAE;kBAAAiC,QAAA,gBAE3CjD,OAAA;oBAAKwB,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAAC6C,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAACC,KAAK,EAAC,4BAA4B;oBAAAvB,QAAA,gBAC1FjD,OAAA;sBAAMyE,CAAC,EAAC,4BAA4B;sBAACF,IAAI,EAAC;oBAAc;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAC1DrD,OAAA;sBAAMyE,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAChHrD,OAAA;sBAAMyE,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/G,CAAC,mBAEV;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CACR;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNrD,OAAA;YAAKgD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC5BQ,MAAM,IAAIA,MAAM,CAACqB,MAAM,GAAG,CAAC,iBACxB9E,OAAA;cAAKgD,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAC5BQ,MAAM,CAACsB,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACrBjF,OAAA;gBAEIgE,GAAG,EAAEgB,KAAM;gBACXf,GAAG,EAAE,GAAGzB,IAAI,IAAIyC,KAAK,GAAG,CAAC,EAAG;gBAC5BjC,SAAS,EAAEnC,aAAa,KAAKoE,KAAK,GAAG,QAAQ,GAAG,EAAG;gBACnDZ,OAAO,EAAEA,CAAA,KAAMvD,gBAAgB,CAACmE,KAAK,CAAE;gBACvCf,OAAO,EAAGC,CAAC,IAAK;kBACZA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAG,oEAAoE;gBACvF;cAAE,GAPGiB,KAAK;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQb,CACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENrD,OAAA;UAAKgD,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBjD,OAAA;YAAKgD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAES;UAAY;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtDrD,OAAA;YAAAiD,QAAA,EAAKT;UAAI;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAEfrD,OAAA;YAAKgD,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC5BjD,OAAA;cAAMgD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAElB,WAAW,CAAC6B,YAAY;YAAC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACjEQ,WAAW,iBACR7D,OAAA,CAAAE,SAAA;cAAA+C,QAAA,gBACIjD,OAAA;gBAAMgD,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAElB,WAAW,CAACC,KAAK;cAAC;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5DrD,OAAA;gBAAMgD,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,GAC3BiC,IAAI,CAACC,KAAK,CAAE,CAACnD,KAAK,GAAGwB,aAAa,IAAIxB,KAAK,GAAI,GAAG,CAAC,EAAC,OACzD;cAAA;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eACT,CACL;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAENrD,OAAA;YAAKgD,SAAS,EAAC,qBAAqB;YAAAC,QAAA,eAChCjD,OAAA;cAAAiD,QAAA,EAAIM;YAAW;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,EAELM,cAAc,IAAIyB,MAAM,CAACC,IAAI,CAAC1B,cAAc,CAAC,CAACmB,MAAM,GAAG,CAAC,iBACrD9E,OAAA;YAAKgD,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACnCjD,OAAA;cAAAiD,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvBrD,OAAA;cAAAiD,QAAA,EACKmC,MAAM,CAACE,OAAO,CAAC3B,cAAc,CAAC,CAACoB,GAAG,CAAC,CAAC,CAACQ,GAAG,EAAEC,KAAK,CAAC,kBAC7CxF,OAAA;gBAAAiD,QAAA,gBACIjD,OAAA;kBAAAiD,QAAA,GAASsC,GAAG,CAACE,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACA,OAAO,CAAC,IAAI,EAAEC,GAAG,IAAIA,GAAG,CAACC,WAAW,CAAC,CAAC,CAAC,EAAC,GAAC;gBAAA;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACmC,KAAK;cAAA,GAD5FD,GAAG;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAER,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACR,eAGDrD,OAAA;YAAKgD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBAClCjD,OAAA;cAAAiD,QAAA,EAAI;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAExBrD,OAAA;cAAKgD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBAClCjD,OAAA;gBAAKgD,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBjD,OAAA;kBAAAiD,QAAA,EAAO;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvBrD,OAAA;kBAAKgD,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC7BjD,OAAA;oBACIqE,OAAO,EAAEA,CAAA,KAAMzC,WAAW,CAACsD,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEjE,QAAQ,GAAG,CAAC,CAAC,CAAE;oBACtDqB,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAC3B;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTrD,OAAA;oBAAMgD,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAAEtB;kBAAQ;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAClDrD,OAAA;oBACIqE,OAAO,EAAEA,CAAA,KAAMzC,WAAW,CAACD,QAAQ,GAAG,CAAC,CAAE;oBACzCqB,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAC3B;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EAELS,gBAAgB,iBACb9D,OAAA;gBAAKgD,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBjD,OAAA;kBAAAiD,QAAA,EAAO;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrCrD,OAAA;kBAAGgD,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAElC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACR;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENrD,OAAA;YAAKgD,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC5BjD,OAAA;cACIgD,SAAS,EAAC,2BAA2B;cACrCqB,OAAO,EAAEvB,eAAgB;cAAAG,QAAA,GAC5B,gBACiB,EAAClB,WAAW,CAAC,CAACyB,aAAa,IAAIxB,KAAK,IAAIL,QAAQ,CAAC;YAAA;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,EACRS,gBAAgB,iBACb9D,OAAA;cACIgD,SAAS,EAAC,mCAAmC;cAC7CqB,OAAO,EAAEA,CAAA,KAAMrD,qBAAqB,CAAC,IAAI,CAAE;cAAAiC,QAAA,gBAE3CjD,OAAA;gBAAKwB,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAAC6C,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAACC,KAAK,EAAC,4BAA4B;gBAAAvB,QAAA,gBAC1FjD,OAAA;kBAAMyE,CAAC,EAAC,4BAA4B;kBAACF,IAAI,EAAC;gBAAc;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC1DrD,OAAA;kBAAMyE,CAAC,EAAC,mBAAmB;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC;gBAAO;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAChHrD,OAAA;kBAAMyE,CAAC,EAAC,mBAAmB;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC;gBAAO;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/G,CAAC,mBAEV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACX;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNrD,OAAA;QAAKgD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC7BjD,OAAA;UAAAiD,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBrD,OAAA;UAAKgD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBAElCjD,OAAA;YAAKgD,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACjCjD,OAAA;cAAKgD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eAClCjD,OAAA;gBAAKgE,GAAG,EAAC,oEAAoE;gBAACC,GAAG,EAAC;cAAiB;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrG,CAAC,eACNrD,OAAA;cAAKgD,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACjCjD,OAAA;gBAAAiD,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClBrD,OAAA;gBAAAiD,QAAA,EAAG;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNrD,OAAA;YAAKgD,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACjCjD,OAAA;cAAKgD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eAClCjD,OAAA;gBAAKgE,GAAG,EAAC,oEAAoE;gBAACC,GAAG,EAAC;cAAiB;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrG,CAAC,eACNrD,OAAA;cAAKgD,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACjCjD,OAAA;gBAAAiD,QAAA,EAAI;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzBrD,OAAA;gBAAAiD,QAAA,EAAG;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNrD,OAAA;YAAKgD,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACjCjD,OAAA;cAAKgD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eAClCjD,OAAA;gBAAKgE,GAAG,EAAC,oEAAoE;gBAACC,GAAG,EAAC;cAAiB;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrG,CAAC,eACNrD,OAAA;cAAKgD,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACjCjD,OAAA;gBAAAiD,QAAA,EAAI;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvBrD,OAAA;gBAAAiD,QAAA,EAAG;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNrD,OAAA,CAACH,aAAa;MACVgG,MAAM,EAAE5E,iBAAkB;MAC1B6E,OAAO,EAAEA,CAAA,KAAM5E,oBAAoB,CAAC,KAAK,CAAE;MAC3CX,OAAO,EAAEA,OAAQ;MACjBoB,QAAQ,EAAEA;IAAS;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAACjD,EAAA,CA7SID,aAAa;EAAA,QACAV,SAAS,EACFG,OAAO;AAAA;AAAAmG,EAAA,GAF3B5F,aAAa;AA+SnB,eAAeA,aAAa;AAAC,IAAA4F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}