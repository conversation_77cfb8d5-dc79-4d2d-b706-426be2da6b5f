// Test script to verify checkout functionality
import paymentService from './services/paymentService';
import apiConfig from './services/apiConfig';

const testCheckout = async () => {
  console.log('🧪 Testing Checkout Functionality');
  console.log('==================================');
  
  // Test 1: Check API configuration
  console.log('\n1. API Configuration:');
  console.log(`   Base URL: ${apiConfig.getApiUrl()}`);
  console.log(`   Payment Processing: ${apiConfig.isFeatureEnabled('paymentProcessing') ? '✅ Enabled' : '❌ Disabled'}`);
  console.log(`   PayMongo Public Key: ${apiConfig.getPaymentConfig().paymongoPublicKey ? '✅ Configured' : '❌ Missing'}`);
  
  // Test 2: Test payment service
  console.log('\n2. Payment Service Test:');
  try {
    const testOrderData = {
      orderId: 'test-order-123',
      totalAmount: 10000, // ₱100.00
      currency: 'PHP',
      description: 'Test Order - Office Chair',
      customerInfo: {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+639123456789'
      },
      items: [
        {
          name: 'Executive Office Chair',
          quantity: 1,
          amount: 10000
        }
      ],
      metadata: {
        orderId: 'test-order-123',
        source: 'test-checkout'
      }
    };
    
    console.log('   Creating test payment link...');
    const result = await paymentService.createPaymentLink(testOrderData);
    
    if (result.success) {
      console.log('   ✅ Payment link created successfully');
      console.log(`   Payment Link ID: ${result.data.paymentLink.id}`);
      console.log(`   Checkout URL: ${result.data.paymentLink.attributes.checkout_url}`);
    } else {
      console.log('   ❌ Payment link creation failed');
      console.log(`   Error: ${result.error}`);
    }
    
  } catch (error) {
    console.log('   ❌ Payment service error:');
    console.log(`   ${error.message}`);
  }
  
  // Test 3: Check backend connectivity
  console.log('\n3. Backend Connectivity:');
  try {
    const response = await fetch(`${apiConfig.getApiUrl()}/api/health`);
    const data = await response.json();
    
    if (data.success) {
      console.log('   ✅ Backend server is running');
      console.log(`   PayMongo configured: ${data.paymongo?.configured ? '✅' : '❌'}`);
      console.log(`   Webhook configured: ${data.paymongo?.webhook_configured ? '✅' : '❌'}`);
    } else {
      console.log('   ❌ Backend health check failed');
    }
  } catch (error) {
    console.log('   ❌ Backend connectivity error:');
    console.log(`   ${error.message}`);
  }
  
  console.log('\n🏁 Test completed');
};

// Export for use in browser console
window.testCheckout = testCheckout;

export default testCheckout;
