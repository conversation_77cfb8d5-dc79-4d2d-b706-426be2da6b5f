{"ast": null, "code": "// API service for frontend application\n// This handles communication with the backend API\nimport apiClient from './apiClient';\n\n// Mock API functions\nconst api = {\n  // Get all products\n  getProducts: () => {\n    return Promise.resolve({\n      success: true,\n      data: mockProducts\n    });\n  },\n  // Get product by ID\n  getProduct: id => {\n    const product = mockProducts.find(p => p.id === parseInt(id));\n    return Promise.resolve({\n      success: true,\n      data: product || null\n    });\n  },\n  // Get categories\n  getCategories: () => {\n    return Promise.resolve({\n      success: true,\n      data: mockCategories\n    });\n  }\n};\n\n// Enhanced Products API for admin functionality\nconst productsApi = {\n  // Get products with pagination and filtering\n  getProducts: async (params = {}) => {\n    try {\n      const response = await apiClient.get('/api/products', {\n        params\n      });\n      return response;\n    } catch (error) {\n      console.error('Error fetching products:', error);\n      // Fallback to mock data if backend is not available\n      return {\n        success: true,\n        data: {\n          products: [],\n          pagination: {\n            currentPage: 1,\n            totalPages: 0,\n            totalItems: 0,\n            itemsPerPage: 20\n          }\n        }\n      };\n    }\n  },\n  // Get product by ID with full details\n  getProductById: async id => {\n    try {\n      const response = await apiClient.get(`/api/products/${id}`);\n      return response;\n    } catch (error) {\n      console.error('Error fetching product by ID:', error);\n      return {\n        success: false,\n        message: 'Product not found'\n      };\n    }\n  },\n  // Create or update product\n  createOrUpdateProduct: async productData => {\n    try {\n      const url = productData.ProductID ? `/api/products/${productData.ProductID}` : '/api/products';\n      const method = productData.ProductID ? 'PUT' : 'POST';\n      const response = await apiClient[method.toLowerCase()](url, productData);\n      return response;\n    } catch (error) {\n      console.error('Error creating/updating product:', error);\n      throw error;\n    }\n  },\n  // Delete product\n  deleteProduct: async productId => {\n    try {\n      const response = await apiClient.delete(`/api/products/${productId}`);\n      return response;\n    } catch (error) {\n      console.error('Error deleting product:', error);\n      throw error;\n    }\n  },\n  // Upload 3D model\n  uploadModel: async (productId, formData) => {\n    try {\n      const response = await apiClient.post(`/api/products/${productId}/models`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response;\n    } catch (error) {\n      console.error('Error uploading 3D model:', error);\n      throw error;\n    }\n  },\n  // Upload images\n  uploadImages: async (productId, formData) => {\n    try {\n      const response = await apiClient.post(`/api/products/${productId}/images`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response;\n    } catch (error) {\n      console.error('Error uploading images:', error);\n      throw error;\n    }\n  },\n  // Get categories\n  getCategories: async () => {\n    try {\n      const response = await apiClient.get('/api/products/categories');\n      return response;\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n      // Fallback to mock data if backend is not available\n      return {\n        success: true,\n        data: mockCategories\n      };\n    }\n  }\n};\n\n// Original API with authentication\nconst authApi = {\n  // Mock authentication with admin support\n  login: credentials => {\n    // Check for admin credentials\n    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {\n      return Promise.resolve({\n        success: true,\n        data: {\n          token: 'mock-admin-jwt-token',\n          user: {\n            id: 1,\n            firstName: 'Admin',\n            lastName: 'User',\n            email: credentials.email,\n            role: 'Admin'\n          }\n        }\n      });\n    }\n\n    // Check for employee credentials\n    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {\n      return Promise.resolve({\n        success: true,\n        data: {\n          token: 'mock-employee-jwt-token',\n          user: {\n            id: 2,\n            firstName: 'Manager',\n            lastName: 'User',\n            email: credentials.email,\n            role: 'Employee'\n          }\n        }\n      });\n    }\n\n    // Regular customer login\n    return Promise.resolve({\n      success: true,\n      data: {\n        token: 'mock-jwt-token',\n        user: {\n          id: 3,\n          firstName: 'Demo',\n          lastName: 'User',\n          email: credentials.email,\n          role: 'Customer'\n        }\n      }\n    });\n  },\n  // Mock registration (always succeeds)\n  register: userData => {\n    return Promise.resolve({\n      success: true,\n      data: {\n        token: 'mock-jwt-token',\n        user: {\n          id: 1,\n          name: userData.name,\n          email: userData.email\n        }\n      }\n    });\n  }\n};\n\n// Combine APIs\nconst combinedApi = {\n  ...api,\n  ...authApi\n};\nexport default combinedApi;\nexport { productsApi };", "map": {"version": 3, "names": ["apiClient", "api", "getProducts", "Promise", "resolve", "success", "data", "mockProducts", "getProduct", "id", "product", "find", "p", "parseInt", "getCategories", "mockCategories", "productsApi", "params", "response", "get", "error", "console", "products", "pagination", "currentPage", "totalPages", "totalItems", "itemsPerPage", "getProductById", "message", "createOrUpdateProduct", "productData", "url", "ProductID", "method", "toLowerCase", "deleteProduct", "productId", "delete", "uploadModel", "formData", "post", "headers", "uploadImages", "authApi", "login", "credentials", "email", "password", "token", "user", "firstName", "lastName", "role", "register", "userData", "name", "combinedApi"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/services/api.js"], "sourcesContent": ["// API service for frontend application\n// This handles communication with the backend API\nimport apiClient from './apiClient';\n\n// Mock API functions\nconst api = {\n  // Get all products\n  getProducts: () => {\n    return Promise.resolve({\n      success: true,\n      data: mockProducts\n    });\n  },\n\n  // Get product by ID\n  getProduct: (id) => {\n    const product = mockProducts.find(p => p.id === parseInt(id));\n    return Promise.resolve({\n      success: true,\n      data: product || null\n    });\n  },\n\n  // Get categories\n  getCategories: () => {\n    return Promise.resolve({\n      success: true,\n      data: mockCategories\n    });\n  }\n};\n\n// Enhanced Products API for admin functionality\nconst productsApi = {\n  // Get products with pagination and filtering\n  getProducts: async (params = {}) => {\n    try {\n      const response = await apiClient.get('/api/products', { params });\n      return response;\n    } catch (error) {\n      console.error('Error fetching products:', error);\n      // Fallback to mock data if backend is not available\n      return {\n        success: true,\n        data: {\n          products: [],\n          pagination: {\n            currentPage: 1,\n            totalPages: 0,\n            totalItems: 0,\n            itemsPerPage: 20\n          }\n        }\n      };\n    }\n  },\n\n  // Get product by ID with full details\n  getProductById: async (id) => {\n    try {\n      const response = await apiClient.get(`/api/products/${id}`);\n      return response;\n    } catch (error) {\n      console.error('Error fetching product by ID:', error);\n      return {\n        success: false,\n        message: 'Product not found'\n      };\n    }\n  },\n\n  // Create or update product\n  createOrUpdateProduct: async (productData) => {\n    try {\n      const url = productData.ProductID\n        ? `/api/products/${productData.ProductID}`\n        : '/api/products';\n\n      const method = productData.ProductID ? 'PUT' : 'POST';\n\n      const response = await apiClient[method.toLowerCase()](url, productData);\n      return response;\n    } catch (error) {\n      console.error('Error creating/updating product:', error);\n      throw error;\n    }\n  },\n\n  // Delete product\n  deleteProduct: async (productId) => {\n    try {\n      const response = await apiClient.delete(`/api/products/${productId}`);\n      return response;\n    } catch (error) {\n      console.error('Error deleting product:', error);\n      throw error;\n    }\n  },\n\n  // Upload 3D model\n  uploadModel: async (productId, formData) => {\n    try {\n      const response = await apiClient.post(`/api/products/${productId}/models`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response;\n    } catch (error) {\n      console.error('Error uploading 3D model:', error);\n      throw error;\n    }\n  },\n\n  // Upload images\n  uploadImages: async (productId, formData) => {\n    try {\n      const response = await apiClient.post(`/api/products/${productId}/images`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response;\n    } catch (error) {\n      console.error('Error uploading images:', error);\n      throw error;\n    }\n  },\n\n  // Get categories\n  getCategories: async () => {\n    try {\n      const response = await apiClient.get('/api/products/categories');\n      return response;\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n      // Fallback to mock data if backend is not available\n      return {\n        success: true,\n        data: mockCategories\n      };\n    }\n  }\n};\n\n// Original API with authentication\nconst authApi = {\n  // Mock authentication with admin support\n  login: (credentials) => {\n    // Check for admin credentials\n    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {\n      return Promise.resolve({\n        success: true,\n        data: {\n          token: 'mock-admin-jwt-token',\n          user: {\n            id: 1,\n            firstName: 'Admin',\n            lastName: 'User',\n            email: credentials.email,\n            role: 'Admin'\n          }\n        }\n      });\n    }\n\n    // Check for employee credentials\n    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {\n      return Promise.resolve({\n        success: true,\n        data: {\n          token: 'mock-employee-jwt-token',\n          user: {\n            id: 2,\n            firstName: 'Manager',\n            lastName: 'User',\n            email: credentials.email,\n            role: 'Employee'\n          }\n        }\n      });\n    }\n\n    // Regular customer login\n    return Promise.resolve({\n      success: true,\n      data: {\n        token: 'mock-jwt-token',\n        user: {\n          id: 3,\n          firstName: 'Demo',\n          lastName: 'User',\n          email: credentials.email,\n          role: 'Customer'\n        }\n      }\n    });\n  },\n\n  // Mock registration (always succeeds)\n  register: (userData) => {\n    return Promise.resolve({\n      success: true,\n      data: {\n        token: 'mock-jwt-token',\n        user: {\n          id: 1,\n          name: userData.name,\n          email: userData.email\n        }\n      }\n    });\n  }\n};\n\n// Combine APIs\nconst combinedApi = {\n  ...api,\n  ...authApi\n};\n\nexport default combinedApi;\nexport { productsApi };\n"], "mappings": "AAAA;AACA;AACA,OAAOA,SAAS,MAAM,aAAa;;AAEnC;AACA,MAAMC,GAAG,GAAG;EACV;EACAC,WAAW,EAAEA,CAAA,KAAM;IACjB,OAAOC,OAAO,CAACC,OAAO,CAAC;MACrBC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAEC;IACR,CAAC,CAAC;EACJ,CAAC;EAED;EACAC,UAAU,EAAGC,EAAE,IAAK;IAClB,MAAMC,OAAO,GAAGH,YAAY,CAACI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,EAAE,KAAKI,QAAQ,CAACJ,EAAE,CAAC,CAAC;IAC7D,OAAON,OAAO,CAACC,OAAO,CAAC;MACrBC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAEI,OAAO,IAAI;IACnB,CAAC,CAAC;EACJ,CAAC;EAED;EACAI,aAAa,EAAEA,CAAA,KAAM;IACnB,OAAOX,OAAO,CAACC,OAAO,CAAC;MACrBC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAES;IACR,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,MAAMC,WAAW,GAAG;EAClB;EACAd,WAAW,EAAE,MAAAA,CAAOe,MAAM,GAAG,CAAC,CAAC,KAAK;IAClC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMlB,SAAS,CAACmB,GAAG,CAAC,eAAe,EAAE;QAAEF;MAAO,CAAC,CAAC;MACjE,OAAOC,QAAQ;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD;MACA,OAAO;QACLf,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE;UACJgB,QAAQ,EAAE,EAAE;UACZC,UAAU,EAAE;YACVC,WAAW,EAAE,CAAC;YACdC,UAAU,EAAE,CAAC;YACbC,UAAU,EAAE,CAAC;YACbC,YAAY,EAAE;UAChB;QACF;MACF,CAAC;IACH;EACF,CAAC;EAED;EACAC,cAAc,EAAE,MAAOnB,EAAE,IAAK;IAC5B,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAMlB,SAAS,CAACmB,GAAG,CAAC,iBAAiBV,EAAE,EAAE,CAAC;MAC3D,OAAOS,QAAQ;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO;QACLf,OAAO,EAAE,KAAK;QACdwB,OAAO,EAAE;MACX,CAAC;IACH;EACF,CAAC;EAED;EACAC,qBAAqB,EAAE,MAAOC,WAAW,IAAK;IAC5C,IAAI;MACF,MAAMC,GAAG,GAAGD,WAAW,CAACE,SAAS,GAC7B,iBAAiBF,WAAW,CAACE,SAAS,EAAE,GACxC,eAAe;MAEnB,MAAMC,MAAM,GAAGH,WAAW,CAACE,SAAS,GAAG,KAAK,GAAG,MAAM;MAErD,MAAMf,QAAQ,GAAG,MAAMlB,SAAS,CAACkC,MAAM,CAACC,WAAW,CAAC,CAAC,CAAC,CAACH,GAAG,EAAED,WAAW,CAAC;MACxE,OAAOb,QAAQ;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAgB,aAAa,EAAE,MAAOC,SAAS,IAAK;IAClC,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAMlB,SAAS,CAACsC,MAAM,CAAC,iBAAiBD,SAAS,EAAE,CAAC;MACrE,OAAOnB,QAAQ;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAmB,WAAW,EAAE,MAAAA,CAAOF,SAAS,EAAEG,QAAQ,KAAK;IAC1C,IAAI;MACF,MAAMtB,QAAQ,GAAG,MAAMlB,SAAS,CAACyC,IAAI,CAAC,iBAAiBJ,SAAS,SAAS,EAAEG,QAAQ,EAAE;QACnFE,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,OAAOxB,QAAQ;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAuB,YAAY,EAAE,MAAAA,CAAON,SAAS,EAAEG,QAAQ,KAAK;IAC3C,IAAI;MACF,MAAMtB,QAAQ,GAAG,MAAMlB,SAAS,CAACyC,IAAI,CAAC,iBAAiBJ,SAAS,SAAS,EAAEG,QAAQ,EAAE;QACnFE,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,OAAOxB,QAAQ;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAN,aAAa,EAAE,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMlB,SAAS,CAACmB,GAAG,CAAC,0BAA0B,CAAC;MAChE,OAAOD,QAAQ;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD;MACA,OAAO;QACLf,OAAO,EAAE,IAAI;QACbC,IAAI,EAAES;MACR,CAAC;IACH;EACF;AACF,CAAC;;AAED;AACA,MAAM6B,OAAO,GAAG;EACd;EACAC,KAAK,EAAGC,WAAW,IAAK;IACtB;IACA,IAAIA,WAAW,CAACC,KAAK,KAAK,sBAAsB,IAAID,WAAW,CAACE,QAAQ,KAAK,UAAU,EAAE;MACvF,OAAO7C,OAAO,CAACC,OAAO,CAAC;QACrBC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE;UACJ2C,KAAK,EAAE,sBAAsB;UAC7BC,IAAI,EAAE;YACJzC,EAAE,EAAE,CAAC;YACL0C,SAAS,EAAE,OAAO;YAClBC,QAAQ,EAAE,MAAM;YAChBL,KAAK,EAAED,WAAW,CAACC,KAAK;YACxBM,IAAI,EAAE;UACR;QACF;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIP,WAAW,CAACC,KAAK,KAAK,wBAAwB,IAAID,WAAW,CAACE,QAAQ,KAAK,UAAU,EAAE;MACzF,OAAO7C,OAAO,CAACC,OAAO,CAAC;QACrBC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE;UACJ2C,KAAK,EAAE,yBAAyB;UAChCC,IAAI,EAAE;YACJzC,EAAE,EAAE,CAAC;YACL0C,SAAS,EAAE,SAAS;YACpBC,QAAQ,EAAE,MAAM;YAChBL,KAAK,EAAED,WAAW,CAACC,KAAK;YACxBM,IAAI,EAAE;UACR;QACF;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,OAAOlD,OAAO,CAACC,OAAO,CAAC;MACrBC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;QACJ2C,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE;UACJzC,EAAE,EAAE,CAAC;UACL0C,SAAS,EAAE,MAAM;UACjBC,QAAQ,EAAE,MAAM;UAChBL,KAAK,EAAED,WAAW,CAACC,KAAK;UACxBM,IAAI,EAAE;QACR;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACAC,QAAQ,EAAGC,QAAQ,IAAK;IACtB,OAAOpD,OAAO,CAACC,OAAO,CAAC;MACrBC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;QACJ2C,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE;UACJzC,EAAE,EAAE,CAAC;UACL+C,IAAI,EAAED,QAAQ,CAACC,IAAI;UACnBT,KAAK,EAAEQ,QAAQ,CAACR;QAClB;MACF;IACF,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,MAAMU,WAAW,GAAG;EAClB,GAAGxD,GAAG;EACN,GAAG2C;AACL,CAAC;AAED,eAAea,WAAW;AAC1B,SAASzC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}