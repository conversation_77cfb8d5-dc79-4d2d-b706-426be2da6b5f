{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\product\\\\ProductCard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useMemo, useCallback } from 'react';\nimport { Link } from 'react-router-dom';\nimport PropTypes from 'prop-types';\nimport { useCart } from '../../contexts/CartContext';\nimport { usePrice } from '../../hooks/usePrice';\nimport CheckoutModal from '../cart/CheckoutModal';\nimport { CONFIGURATION_OPTIONS, PRODUCT_CONFIG } from '../../constants';\nimport { supportsAdvanced3D, calculateDiscountPercentage } from '../../utils';\n\n/**\n * ProductCard component displays a product with image, details, pricing, and quick actions\n * @param {Object} product - Product object containing all product information\n * @param {string|number} product.id - Unique product identifier\n * @param {string} product.name - Product name\n * @param {number} product.price - Base product price\n * @param {number} [product.discountPrice] - Discounted price if applicable\n * @param {string[]} [product.images] - Array of product image URLs\n * @param {string} [product.categoryName] - Product category name\n * @param {boolean} [product.featured] - Whether product is featured\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductCard = ({\n  product\n}) => {\n  _s();\n  const {\n    addToCart\n  } = useCart();\n  const {\n    formatSinglePrice,\n    formatPriceWithDiscount\n  } = usePrice();\n  const [showCheckoutModal, setShowCheckoutModal] = useState(false);\n  const [quickConfig, setQuickConfig] = useState({\n    color: 'default',\n    material: 'default',\n    size: 'standard'\n  });\n  const {\n    id,\n    name,\n    price,\n    discountPrice,\n    images,\n    categoryName,\n    featured\n  } = product;\n\n  // Memoize price calculation to avoid recalculation on every render\n  const calculateConfiguredPrice = useCallback((basePrice, config) => {\n    let multiplier = 1;\n\n    // Apply configuration multipliers from constants\n    const colorConfig = CONFIGURATION_OPTIONS.COLORS[config.color];\n    const materialConfig = CONFIGURATION_OPTIONS.MATERIALS[config.material];\n    const sizeConfig = CONFIGURATION_OPTIONS.SIZES[config.size];\n    if (colorConfig) multiplier *= colorConfig.multiplier;\n    if (materialConfig) multiplier *= materialConfig.multiplier;\n    if (sizeConfig) multiplier *= sizeConfig.multiplier;\n    return basePrice * multiplier;\n  }, []);\n\n  // Memoize expensive calculations\n  const productData = useMemo(() => {\n    const baseDisplayPrice = discountPrice || price;\n    const configuredPrice = calculateConfiguredPrice(baseDisplayPrice, quickConfig);\n    const hasDiscount = discountPrice && discountPrice < price;\n    const hasConfiguration = quickConfig.color !== 'default' || quickConfig.material !== 'default' || quickConfig.size !== 'standard';\n    const primaryImage = images && images.length > 0 ? images[0] : PRODUCT_CONFIG.DEFAULT_IMAGE;\n    return {\n      baseDisplayPrice,\n      configuredPrice,\n      hasDiscount,\n      hasConfiguration,\n      primaryImage\n    };\n  }, [discountPrice, price, quickConfig, images, calculateConfiguredPrice]);\n\n  // Memoize price formatting\n  const formatPrice = useCallback(price => {\n    return formatSinglePrice(price);\n  }, [formatSinglePrice]);\n  const handleAddToCart = useCallback(e => {\n    e.preventDefault(); // Prevent navigation when clicking add to cart\n\n    if (!product || !product.id) {\n      console.error('ProductCard: Invalid product data');\n      alert('Unable to add item to cart. Product information is missing.');\n      return;\n    }\n    try {\n      // Create product with quick configuration options\n      const configuredProduct = {\n        ...product,\n        quickConfiguration: quickConfig,\n        configuredPrice: calculateConfiguredPrice(product.price, quickConfig)\n      };\n      addToCart(configuredProduct, 1); // Add 1 item with quick config settings\n      setShowCheckoutModal(true); // Show checkout modal\n    } catch (error) {\n      console.error('ProductCard: Failed to add item to cart:', error);\n      alert('Failed to add item to cart. Please try again.');\n    }\n  }, [product, quickConfig, calculateConfiguredPrice, addToCart]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"product-card\",\n    children: [featured && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"featured-badge\",\n      children: \"Featured\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 26\n    }, this), productData.hasDiscount && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"discount-badge\",\n      children: [calculateDiscountPercentage(price, discountPrice), \"% OFF\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(Link, {\n      to: `/product/${id}`,\n      className: \"product-link\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-image\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: productData.primaryImage,\n          alt: name,\n          onError: e => {\n            e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-category\",\n          children: categoryName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"product-name\",\n          children: name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-pricing\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"current-price\",\n            children: formatPrice(productData.hasConfiguration ? productData.configuredPrice : productData.baseDisplayPrice)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 25\n          }, this), productData.hasConfiguration && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"base-price\",\n            children: formatPrice(productData.baseDisplayPrice)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 29\n          }, this), productData.hasDiscount && !productData.hasConfiguration && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"original-price\",\n            children: formatPrice(price)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 29\n          }, this), productData.hasConfiguration && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"config-indicator\",\n            children: \"Configured\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-actions\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: `/product/${id}`,\n        className: \"btn btn-primary btn-compact\",\n        children: \"View Details\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-secondary btn-compact\",\n        onClick: handleAddToCart,\n        children: \"Add to Cart\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"config-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: `config-btn color-btn ${quickConfig.color !== 'default' ? 'active' : ''}`,\n        onClick: e => {\n          e.preventDefault();\n          const colors = ['default', 'dark-walnut', 'cherry', 'black', 'white'];\n          const currentIndex = colors.indexOf(quickConfig.color);\n          const nextColor = colors[(currentIndex + 1) % colors.length];\n          setQuickConfig(prev => ({\n            ...prev,\n            color: nextColor\n          }));\n        },\n        title: `Color: ${quickConfig.color.replace('-', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}`,\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"16\",\n          height: \"16\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M12 2a10 10 0 0 0 0 20 10 10 0 0 1 0-20z\",\n            fill: \"currentColor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `config-btn material-btn ${quickConfig.material !== 'default' ? 'active' : ''}`,\n        onClick: e => {\n          e.preventDefault();\n          const materials = ['default', 'solid-wood', 'engineered-wood', 'metal', 'glass'];\n          const currentIndex = materials.indexOf(quickConfig.material);\n          const nextMaterial = materials[(currentIndex + 1) % materials.length];\n          setQuickConfig(prev => ({\n            ...prev,\n            material: nextMaterial\n          }));\n        },\n        title: `Material: ${quickConfig.material.replace('-', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}`,\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"16\",\n          height: \"16\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n            x: \"3\",\n            y: \"3\",\n            width: \"18\",\n            height: \"18\",\n            rx: \"2\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M9 9h6v6H9z\",\n            fill: \"currentColor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `config-btn size-btn ${quickConfig.size !== 'standard' ? 'active' : ''}`,\n        onClick: e => {\n          e.preventDefault();\n          const sizes = ['standard', 'compact', 'large', 'xl'];\n          const currentIndex = sizes.indexOf(quickConfig.size);\n          const nextSize = sizes[(currentIndex + 1) % sizes.length];\n          setQuickConfig(prev => ({\n            ...prev,\n            size: nextSize\n          }));\n        },\n        title: `Size: ${quickConfig.size.toUpperCase()}`,\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"16\",\n          height: \"16\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: `/product/${id}?configurator=true`,\n        className: \"config-btn full-config-btn\",\n        title: \"Full 3D Configurator\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"16\",\n          height: \"16\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M12 2L2 7L12 12L22 7L12 2Z\",\n            fill: \"currentColor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M2 17L12 22L22 17\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M2 12L12 17L22 12\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(CheckoutModal, {\n      isOpen: showCheckoutModal,\n      onClose: () => setShowCheckoutModal(false),\n      product: product,\n      quantity: 1\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 9\n  }, this);\n};\n\n// PropTypes validation\n_s(ProductCard, \"GHPIJTGPtezl/lm+xhHmefToIH8=\", false, function () {\n  return [useCart, usePrice];\n});\n_c = ProductCard;\nProductCard.propTypes = {\n  product: PropTypes.shape({\n    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,\n    name: PropTypes.string.isRequired,\n    price: PropTypes.number.isRequired,\n    discountPrice: PropTypes.number,\n    images: PropTypes.arrayOf(PropTypes.string),\n    categoryName: PropTypes.string,\n    featured: PropTypes.bool\n  }).isRequired\n};\n\n// Default props\nProductCard.defaultProps = {\n  product: {\n    images: [],\n    categoryName: 'Uncategorized',\n    featured: false\n  }\n};\n\n// Memoize the component to prevent unnecessary re-renders\nexport default _c2 = /*#__PURE__*/React.memo(ProductCard);\nvar _c, _c2;\n$RefreshReg$(_c, \"ProductCard\");\n$RefreshReg$(_c2, \"%default%\");", "map": {"version": 3, "names": ["React", "useState", "useMemo", "useCallback", "Link", "PropTypes", "useCart", "usePrice", "CheckoutModal", "CONFIGURATION_OPTIONS", "PRODUCT_CONFIG", "supportsAdvanced3D", "calculateDiscountPercentage", "jsxDEV", "_jsxDEV", "ProductCard", "product", "_s", "addToCart", "formatSinglePrice", "formatPriceWithDiscount", "showCheckoutModal", "setShowCheckoutModal", "quickConfig", "setQuickConfig", "color", "material", "size", "id", "name", "price", "discountPrice", "images", "categoryName", "featured", "calculateConfiguredPrice", "basePrice", "config", "multiplier", "colorConfig", "COLORS", "materialConfig", "MATERIALS", "sizeConfig", "SIZES", "productData", "baseDisplayPrice", "configuredPrice", "hasDiscount", "hasConfiguration", "primaryImage", "length", "DEFAULT_IMAGE", "formatPrice", "handleAddToCart", "e", "preventDefault", "console", "error", "alert", "configuredProduct", "quickConfiguration", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "src", "alt", "onError", "target", "onClick", "colors", "currentIndex", "indexOf", "nextColor", "prev", "title", "replace", "l", "toUpperCase", "width", "height", "viewBox", "fill", "cx", "cy", "r", "stroke", "strokeWidth", "d", "materials", "nextMaterial", "x", "y", "rx", "sizes", "nextSize", "strokeLinecap", "strokeLinejoin", "isOpen", "onClose", "quantity", "_c", "propTypes", "shape", "oneOfType", "string", "number", "isRequired", "arrayOf", "bool", "defaultProps", "_c2", "memo", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/product/ProductCard.js"], "sourcesContent": ["import React, { useState, useMemo, useCallback } from 'react';\nimport { Link } from 'react-router-dom';\nimport PropTypes from 'prop-types';\nimport { useCart } from '../../contexts/CartContext';\nimport { usePrice } from '../../hooks/usePrice';\nimport CheckoutModal from '../cart/CheckoutModal';\nimport { CONFIGURATION_OPTIONS, PRODUCT_CONFIG } from '../../constants';\nimport { supportsAdvanced3D, calculateDiscountPercentage } from '../../utils';\n\n/**\n * ProductCard component displays a product with image, details, pricing, and quick actions\n * @param {Object} product - Product object containing all product information\n * @param {string|number} product.id - Unique product identifier\n * @param {string} product.name - Product name\n * @param {number} product.price - Base product price\n * @param {number} [product.discountPrice] - Discounted price if applicable\n * @param {string[]} [product.images] - Array of product image URLs\n * @param {string} [product.categoryName] - Product category name\n * @param {boolean} [product.featured] - Whether product is featured\n */\nconst ProductCard = ({ product }) => {\n    const { addToCart } = useCart();\n    const { formatSinglePrice, formatPriceWithDiscount } = usePrice();\n    const [showCheckoutModal, setShowCheckoutModal] = useState(false);\n    const [quickConfig, setQuickConfig] = useState({\n        color: 'default',\n        material: 'default',\n        size: 'standard'\n    });\n    const {\n        id,\n        name,\n        price,\n        discountPrice,\n        images,\n        categoryName,\n        featured\n    } = product;\n\n    // Memoize price calculation to avoid recalculation on every render\n    const calculateConfiguredPrice = useCallback((basePrice, config) => {\n        let multiplier = 1;\n\n        // Apply configuration multipliers from constants\n        const colorConfig = CONFIGURATION_OPTIONS.COLORS[config.color];\n        const materialConfig = CONFIGURATION_OPTIONS.MATERIALS[config.material];\n        const sizeConfig = CONFIGURATION_OPTIONS.SIZES[config.size];\n\n        if (colorConfig) multiplier *= colorConfig.multiplier;\n        if (materialConfig) multiplier *= materialConfig.multiplier;\n        if (sizeConfig) multiplier *= sizeConfig.multiplier;\n\n        return basePrice * multiplier;\n    }, []);\n\n    // Memoize expensive calculations\n    const productData = useMemo(() => {\n        const baseDisplayPrice = discountPrice || price;\n        const configuredPrice = calculateConfiguredPrice(baseDisplayPrice, quickConfig);\n        const hasDiscount = discountPrice && discountPrice < price;\n        const hasConfiguration = quickConfig.color !== 'default' || quickConfig.material !== 'default' || quickConfig.size !== 'standard';\n        const primaryImage = images && images.length > 0 ? images[0] : PRODUCT_CONFIG.DEFAULT_IMAGE;\n\n        return {\n            baseDisplayPrice,\n            configuredPrice,\n            hasDiscount,\n            hasConfiguration,\n            primaryImage\n        };\n    }, [discountPrice, price, quickConfig, images, calculateConfiguredPrice]);\n\n    // Memoize price formatting\n    const formatPrice = useCallback((price) => {\n        return formatSinglePrice(price);\n    }, [formatSinglePrice]);\n\n    const handleAddToCart = useCallback((e) => {\n        e.preventDefault(); // Prevent navigation when clicking add to cart\n\n        if (!product || !product.id) {\n            console.error('ProductCard: Invalid product data');\n            alert('Unable to add item to cart. Product information is missing.');\n            return;\n        }\n\n        try {\n            // Create product with quick configuration options\n            const configuredProduct = {\n                ...product,\n                quickConfiguration: quickConfig,\n                configuredPrice: calculateConfiguredPrice(product.price, quickConfig)\n            };\n            addToCart(configuredProduct, 1); // Add 1 item with quick config settings\n            setShowCheckoutModal(true); // Show checkout modal\n        } catch (error) {\n            console.error('ProductCard: Failed to add item to cart:', error);\n            alert('Failed to add item to cart. Please try again.');\n        }\n    }, [product, quickConfig, calculateConfiguredPrice, addToCart]);\n\n    return (\n        <div className=\"product-card\">\n            {featured && <div className=\"featured-badge\">Featured</div>}\n            {productData.hasDiscount && (\n                <div className=\"discount-badge\">\n                    {calculateDiscountPercentage(price, discountPrice)}% OFF\n                </div>\n            )}\n\n            <Link to={`/product/${id}`} className=\"product-link\">\n                <div className=\"product-image\">\n                    <img\n                        src={productData.primaryImage}\n                        alt={name}\n                        onError={(e) => {\n                            e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop';\n                        }}\n                    />\n                </div>\n\n                <div className=\"product-info\">\n                    <div className=\"product-category\">{categoryName}</div>\n                    <h3 className=\"product-name\">{name}</h3>\n\n                    <div className=\"product-pricing\">\n                        <span className=\"current-price\">\n                            {formatPrice(productData.hasConfiguration ? productData.configuredPrice : productData.baseDisplayPrice)}\n                        </span>\n                        {productData.hasConfiguration && (\n                            <span className=\"base-price\">{formatPrice(productData.baseDisplayPrice)}</span>\n                        )}\n                        {productData.hasDiscount && !productData.hasConfiguration && (\n                            <span className=\"original-price\">{formatPrice(price)}</span>\n                        )}\n                        {productData.hasConfiguration && (\n                            <span className=\"config-indicator\">Configured</span>\n                        )}\n                    </div>\n                </div>\n            </Link>\n\n            <div className=\"product-actions\">\n                <Link to={`/product/${id}`} className=\"btn btn-primary btn-compact\">\n                    View Details\n                </Link>\n                <button\n                    className=\"btn btn-secondary btn-compact\"\n                    onClick={handleAddToCart}\n                >\n                    Add to Cart\n                </button>\n            </div>\n\n            {/* 3D Configuration Quick Actions */}\n            <div className=\"config-actions\">\n                <button\n                    className={`config-btn color-btn ${quickConfig.color !== 'default' ? 'active' : ''}`}\n                    onClick={(e) => {\n                        e.preventDefault();\n                        const colors = ['default', 'dark-walnut', 'cherry', 'black', 'white'];\n                        const currentIndex = colors.indexOf(quickConfig.color);\n                        const nextColor = colors[(currentIndex + 1) % colors.length];\n                        setQuickConfig(prev => ({ ...prev, color: nextColor }));\n                    }}\n                    title={`Color: ${quickConfig.color.replace('-', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}`}\n                >\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                        <path d=\"M12 2a10 10 0 0 0 0 20 10 10 0 0 1 0-20z\" fill=\"currentColor\"/>\n                    </svg>\n                </button>\n\n                <button\n                    className={`config-btn material-btn ${quickConfig.material !== 'default' ? 'active' : ''}`}\n                    onClick={(e) => {\n                        e.preventDefault();\n                        const materials = ['default', 'solid-wood', 'engineered-wood', 'metal', 'glass'];\n                        const currentIndex = materials.indexOf(quickConfig.material);\n                        const nextMaterial = materials[(currentIndex + 1) % materials.length];\n                        setQuickConfig(prev => ({ ...prev, material: nextMaterial }));\n                    }}\n                    title={`Material: ${quickConfig.material.replace('-', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}`}\n                >\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                        <path d=\"M9 9h6v6H9z\" fill=\"currentColor\"/>\n                    </svg>\n                </button>\n\n                <button\n                    className={`config-btn size-btn ${quickConfig.size !== 'standard' ? 'active' : ''}`}\n                    onClick={(e) => {\n                        e.preventDefault();\n                        const sizes = ['standard', 'compact', 'large', 'xl'];\n                        const currentIndex = sizes.indexOf(quickConfig.size);\n                        const nextSize = sizes[(currentIndex + 1) % sizes.length];\n                        setQuickConfig(prev => ({ ...prev, size: nextSize }));\n                    }}\n                    title={`Size: ${quickConfig.size.toUpperCase()}`}\n                >\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                </button>\n\n                <Link\n                    to={`/product/${id}?configurator=true`}\n                    className=\"config-btn full-config-btn\"\n                    title=\"Full 3D Configurator\"\n                >\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M12 2L2 7L12 12L22 7L12 2Z\" fill=\"currentColor\"/>\n                        <path d=\"M2 17L12 22L22 17\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <path d=\"M2 12L12 17L22 12\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                </Link>\n            </div>\n\n            {/* Checkout Modal */}\n            <CheckoutModal\n                isOpen={showCheckoutModal}\n                onClose={() => setShowCheckoutModal(false)}\n                product={product}\n                quantity={1}\n            />\n        </div>\n    );\n};\n\n// PropTypes validation\nProductCard.propTypes = {\n    product: PropTypes.shape({\n        id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,\n        name: PropTypes.string.isRequired,\n        price: PropTypes.number.isRequired,\n        discountPrice: PropTypes.number,\n        images: PropTypes.arrayOf(PropTypes.string),\n        categoryName: PropTypes.string,\n        featured: PropTypes.bool\n    }).isRequired\n};\n\n// Default props\nProductCard.defaultProps = {\n    product: {\n        images: [],\n        categoryName: 'Uncategorized',\n        featured: false\n    }\n};\n\n// Memoize the component to prevent unnecessary re-renders\nexport default React.memo(ProductCard);\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AAC7D,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,OAAOC,aAAa,MAAM,uBAAuB;AACjD,SAASC,qBAAqB,EAAEC,cAAc,QAAQ,iBAAiB;AACvE,SAASC,kBAAkB,EAAEC,2BAA2B,QAAQ,aAAa;;AAE7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA,SAAAC,MAAA,IAAAC,OAAA;AAWA,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjC,MAAM;IAAEC;EAAU,CAAC,GAAGZ,OAAO,CAAC,CAAC;EAC/B,MAAM;IAAEa,iBAAiB;IAAEC;EAAwB,CAAC,GAAGb,QAAQ,CAAC,CAAC;EACjE,MAAM,CAACc,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC;IAC3CwB,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE;EACV,CAAC,CAAC;EACF,MAAM;IACFC,EAAE;IACFC,IAAI;IACJC,KAAK;IACLC,aAAa;IACbC,MAAM;IACNC,YAAY;IACZC;EACJ,CAAC,GAAGlB,OAAO;;EAEX;EACA,MAAMmB,wBAAwB,GAAGhC,WAAW,CAAC,CAACiC,SAAS,EAAEC,MAAM,KAAK;IAChE,IAAIC,UAAU,GAAG,CAAC;;IAElB;IACA,MAAMC,WAAW,GAAG9B,qBAAqB,CAAC+B,MAAM,CAACH,MAAM,CAACZ,KAAK,CAAC;IAC9D,MAAMgB,cAAc,GAAGhC,qBAAqB,CAACiC,SAAS,CAACL,MAAM,CAACX,QAAQ,CAAC;IACvE,MAAMiB,UAAU,GAAGlC,qBAAqB,CAACmC,KAAK,CAACP,MAAM,CAACV,IAAI,CAAC;IAE3D,IAAIY,WAAW,EAAED,UAAU,IAAIC,WAAW,CAACD,UAAU;IACrD,IAAIG,cAAc,EAAEH,UAAU,IAAIG,cAAc,CAACH,UAAU;IAC3D,IAAIK,UAAU,EAAEL,UAAU,IAAIK,UAAU,CAACL,UAAU;IAEnD,OAAOF,SAAS,GAAGE,UAAU;EACjC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,WAAW,GAAG3C,OAAO,CAAC,MAAM;IAC9B,MAAM4C,gBAAgB,GAAGf,aAAa,IAAID,KAAK;IAC/C,MAAMiB,eAAe,GAAGZ,wBAAwB,CAACW,gBAAgB,EAAEvB,WAAW,CAAC;IAC/E,MAAMyB,WAAW,GAAGjB,aAAa,IAAIA,aAAa,GAAGD,KAAK;IAC1D,MAAMmB,gBAAgB,GAAG1B,WAAW,CAACE,KAAK,KAAK,SAAS,IAAIF,WAAW,CAACG,QAAQ,KAAK,SAAS,IAAIH,WAAW,CAACI,IAAI,KAAK,UAAU;IACjI,MAAMuB,YAAY,GAAGlB,MAAM,IAAIA,MAAM,CAACmB,MAAM,GAAG,CAAC,GAAGnB,MAAM,CAAC,CAAC,CAAC,GAAGtB,cAAc,CAAC0C,aAAa;IAE3F,OAAO;MACHN,gBAAgB;MAChBC,eAAe;MACfC,WAAW;MACXC,gBAAgB;MAChBC;IACJ,CAAC;EACL,CAAC,EAAE,CAACnB,aAAa,EAAED,KAAK,EAAEP,WAAW,EAAES,MAAM,EAAEG,wBAAwB,CAAC,CAAC;;EAEzE;EACA,MAAMkB,WAAW,GAAGlD,WAAW,CAAE2B,KAAK,IAAK;IACvC,OAAOX,iBAAiB,CAACW,KAAK,CAAC;EACnC,CAAC,EAAE,CAACX,iBAAiB,CAAC,CAAC;EAEvB,MAAMmC,eAAe,GAAGnD,WAAW,CAAEoD,CAAC,IAAK;IACvCA,CAAC,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC;;IAEpB,IAAI,CAACxC,OAAO,IAAI,CAACA,OAAO,CAACY,EAAE,EAAE;MACzB6B,OAAO,CAACC,KAAK,CAAC,mCAAmC,CAAC;MAClDC,KAAK,CAAC,6DAA6D,CAAC;MACpE;IACJ;IAEA,IAAI;MACA;MACA,MAAMC,iBAAiB,GAAG;QACtB,GAAG5C,OAAO;QACV6C,kBAAkB,EAAEtC,WAAW;QAC/BwB,eAAe,EAAEZ,wBAAwB,CAACnB,OAAO,CAACc,KAAK,EAAEP,WAAW;MACxE,CAAC;MACDL,SAAS,CAAC0C,iBAAiB,EAAE,CAAC,CAAC,CAAC,CAAC;MACjCtC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC,OAAOoC,KAAK,EAAE;MACZD,OAAO,CAACC,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChEC,KAAK,CAAC,+CAA+C,CAAC;IAC1D;EACJ,CAAC,EAAE,CAAC3C,OAAO,EAAEO,WAAW,EAAEY,wBAAwB,EAAEjB,SAAS,CAAC,CAAC;EAE/D,oBACIJ,OAAA;IAAKgD,SAAS,EAAC,cAAc;IAAAC,QAAA,GACxB7B,QAAQ,iBAAIpB,OAAA;MAAKgD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAAC;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,EAC1DtB,WAAW,CAACG,WAAW,iBACpBlC,OAAA;MAAKgD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,GAC1BnD,2BAA2B,CAACkB,KAAK,EAAEC,aAAa,CAAC,EAAC,OACvD;IAAA;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACR,eAEDrD,OAAA,CAACV,IAAI;MAACgE,EAAE,EAAE,YAAYxC,EAAE,EAAG;MAACkC,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAChDjD,OAAA;QAAKgD,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC1BjD,OAAA;UACIuD,GAAG,EAAExB,WAAW,CAACK,YAAa;UAC9BoB,GAAG,EAAEzC,IAAK;UACV0C,OAAO,EAAGhB,CAAC,IAAK;YACZA,CAAC,CAACiB,MAAM,CAACH,GAAG,GAAG,mFAAmF;UACtG;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENrD,OAAA;QAAKgD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACzBjD,OAAA;UAAKgD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAE9B;QAAY;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtDrD,OAAA;UAAIgD,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAElC;QAAI;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAExCrD,OAAA;UAAKgD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5BjD,OAAA;YAAMgD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC1BV,WAAW,CAACR,WAAW,CAACI,gBAAgB,GAAGJ,WAAW,CAACE,eAAe,GAAGF,WAAW,CAACC,gBAAgB;UAAC;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrG,CAAC,EACNtB,WAAW,CAACI,gBAAgB,iBACzBnC,OAAA;YAAMgD,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEV,WAAW,CAACR,WAAW,CAACC,gBAAgB;UAAC;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACjF,EACAtB,WAAW,CAACG,WAAW,IAAI,CAACH,WAAW,CAACI,gBAAgB,iBACrDnC,OAAA;YAAMgD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAEV,WAAW,CAACvB,KAAK;UAAC;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAC9D,EACAtB,WAAW,CAACI,gBAAgB,iBACzBnC,OAAA;YAAMgD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACtD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEPrD,OAAA;MAAKgD,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5BjD,OAAA,CAACV,IAAI;QAACgE,EAAE,EAAE,YAAYxC,EAAE,EAAG;QAACkC,SAAS,EAAC,6BAA6B;QAAAC,QAAA,EAAC;MAEpE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACPrD,OAAA;QACIgD,SAAS,EAAC,+BAA+B;QACzCW,OAAO,EAAEnB,eAAgB;QAAAS,QAAA,EAC5B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGNrD,OAAA;MAAKgD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC3BjD,OAAA;QACIgD,SAAS,EAAE,wBAAwBvC,WAAW,CAACE,KAAK,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;QACrFgD,OAAO,EAAGlB,CAAC,IAAK;UACZA,CAAC,CAACC,cAAc,CAAC,CAAC;UAClB,MAAMkB,MAAM,GAAG,CAAC,SAAS,EAAE,aAAa,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC;UACrE,MAAMC,YAAY,GAAGD,MAAM,CAACE,OAAO,CAACrD,WAAW,CAACE,KAAK,CAAC;UACtD,MAAMoD,SAAS,GAAGH,MAAM,CAAC,CAACC,YAAY,GAAG,CAAC,IAAID,MAAM,CAACvB,MAAM,CAAC;UAC5D3B,cAAc,CAACsD,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAErD,KAAK,EAAEoD;UAAU,CAAC,CAAC,CAAC;QAC3D,CAAE;QACFE,KAAK,EAAE,UAAUxD,WAAW,CAACE,KAAK,CAACuD,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAEC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,EAAG;QAAAnB,QAAA,eAE9FjD,OAAA;UAAKqE,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAAAvB,QAAA,gBACvDjD,OAAA;YAAQyE,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,CAAC,EAAC,IAAI;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC;UAAG;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eACtErD,OAAA;YAAM8E,CAAC,EAAC,0CAA0C;YAACN,IAAI,EAAC;UAAc;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAETrD,OAAA;QACIgD,SAAS,EAAE,2BAA2BvC,WAAW,CAACG,QAAQ,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;QAC3F+C,OAAO,EAAGlB,CAAC,IAAK;UACZA,CAAC,CAACC,cAAc,CAAC,CAAC;UAClB,MAAMqC,SAAS,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,iBAAiB,EAAE,OAAO,EAAE,OAAO,CAAC;UAChF,MAAMlB,YAAY,GAAGkB,SAAS,CAACjB,OAAO,CAACrD,WAAW,CAACG,QAAQ,CAAC;UAC5D,MAAMoE,YAAY,GAAGD,SAAS,CAAC,CAAClB,YAAY,GAAG,CAAC,IAAIkB,SAAS,CAAC1C,MAAM,CAAC;UACrE3B,cAAc,CAACsD,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEpD,QAAQ,EAAEoE;UAAa,CAAC,CAAC,CAAC;QACjE,CAAE;QACFf,KAAK,EAAE,aAAaxD,WAAW,CAACG,QAAQ,CAACsD,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAEC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,EAAG;QAAAnB,QAAA,eAEpGjD,OAAA;UAAKqE,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAAAvB,QAAA,gBACvDjD,OAAA;YAAMiF,CAAC,EAAC,GAAG;YAACC,CAAC,EAAC,GAAG;YAACb,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACa,EAAE,EAAC,GAAG;YAACP,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC;UAAG;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eACvFrD,OAAA;YAAM8E,CAAC,EAAC,aAAa;YAACN,IAAI,EAAC;UAAc;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAETrD,OAAA;QACIgD,SAAS,EAAE,uBAAuBvC,WAAW,CAACI,IAAI,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;QACpF8C,OAAO,EAAGlB,CAAC,IAAK;UACZA,CAAC,CAACC,cAAc,CAAC,CAAC;UAClB,MAAM0C,KAAK,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC;UACpD,MAAMvB,YAAY,GAAGuB,KAAK,CAACtB,OAAO,CAACrD,WAAW,CAACI,IAAI,CAAC;UACpD,MAAMwE,QAAQ,GAAGD,KAAK,CAAC,CAACvB,YAAY,GAAG,CAAC,IAAIuB,KAAK,CAAC/C,MAAM,CAAC;UACzD3B,cAAc,CAACsD,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEnD,IAAI,EAAEwE;UAAS,CAAC,CAAC,CAAC;QACzD,CAAE;QACFpB,KAAK,EAAE,SAASxD,WAAW,CAACI,IAAI,CAACuD,WAAW,CAAC,CAAC,EAAG;QAAAnB,QAAA,eAEjDjD,OAAA;UAAKqE,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAAAvB,QAAA,eACvDjD,OAAA;YAAM8E,CAAC,EAAC,wCAAwC;YAACF,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACS,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC;UAAO;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAETrD,OAAA,CAACV,IAAI;QACDgE,EAAE,EAAE,YAAYxC,EAAE,oBAAqB;QACvCkC,SAAS,EAAC,4BAA4B;QACtCiB,KAAK,EAAC,sBAAsB;QAAAhB,QAAA,eAE5BjD,OAAA;UAAKqE,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAAAvB,QAAA,gBACvDjD,OAAA;YAAM8E,CAAC,EAAC,4BAA4B;YAACN,IAAI,EAAC;UAAc;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eAC1DrD,OAAA;YAAM8E,CAAC,EAAC,mBAAmB;YAACF,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACS,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC;UAAO;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eAChHrD,OAAA;YAAM8E,CAAC,EAAC,mBAAmB;YAACF,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACS,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC;UAAO;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/G;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNrD,OAAA,CAACN,aAAa;MACV8F,MAAM,EAAEjF,iBAAkB;MAC1BkF,OAAO,EAAEA,CAAA,KAAMjF,oBAAoB,CAAC,KAAK,CAAE;MAC3CN,OAAO,EAAEA,OAAQ;MACjBwF,QAAQ,EAAE;IAAE;MAAAxC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;;AAED;AAAAlD,EAAA,CAlNMF,WAAW;EAAA,QACST,OAAO,EAC0BC,QAAQ;AAAA;AAAAkG,EAAA,GAF7D1F,WAAW;AAmNjBA,WAAW,CAAC2F,SAAS,GAAG;EACpB1F,OAAO,EAAEX,SAAS,CAACsG,KAAK,CAAC;IACrB/E,EAAE,EAAEvB,SAAS,CAACuG,SAAS,CAAC,CAACvG,SAAS,CAACwG,MAAM,EAAExG,SAAS,CAACyG,MAAM,CAAC,CAAC,CAACC,UAAU;IACxElF,IAAI,EAAExB,SAAS,CAACwG,MAAM,CAACE,UAAU;IACjCjF,KAAK,EAAEzB,SAAS,CAACyG,MAAM,CAACC,UAAU;IAClChF,aAAa,EAAE1B,SAAS,CAACyG,MAAM;IAC/B9E,MAAM,EAAE3B,SAAS,CAAC2G,OAAO,CAAC3G,SAAS,CAACwG,MAAM,CAAC;IAC3C5E,YAAY,EAAE5B,SAAS,CAACwG,MAAM;IAC9B3E,QAAQ,EAAE7B,SAAS,CAAC4G;EACxB,CAAC,CAAC,CAACF;AACP,CAAC;;AAED;AACAhG,WAAW,CAACmG,YAAY,GAAG;EACvBlG,OAAO,EAAE;IACLgB,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,eAAe;IAC7BC,QAAQ,EAAE;EACd;AACJ,CAAC;;AAED;AACA,eAAAiF,GAAA,gBAAenH,KAAK,CAACoH,IAAI,CAACrG,WAAW,CAAC;AAAC,IAAA0F,EAAA,EAAAU,GAAA;AAAAE,YAAA,CAAAZ,EAAA;AAAAY,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}