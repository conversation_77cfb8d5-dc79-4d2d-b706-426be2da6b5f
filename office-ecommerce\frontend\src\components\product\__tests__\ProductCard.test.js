import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { CartProvider } from '../../../contexts/CartContext';
import { PriceProvider } from '../../../hooks/usePrice';
import ProductCard from '../ProductCard';

// Mock the CheckoutModal component
jest.mock('../../cart/CheckoutModal', () => {
  return function MockCheckoutModal({ isOpen, onClose }) {
    return isOpen ? (
      <div data-testid="checkout-modal">
        <button onClick={onClose}>Close Modal</button>
      </div>
    ) : null;
  };
});

// Mock the usePrice hook
jest.mock('../../../hooks/usePrice', () => ({
  usePrice: () => ({
    formatSinglePrice: (price) => `$${price.toFixed(2)}`,
    formatPriceWithDiscount: (price, discount) => ({
      original: `$${price.toFixed(2)}`,
      discounted: `$${discount.toFixed(2)}`
    })
  }),
  PriceProvider: ({ children }) => children
}));

// Test wrapper component
const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <CartProvider>
      <PriceProvider>
        {children}
      </PriceProvider>
    </CartProvider>
  </BrowserRouter>
);

describe('ProductCard', () => {
  const mockProduct = {
    id: 1,
    name: 'Executive Office Chair',
    price: 299.99,
    images: ['chair1.jpg', 'chair2.jpg'],
    categoryName: 'Office Chairs',
    featured: false
  };

  const mockProductWithDiscount = {
    ...mockProduct,
    discountPrice: 249.99
  };

  const mockFeaturedProduct = {
    ...mockProduct,
    featured: true
  };

  beforeEach(() => {
    // Clear any previous localStorage
    localStorage.clear();
  });

  test('renders product information correctly', () => {
    render(
      <TestWrapper>
        <ProductCard product={mockProduct} />
      </TestWrapper>
    );

    expect(screen.getByText('Executive Office Chair')).toBeInTheDocument();
    expect(screen.getByText('Office Chairs')).toBeInTheDocument();
    expect(screen.getByText('$299.99')).toBeInTheDocument();
  });

  test('displays featured badge for featured products', () => {
    render(
      <TestWrapper>
        <ProductCard product={mockFeaturedProduct} />
      </TestWrapper>
    );

    expect(screen.getByText('Featured')).toBeInTheDocument();
  });

  test('displays discount badge and pricing for discounted products', () => {
    render(
      <TestWrapper>
        <ProductCard product={mockProductWithDiscount} />
      </TestWrapper>
    );

    expect(screen.getByText('17% OFF')).toBeInTheDocument();
    expect(screen.getByText('$249.99')).toBeInTheDocument();
  });

  test('renders product image with fallback', () => {
    render(
      <TestWrapper>
        <ProductCard product={mockProduct} />
      </TestWrapper>
    );

    const image = screen.getByAltText('Executive Office Chair');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', 'chair1.jpg');

    // Test fallback image on error
    fireEvent.error(image);
    expect(image).toHaveAttribute('src', expect.stringContaining('unsplash'));
  });

  test('handles product with no images', () => {
    const productWithoutImages = { ...mockProduct, images: [] };
    
    render(
      <TestWrapper>
        <ProductCard product={productWithoutImages} />
      </TestWrapper>
    );

    const image = screen.getByAltText('Executive Office Chair');
    expect(image).toHaveAttribute('src', expect.stringContaining('unsplash'));
  });

  test('navigates to product detail page when clicked', () => {
    render(
      <TestWrapper>
        <ProductCard product={mockProduct} />
      </TestWrapper>
    );

    const productLink = screen.getByRole('link', { name: /executive office chair/i });
    expect(productLink).toHaveAttribute('href', '/product/1');
  });

  test('opens checkout modal when add to cart is clicked', async () => {
    render(
      <TestWrapper>
        <ProductCard product={mockProduct} />
      </TestWrapper>
    );

    const addToCartButton = screen.getByRole('button', { name: /add to cart/i });
    fireEvent.click(addToCartButton);

    await waitFor(() => {
      expect(screen.getByTestId('checkout-modal')).toBeInTheDocument();
    });
  });

  test('closes checkout modal when close button is clicked', async () => {
    render(
      <TestWrapper>
        <ProductCard product={mockProduct} />
      </TestWrapper>
    );

    // Open modal
    const addToCartButton = screen.getByRole('button', { name: /add to cart/i });
    fireEvent.click(addToCartButton);

    await waitFor(() => {
      expect(screen.getByTestId('checkout-modal')).toBeInTheDocument();
    });

    // Close modal
    const closeButton = screen.getByText('Close Modal');
    fireEvent.click(closeButton);

    await waitFor(() => {
      expect(screen.queryByTestId('checkout-modal')).not.toBeInTheDocument();
    });
  });

  test('updates configured price when quick configuration changes', () => {
    render(
      <TestWrapper>
        <ProductCard product={mockProduct} />
      </TestWrapper>
    );

    // Initially shows base price
    expect(screen.getByText('$299.99')).toBeInTheDocument();

    // Change color configuration (this would require the component to expose config controls)
    // This test would need to be updated based on actual implementation
  });

  test('handles add to cart error gracefully', async () => {
    // Mock console.error to avoid error output in tests
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    // Mock alert
    const alertSpy = jest.spyOn(window, 'alert').mockImplementation(() => {});

    const invalidProduct = { ...mockProduct, id: null };
    
    render(
      <TestWrapper>
        <ProductCard product={invalidProduct} />
      </TestWrapper>
    );

    const addToCartButton = screen.getByRole('button', { name: /add to cart/i });
    fireEvent.click(addToCartButton);

    await waitFor(() => {
      expect(alertSpy).toHaveBeenCalledWith(
        'Unable to add item to cart. Product information is missing.'
      );
    });

    consoleSpy.mockRestore();
    alertSpy.mockRestore();
  });

  test('displays view details button with correct link', () => {
    render(
      <TestWrapper>
        <ProductCard product={mockProduct} />
      </TestWrapper>
    );

    const viewDetailsButton = screen.getByRole('link', { name: /view details/i });
    expect(viewDetailsButton).toHaveAttribute('href', '/product/1');
  });

  test('shows configuration indicator when product is configured', () => {
    // This test would need to be implemented based on the actual configuration UI
    // For now, it's a placeholder for future implementation
    expect(true).toBe(true);
  });

  test('calculates discount percentage correctly', () => {
    render(
      <TestWrapper>
        <ProductCard product={mockProductWithDiscount} />
      </TestWrapper>
    );

    // 17% discount: (299.99 - 249.99) / 299.99 * 100 = 16.67% rounded to 17%
    expect(screen.getByText('17% OFF')).toBeInTheDocument();
  });

  test('handles missing product data gracefully', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    expect(() => {
      render(
        <TestWrapper>
          <ProductCard product={null} />
        </TestWrapper>
      );
    }).not.toThrow();

    consoleSpy.mockRestore();
  });

  test('applies correct CSS classes', () => {
    render(
      <TestWrapper>
        <ProductCard product={mockProduct} />
      </TestWrapper>
    );

    const productCard = screen.getByText('Executive Office Chair').closest('.product-card');
    expect(productCard).toHaveClass('product-card');
  });

  test('prevents navigation when add to cart button is clicked', async () => {
    render(
      <TestWrapper>
        <ProductCard product={mockProduct} />
      </TestWrapper>
    );

    const addToCartButton = screen.getByRole('button', { name: /add to cart/i });
    
    // Mock preventDefault
    const mockPreventDefault = jest.fn();
    
    fireEvent.click(addToCartButton, {
      preventDefault: mockPreventDefault
    });

    // The actual preventDefault call would be tested in the component implementation
    // This is more of an integration test
  });
});

// Test utilities
export const createMockProduct = (overrides = {}) => ({
  id: 1,
  name: 'Test Product',
  price: 100,
  images: ['test.jpg'],
  categoryName: 'Test Category',
  featured: false,
  ...overrides
});

export const renderWithProviders = (component) => {
  return render(
    <TestWrapper>
      {component}
    </TestWrapper>
  );
};
