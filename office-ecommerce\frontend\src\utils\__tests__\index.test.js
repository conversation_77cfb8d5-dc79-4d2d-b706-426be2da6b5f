import {
  formatPrice,
  isValidEmail,
  isValidPhone,
  validatePassword,
  debounce,
  throttle,
  deepClone,
  generateId,
  capitalize,
  camelToTitle,
  supportsAdvanced3D,
  calculateDiscountPercentage,
  formatDate,
  truncateText,
  getQueryParam,
  setQueryParam
} from '../index';

describe('Utility Functions', () => {
  
  describe('formatPrice', () => {
    test('formats price correctly with default currency', () => {
      expect(formatPrice(29.99)).toBe('$29.99');
      expect(formatPrice(100)).toBe('$100.00');
      expect(formatPrice(0)).toBe('$0.00');
    });

    test('handles invalid input gracefully', () => {
      expect(formatPrice(null)).toBe('$0.00');
      expect(formatPrice(undefined)).toBe('$0.00');
      expect(formatPrice('invalid')).toBe('$0.00');
      expect(formatPrice(NaN)).toBe('$0.00');
    });

    test('formats price with different currency', () => {
      expect(formatPrice(29.99, 'EUR')).toBe('€29.99');
    });
  });

  describe('isValidEmail', () => {
    test('validates correct email addresses', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
    });

    test('rejects invalid email addresses', () => {
      expect(isValidEmail('invalid-email')).toBe(false);
      expect(isValidEmail('test@')).toBe(false);
      expect(isValidEmail('@example.com')).toBe(false);
      expect(isValidEmail('')).toBe(false);
      expect(isValidEmail(null)).toBe(false);
      expect(isValidEmail(undefined)).toBe(false);
    });
  });

  describe('isValidPhone', () => {
    test('validates correct phone numbers', () => {
      expect(isValidPhone('+1234567890')).toBe(true);
      expect(isValidPhone('1234567890')).toBe(true);
      expect(isValidPhone('+44123456789')).toBe(true);
    });

    test('rejects invalid phone numbers', () => {
      expect(isValidPhone('abc123')).toBe(false);
      expect(isValidPhone('123')).toBe(false);
      expect(isValidPhone('')).toBe(false);
      expect(isValidPhone(null)).toBe(false);
    });
  });

  describe('validatePassword', () => {
    test('validates strong passwords', () => {
      const result = validatePassword('StrongPass123');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('rejects weak passwords', () => {
      const result = validatePassword('weak');
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('provides specific error messages', () => {
      const result = validatePassword('short');
      expect(result.errors).toContain('Password must be at least 8 characters long');
      expect(result.errors).toContain('Password must contain at least one uppercase letter');
      expect(result.errors).toContain('Password must contain at least one number');
    });
  });

  describe('debounce', () => {
    jest.useFakeTimers();

    test('delays function execution', () => {
      const mockFn = jest.fn();
      const debouncedFn = debounce(mockFn, 100);

      debouncedFn();
      expect(mockFn).not.toHaveBeenCalled();

      jest.advanceTimersByTime(100);
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    test('cancels previous calls', () => {
      const mockFn = jest.fn();
      const debouncedFn = debounce(mockFn, 100);

      debouncedFn();
      debouncedFn();
      debouncedFn();

      jest.advanceTimersByTime(100);
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    afterEach(() => {
      jest.clearAllTimers();
    });
  });

  describe('throttle', () => {
    jest.useFakeTimers();

    test('limits function calls', () => {
      const mockFn = jest.fn();
      const throttledFn = throttle(mockFn, 100);

      throttledFn();
      throttledFn();
      throttledFn();

      expect(mockFn).toHaveBeenCalledTimes(1);

      jest.advanceTimersByTime(100);
      throttledFn();
      expect(mockFn).toHaveBeenCalledTimes(2);
    });
  });

  describe('deepClone', () => {
    test('clones primitive values', () => {
      expect(deepClone(42)).toBe(42);
      expect(deepClone('string')).toBe('string');
      expect(deepClone(null)).toBe(null);
    });

    test('clones objects deeply', () => {
      const original = { a: 1, b: { c: 2 } };
      const cloned = deepClone(original);

      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);
      expect(cloned.b).not.toBe(original.b);
    });

    test('clones arrays', () => {
      const original = [1, [2, 3], { a: 4 }];
      const cloned = deepClone(original);

      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);
      expect(cloned[1]).not.toBe(original[1]);
    });

    test('clones dates', () => {
      const original = new Date('2023-01-01');
      const cloned = deepClone(original);

      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);
    });
  });

  describe('generateId', () => {
    test('generates unique IDs', () => {
      const id1 = generateId();
      const id2 = generateId();

      expect(id1).not.toBe(id2);
      expect(typeof id1).toBe('string');
      expect(id1.length).toBeGreaterThan(0);
    });
  });

  describe('capitalize', () => {
    test('capitalizes first letter', () => {
      expect(capitalize('hello')).toBe('Hello');
      expect(capitalize('WORLD')).toBe('World');
      expect(capitalize('tEST')).toBe('Test');
    });

    test('handles edge cases', () => {
      expect(capitalize('')).toBe('');
      expect(capitalize(null)).toBe('');
      expect(capitalize(undefined)).toBe('');
    });
  });

  describe('camelToTitle', () => {
    test('converts camelCase to Title Case', () => {
      expect(camelToTitle('firstName')).toBe('First Name');
      expect(camelToTitle('productCategory')).toBe('Product Category');
      expect(camelToTitle('XMLHttpRequest')).toBe('X M L Http Request');
    });

    test('handles edge cases', () => {
      expect(camelToTitle('')).toBe('');
      expect(camelToTitle('single')).toBe('Single');
    });
  });

  describe('supportsAdvanced3D', () => {
    test('identifies 3D configurable products', () => {
      expect(supportsAdvanced3D({ name: 'Office Table' })).toBe(true);
      expect(supportsAdvanced3D({ name: 'Executive Desk' })).toBe(true);
      expect(supportsAdvanced3D({ name: 'Ergonomic Chair' })).toBe(true);
      expect(supportsAdvanced3D({ name: 'Storage Cabinet' })).toBe(true);
    });

    test('rejects non-configurable products', () => {
      expect(supportsAdvanced3D({ name: 'Office Supplies' })).toBe(false);
      expect(supportsAdvanced3D({ name: 'Pen' })).toBe(false);
      expect(supportsAdvanced3D(null)).toBe(false);
      expect(supportsAdvanced3D({})).toBe(false);
    });
  });

  describe('calculateDiscountPercentage', () => {
    test('calculates discount percentage correctly', () => {
      expect(calculateDiscountPercentage(100, 80)).toBe(20);
      expect(calculateDiscountPercentage(299.99, 249.99)).toBe(17);
      expect(calculateDiscountPercentage(50, 25)).toBe(50);
    });

    test('handles edge cases', () => {
      expect(calculateDiscountPercentage(100, 100)).toBe(0);
      expect(calculateDiscountPercentage(100, 120)).toBe(0);
      expect(calculateDiscountPercentage(0, 0)).toBe(0);
      expect(calculateDiscountPercentage(null, 50)).toBe(0);
    });
  });

  describe('formatDate', () => {
    test('formats dates correctly', () => {
      const date = new Date('2023-12-25');
      const formatted = formatDate(date);
      expect(formatted).toBe('December 25, 2023');
    });

    test('handles string dates', () => {
      const formatted = formatDate('2023-12-25');
      expect(formatted).toBe('December 25, 2023');
    });

    test('handles invalid dates', () => {
      expect(formatDate('invalid')).toBe('');
      expect(formatDate(null)).toBe('');
      expect(formatDate(undefined)).toBe('');
    });

    test('accepts custom options', () => {
      const date = new Date('2023-12-25');
      const formatted = formatDate(date, { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
      });
      expect(formatted).toBe('Dec 25, 2023');
    });
  });

  describe('truncateText', () => {
    test('truncates long text', () => {
      const longText = 'This is a very long text that should be truncated';
      expect(truncateText(longText, 20)).toBe('This is a very lo...');
    });

    test('preserves short text', () => {
      const shortText = 'Short text';
      expect(truncateText(shortText, 20)).toBe('Short text');
    });

    test('uses custom suffix', () => {
      const text = 'This is a long text';
      expect(truncateText(text, 10, '---')).toBe('This is---');
    });

    test('handles edge cases', () => {
      expect(truncateText('', 10)).toBe('');
      expect(truncateText(null, 10)).toBe('');
      expect(truncateText(undefined, 10)).toBe('');
    });
  });

  describe('URL utilities', () => {
    // Mock window.location for testing
    const mockLocation = {
      search: '?param1=value1&param2=value2'
    };
    
    Object.defineProperty(window, 'location', {
      value: mockLocation,
      writable: true
    });

    test('getQueryParam retrieves URL parameters', () => {
      expect(getQueryParam('param1')).toBe('value1');
      expect(getQueryParam('param2')).toBe('value2');
      expect(getQueryParam('nonexistent')).toBe(null);
    });

    test('setQueryParam updates URL parameters', () => {
      // Mock history.pushState
      const mockPushState = jest.fn();
      Object.defineProperty(window.history, 'pushState', {
        value: mockPushState,
        writable: true
      });

      setQueryParam('newParam', 'newValue');
      expect(mockPushState).toHaveBeenCalled();
    });
  });
});
