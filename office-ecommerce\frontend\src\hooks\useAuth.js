import React, { createContext, useContext, useState, useEffect } from 'react';
import { authService } from '../services/auth';

const AuthContext = createContext();

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};

export const AuthProvider = ({ children }) => {
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);
    const [token, setToken] = useState(localStorage.getItem('token'));

    useEffect(() => {
        const initializeAuth = async () => {
            const savedToken = localStorage.getItem('token');
            const savedUser = localStorage.getItem('user');
            if (savedToken && savedUser) {
                try {
                    const userData = JSON.parse(savedUser);
                    setUser(userData);
                    setToken(savedToken);
                } catch (error) {
                    console.error('Failed to initialize auth:', error);
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                    setToken(null);
                }
            }
            setLoading(false);
        };

        initializeAuth();
    }, []);

    const login = async (email, password) => {
        try {
            const response = await authService.login(email, password);
            console.log('Login response:', response); // Debug log
            console.log('Response structure:', JSON.stringify(response, null, 2)); // More detailed debug

            // The API client returns the response body directly, so response.data contains the actual data
            const { token: newToken, user: userData } = response.data;

            // Normalize user data to match frontend expectations (lowercase properties)
            const normalizedUser = {
                id: userData.UserID,
                email: userData.Email,
                name: userData.Name,
                firstName: userData.FirstName,
                lastName: userData.LastName,
                role: userData.Role, // This is the key property for RBAC
                isActive: userData.IsActive,
                emailVerified: userData.EmailVerified
            };

            localStorage.setItem('token', newToken);
            localStorage.setItem('user', JSON.stringify(normalizedUser));
            setToken(newToken);
            setUser(normalizedUser);

            return { success: true, user: normalizedUser };
        } catch (error) {
            console.error('Login failed:', error);
            return {
                success: false,
                error: error.response?.data?.message || error.message || 'Login failed'
            };
        }
    };

    const register = async (userData) => {
        try {
            const response = await authService.register(userData);
            const { token: newToken, user: newUser } = response;
            
            localStorage.setItem('token', newToken);
            setToken(newToken);
            setUser(newUser);
            
            return { success: true, user: newUser };
        } catch (error) {
            console.error('Registration failed:', error);
            return { 
                success: false, 
                error: error.response?.data?.message || 'Registration failed' 
            };
        }
    };

    const logout = () => {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        setToken(null);
        setUser(null);
    };

    const updateProfile = async (profileData) => {
        try {
            const response = await authService.updateProfile(profileData);
            setUser(response.user);
            return { success: true, user: response.user };
        } catch (error) {
            console.error('Profile update failed:', error);
            return { 
                success: false, 
                error: error.response?.data?.message || 'Profile update failed' 
            };
        }
    };

    const value = {
        user,
        token,
        loading,
        login,
        register,
        logout,
        updateProfile,
        isAuthenticated: !!user
    };

    return (
        <AuthContext.Provider value={value}>
            {children}
        </AuthContext.Provider>
    );
};
