{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\pages\\\\ProductDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { usePara<PERSON>, Link } from 'react-router-dom';\nimport { getProductById } from '../services/products';\nimport { useCart } from '../contexts/CartContext';\nimport CheckoutModal from '../components/cart/CheckoutModal';\nimport Advanced3DConfigurator from '../components/3d/3DConfigurator';\n\n// Product Media Component\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProductMedia = ({\n  images,\n  name,\n  selectedImage,\n  setSelectedImage,\n  isConfigurable3D,\n  setShow3DConfigurator\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"product-media\",\n  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"main-media\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-image\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: images && images[selectedImage] ? images[selectedImage] : 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600',\n        alt: name,\n        onError: e => {\n          e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 17\n      }, this), isConfigurable3D && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"image-overlay\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"configurator-btn\",\n          onClick: () => setShow3DConfigurator(true),\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"16\",\n            height: \"16\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M12 2L2 7L12 12L22 7L12 2Z\",\n              fill: \"currentColor\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M2 17L12 22L22 17\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M2 12L12 17L22 12\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 29\n          }, this), \"3D Configurator\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 9\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"media-thumbnails\",\n    children: images && images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"image-thumbnails\",\n      children: images.map((image, index) => /*#__PURE__*/_jsxDEV(\"img\", {\n        src: image,\n        alt: `${name} ${index + 1}`,\n        className: selectedImage === index ? 'active' : '',\n        onClick: () => setSelectedImage(index),\n        onError: e => {\n          e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=150';\n        }\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 25\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 9\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 10,\n  columnNumber: 5\n}, this);\n\n// Product Info Component\n_c = ProductMedia;\nconst ProductInfo = ({\n  product,\n  formatPrice,\n  displayPrice,\n  hasDiscount,\n  price,\n  quantity,\n  setQuantity,\n  handleAddToCart\n}) => {\n  const {\n    name,\n    categoryName,\n    description,\n    features\n  } = product;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"product-info\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-category\",\n      children: categoryName\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n      children: name\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-pricing\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"current-price\",\n        children: formatPrice(displayPrice)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 17\n      }, this), hasDiscount && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"original-price\",\n          children: formatPrice(price)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"discount-badge\",\n          children: [Math.round((price - displayPrice) / price * 100), \"% OFF\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-description\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 13\n    }, this), features && features.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-features\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Key Features\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n          children: feature\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 29\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"quantity-selector\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"quantity\",\n          children: \"Quantity:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quantity-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setQuantity(Math.max(1, quantity - 1)),\n            disabled: quantity <= 1,\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"quantity\",\n            type: \"number\",\n            min: \"1\",\n            value: quantity,\n            onChange: e => setQuantity(Math.max(1, parseInt(e.target.value) || 1))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setQuantity(quantity + 1),\n            children: \"+\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"add-to-cart-btn\",\n        onClick: handleAddToCart,\n        children: [\"Add to Cart - \", formatPrice(displayPrice * quantity)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 9\n  }, this);\n};\n_c2 = ProductInfo;\nconst ProductDetail = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const {\n    addToCart\n  } = useCart();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [selectedImage, setSelectedImage] = useState(0);\n  const [show3DConfigurator, setShow3DConfigurator] = useState(false);\n  const [showCheckoutModal, setShowCheckoutModal] = useState(false);\n  const [customization, setCustomization] = useState({\n    color: '#6B7280',\n    material: 'wood',\n    dimensions: {\n      width: 120,\n      height: 75,\n      depth: 60\n    }\n  });\n  const [quantity, setQuantity] = useState(1);\n  useEffect(() => {\n    loadProduct();\n  }, [id]);\n  const loadProduct = async () => {\n    try {\n      const response = await getProductById(id);\n      setProduct(response.product);\n    } catch (error) {\n      setError('Failed to load product details');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatPrice = price => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  };\n\n  // Check if product supports advanced 3D configuration\n  const supportsAdvanced3D = product => {\n    if (!product) return false;\n    const productType = product.name.toLowerCase();\n    const configurableTypes = ['table', 'desk', 'chair', 'cabinet', 'storage', 'workstation', 'shelf'];\n    return configurableTypes.some(type => productType.includes(type));\n  };\n\n  // Handle adding to cart with customization\n  const handleAddToCart = () => {\n    if (!product) return;\n    try {\n      addToCart(product, quantity, customization);\n      setShowCheckoutModal(true);\n    } catch (error) {\n      alert('Failed to add item to cart. Please try again.');\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-detail-page\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading\",\n          children: \"Loading product details...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 13\n    }, this);\n  }\n  if (error || !product) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-detail-page\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: [error || 'Product not found', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: \"btn btn-primary\",\n            children: \"Back to Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 13\n    }, this);\n  }\n  const {\n    name,\n    description,\n    price,\n    discountPrice,\n    images,\n    categoryName,\n    specifications\n  } = product;\n  const displayPrice = discountPrice || price;\n  const hasDiscount = discountPrice && discountPrice < price;\n\n  // Check if this product supports advanced 3D configuration\n  const isConfigurable3D = supportsAdvanced3D(product);\n\n  // If 3D configurator is enabled and it's a configurable product, show the configurator\n  if (show3DConfigurator && isConfigurable3D) {\n    return /*#__PURE__*/_jsxDEV(Advanced3DConfigurator, {\n      product: product,\n      onBack: () => setShow3DConfigurator(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 16\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"product-detail-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"breadcrumb\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          children: \"Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 21\n        }, this), \" /\", /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/products\",\n          children: \"Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 21\n        }, this), \" /\", /*#__PURE__*/_jsxDEV(\"span\", {\n          children: name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-detail\",\n        children: [/*#__PURE__*/_jsxDEV(ProductMedia, {\n          images: images,\n          name: name,\n          selectedImage: selectedImage,\n          setSelectedImage: setSelectedImage,\n          isConfigurable3D: isConfigurable3D,\n          setShow3DConfigurator: setShow3DConfigurator\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ProductInfo, {\n          product: product,\n          formatPrice: formatPrice,\n          displayPrice: displayPrice,\n          hasDiscount: hasDiscount,\n          price: price,\n          quantity: quantity,\n          setQuantity: setQuantity,\n          handleAddToCart: handleAddToCart\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"related-products\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Related Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"related-products-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"related-product-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"related-product-image\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300\",\n                alt: \"Related Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"related-product-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Hotel Bed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"$2,299\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"related-product-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"related-product-image\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300\",\n                alt: \"Related Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"related-product-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Conference Table\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"$2,499\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"related-product-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"related-product-image\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300\",\n                alt: \"Related Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"related-product-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Reception Desk\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"$1,899\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(CheckoutModal, {\n      isOpen: showCheckoutModal,\n      onClose: () => setShowCheckoutModal(false),\n      product: product,\n      quantity: quantity\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 302,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 235,\n    columnNumber: 9\n  }, this);\n};\n_s(ProductDetail, \"CUI6LraouZxTbceAwWcnm3lxOYs=\", false, function () {\n  return [useParams, useCart];\n});\n_c3 = ProductDetail;\nexport default ProductDetail;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ProductMedia\");\n$RefreshReg$(_c2, \"ProductInfo\");\n$RefreshReg$(_c3, \"ProductDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Link", "getProductById", "useCart", "CheckoutModal", "Advanced3DConfigurator", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductMedia", "images", "name", "selectedImage", "setSelectedImage", "isConfigurable3D", "setShow3DConfigurator", "className", "children", "src", "alt", "onError", "e", "target", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "width", "height", "viewBox", "fill", "xmlns", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "length", "map", "image", "index", "_c", "ProductInfo", "product", "formatPrice", "displayPrice", "hasDiscount", "price", "quantity", "setQuantity", "handleAddToCart", "categoryName", "description", "features", "Math", "round", "feature", "htmlFor", "type", "max", "disabled", "id", "min", "value", "onChange", "parseInt", "_c2", "ProductDetail", "_s", "addToCart", "setProduct", "loading", "setLoading", "error", "setError", "show3DConfigurator", "showCheckoutModal", "setShowCheckoutModal", "customization", "setCustomization", "color", "material", "dimensions", "depth", "loadProduct", "response", "Intl", "NumberFormat", "style", "currency", "format", "supportsAdvanced3D", "productType", "toLowerCase", "configurableTypes", "some", "includes", "alert", "to", "discountPrice", "specifications", "onBack", "isOpen", "onClose", "_c3", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/pages/ProductDetail.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';\nimport { getProductById } from '../services/products';\nimport { useCart } from '../contexts/CartContext';\nimport CheckoutModal from '../components/cart/CheckoutModal';\nimport Advanced3DConfigurator from '../components/3d/3DConfigurator';\n\n// Product Media Component\nconst ProductMedia = ({ images, name, selectedImage, setSelectedImage, isConfigurable3D, setShow3DConfigurator }) => (\n    <div className=\"product-media\">\n        <div className=\"main-media\">\n            <div className=\"main-image\">\n                <img\n                    src={images && images[selectedImage] ? images[selectedImage] : 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600'}\n                    alt={name}\n                    onError={(e) => {\n                        e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600';\n                    }}\n                />\n                {isConfigurable3D && (\n                    <div className=\"image-overlay\">\n                        <button\n                            className=\"configurator-btn\"\n                            onClick={() => setShow3DConfigurator(true)}\n                        >\n                            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                                <path d=\"M12 2L2 7L12 12L22 7L12 2Z\" fill=\"currentColor\"/>\n                                <path d=\"M2 17L12 22L22 17\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                                <path d=\"M2 12L12 17L22 12\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                            </svg>\n                            3D Configurator\n                        </button>\n                    </div>\n                )}\n            </div>\n        </div>\n\n        <div className=\"media-thumbnails\">\n            {images && images.length > 1 && (\n                <div className=\"image-thumbnails\">\n                    {images.map((image, index) => (\n                        <img\n                            key={index}\n                            src={image}\n                            alt={`${name} ${index + 1}`}\n                            className={selectedImage === index ? 'active' : ''}\n                            onClick={() => setSelectedImage(index)}\n                            onError={(e) => {\n                                e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=150';\n                            }}\n                        />\n                    ))}\n                </div>\n            )}\n        </div>\n    </div>\n);\n\n// Product Info Component\nconst ProductInfo = ({ product, formatPrice, displayPrice, hasDiscount, price, quantity, setQuantity, handleAddToCart }) => {\n    const { name, categoryName, description, features } = product;\n\n    return (\n        <div className=\"product-info\">\n            <div className=\"product-category\">{categoryName}</div>\n            <h1>{name}</h1>\n\n            <div className=\"product-pricing\">\n                <span className=\"current-price\">{formatPrice(displayPrice)}</span>\n                {hasDiscount && (\n                    <>\n                        <span className=\"original-price\">{formatPrice(price)}</span>\n                        <span className=\"discount-badge\">\n                            {Math.round(((price - displayPrice) / price) * 100)}% OFF\n                        </span>\n                    </>\n                )}\n            </div>\n\n            <div className=\"product-description\">\n                <p>{description}</p>\n            </div>\n\n            {features && features.length > 0 && (\n                <div className=\"product-features\">\n                    <h3>Key Features</h3>\n                    <ul>\n                        {features.map((feature, index) => (\n                            <li key={index}>{feature}</li>\n                        ))}\n                    </ul>\n                </div>\n            )}\n\n            <div className=\"product-actions\">\n                <div className=\"quantity-selector\">\n                    <label htmlFor=\"quantity\">Quantity:</label>\n                    <div className=\"quantity-controls\">\n                        <button\n                            type=\"button\"\n                            onClick={() => setQuantity(Math.max(1, quantity - 1))}\n                            disabled={quantity <= 1}\n                        >\n                            -\n                        </button>\n                        <input\n                            id=\"quantity\"\n                            type=\"number\"\n                            min=\"1\"\n                            value={quantity}\n                            onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}\n                        />\n                        <button\n                            type=\"button\"\n                            onClick={() => setQuantity(quantity + 1)}\n                        >\n                            +\n                        </button>\n                    </div>\n                </div>\n\n                <button className=\"add-to-cart-btn\" onClick={handleAddToCart}>\n                    Add to Cart - {formatPrice(displayPrice * quantity)}\n                </button>\n            </div>\n        </div>\n    );\n};\n\nconst ProductDetail = () => {\n    const { id } = useParams();\n    const { addToCart } = useCart();\n    const [product, setProduct] = useState(null);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState('');\n    const [selectedImage, setSelectedImage] = useState(0);\n    const [show3DConfigurator, setShow3DConfigurator] = useState(false);\n    const [showCheckoutModal, setShowCheckoutModal] = useState(false);\n    const [customization, setCustomization] = useState({\n        color: '#6B7280',\n        material: 'wood',\n        dimensions: { width: 120, height: 75, depth: 60 }\n    });\n    const [quantity, setQuantity] = useState(1);\n\n    useEffect(() => {\n        loadProduct();\n    }, [id]);\n\n    const loadProduct = async () => {\n        try {\n            const response = await getProductById(id);\n            setProduct(response.product);\n        } catch (error) {\n            setError('Failed to load product details');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const formatPrice = (price) => {\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: 'USD'\n        }).format(price);\n    };\n\n    // Check if product supports advanced 3D configuration\n    const supportsAdvanced3D = (product) => {\n        if (!product) return false;\n        const productType = product.name.toLowerCase();\n        const configurableTypes = ['table', 'desk', 'chair', 'cabinet', 'storage', 'workstation', 'shelf'];\n        return configurableTypes.some(type => productType.includes(type));\n    };\n\n    // Handle adding to cart with customization\n    const handleAddToCart = () => {\n        if (!product) return;\n\n        try {\n            addToCart(product, quantity, customization);\n            setShowCheckoutModal(true);\n        } catch (error) {\n            alert('Failed to add item to cart. Please try again.');\n        }\n    };\n\n    if (loading) {\n        return (\n            <div className=\"product-detail-page\">\n                <div className=\"container\">\n                    <div className=\"loading\">Loading product details...</div>\n                </div>\n            </div>\n        );\n    }\n\n    if (error || !product) {\n        return (\n            <div className=\"product-detail-page\">\n                <div className=\"container\">\n                    <div className=\"error-message\">\n                        {error || 'Product not found'}\n                        <Link to=\"/products\" className=\"btn btn-primary\">\n                            Back to Products\n                        </Link>\n                    </div>\n                </div>\n            </div>\n        );\n    }\n\n    const {\n        name,\n        description,\n        price,\n        discountPrice,\n        images,\n        categoryName,\n        specifications\n    } = product;\n\n    const displayPrice = discountPrice || price;\n    const hasDiscount = discountPrice && discountPrice < price;\n\n    // Check if this product supports advanced 3D configuration\n    const isConfigurable3D = supportsAdvanced3D(product);\n\n    // If 3D configurator is enabled and it's a configurable product, show the configurator\n    if (show3DConfigurator && isConfigurable3D) {\n        return <Advanced3DConfigurator product={product} onBack={() => setShow3DConfigurator(false)} />;\n    }\n\n    return (\n        <div className=\"product-detail-page\">\n            <div className=\"container\">\n                <div className=\"breadcrumb\">\n                    <Link to=\"/\">Home</Link> /\n                    <Link to=\"/products\">Products</Link> /\n                    <span>{name}</span>\n                </div>\n\n                <div className=\"product-detail\">\n                    <ProductMedia\n                        images={images}\n                        name={name}\n                        selectedImage={selectedImage}\n                        setSelectedImage={setSelectedImage}\n                        isConfigurable3D={isConfigurable3D}\n                        setShow3DConfigurator={setShow3DConfigurator}\n                    />\n\n                    <ProductInfo\n                        product={product}\n                        formatPrice={formatPrice}\n                        displayPrice={displayPrice}\n                        hasDiscount={hasDiscount}\n                        price={price}\n                        quantity={quantity}\n                        setQuantity={setQuantity}\n                        handleAddToCart={handleAddToCart}\n                    />\n                </div>\n\n                {/* Related Products Section */}\n                <div className=\"related-products\">\n                    <h2>Related Products</h2>\n                    <div className=\"related-products-grid\">\n                        {/* Placeholder for related products */}\n                        <div className=\"related-product-card\">\n                            <div className=\"related-product-image\">\n                                <img src=\"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300\" alt=\"Related Product\" />\n                            </div>\n                            <div className=\"related-product-info\">\n                                <h4>Hotel Bed</h4>\n                                <p>$2,299</p>\n                            </div>\n                        </div>\n                        <div className=\"related-product-card\">\n                            <div className=\"related-product-image\">\n                                <img src=\"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300\" alt=\"Related Product\" />\n                            </div>\n                            <div className=\"related-product-info\">\n                                <h4>Conference Table</h4>\n                                <p>$2,499</p>\n                            </div>\n                        </div>\n                        <div className=\"related-product-card\">\n                            <div className=\"related-product-image\">\n                                <img src=\"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300\" alt=\"Related Product\" />\n                            </div>\n                            <div className=\"related-product-info\">\n                                <h4>Reception Desk</h4>\n                                <p>$1,899</p>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            {/* Checkout Modal */}\n            <CheckoutModal\n                isOpen={showCheckoutModal}\n                onClose={() => setShowCheckoutModal(false)}\n                product={product}\n                quantity={quantity}\n            />\n        </div>\n    );\n};\n\nexport default ProductDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,IAAI,QAAQ,kBAAkB;AAClD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,sBAAsB,MAAM,iCAAiC;;AAEpE;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,YAAY,GAAGA,CAAC;EAAEC,MAAM;EAAEC,IAAI;EAAEC,aAAa;EAAEC,gBAAgB;EAAEC,gBAAgB;EAAEC;AAAsB,CAAC,kBAC5GT,OAAA;EAAKU,SAAS,EAAC,eAAe;EAAAC,QAAA,gBAC1BX,OAAA;IAAKU,SAAS,EAAC,YAAY;IAAAC,QAAA,eACvBX,OAAA;MAAKU,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACvBX,OAAA;QACIY,GAAG,EAAER,MAAM,IAAIA,MAAM,CAACE,aAAa,CAAC,GAAGF,MAAM,CAACE,aAAa,CAAC,GAAG,oEAAqE;QACpIO,GAAG,EAAER,IAAK;QACVS,OAAO,EAAGC,CAAC,IAAK;UACZA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAG,oEAAoE;QACvF;MAAE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EACDZ,gBAAgB,iBACbR,OAAA;QAAKU,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC1BX,OAAA;UACIU,SAAS,EAAC,kBAAkB;UAC5BW,OAAO,EAAEA,CAAA,KAAMZ,qBAAqB,CAAC,IAAI,CAAE;UAAAE,QAAA,gBAE3CX,OAAA;YAAKsB,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,KAAK,EAAC,4BAA4B;YAAAf,QAAA,gBAC1FX,OAAA;cAAM2B,CAAC,EAAC,4BAA4B;cAACF,IAAI,EAAC;YAAc;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eAC1DpB,OAAA;cAAM2B,CAAC,EAAC,mBAAmB;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eAChHpB,OAAA;cAAM2B,CAAC,EAAC,mBAAmB;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/G,CAAC,mBAEV;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC,eAENpB,OAAA;IAAKU,SAAS,EAAC,kBAAkB;IAAAC,QAAA,EAC5BP,MAAM,IAAIA,MAAM,CAAC4B,MAAM,GAAG,CAAC,iBACxBhC,OAAA;MAAKU,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAC5BP,MAAM,CAAC6B,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACrBnC,OAAA;QAEIY,GAAG,EAAEsB,KAAM;QACXrB,GAAG,EAAE,GAAGR,IAAI,IAAI8B,KAAK,GAAG,CAAC,EAAG;QAC5BzB,SAAS,EAAEJ,aAAa,KAAK6B,KAAK,GAAG,QAAQ,GAAG,EAAG;QACnDd,OAAO,EAAEA,CAAA,KAAMd,gBAAgB,CAAC4B,KAAK,CAAE;QACvCrB,OAAO,EAAGC,CAAC,IAAK;UACZA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAG,oEAAoE;QACvF;MAAE,GAPGuB,KAAK;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQb,CACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EACR;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACL,CACR;;AAED;AAAAgB,EAAA,GAlDMjC,YAAY;AAmDlB,MAAMkC,WAAW,GAAGA,CAAC;EAAEC,OAAO;EAAEC,WAAW;EAAEC,YAAY;EAAEC,WAAW;EAAEC,KAAK;EAAEC,QAAQ;EAAEC,WAAW;EAAEC;AAAgB,CAAC,KAAK;EACxH,MAAM;IAAExC,IAAI;IAAEyC,YAAY;IAAEC,WAAW;IAAEC;EAAS,CAAC,GAAGV,OAAO;EAE7D,oBACItC,OAAA;IAAKU,SAAS,EAAC,cAAc;IAAAC,QAAA,gBACzBX,OAAA;MAAKU,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAAEmC;IAAY;MAAA7B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACtDpB,OAAA;MAAAW,QAAA,EAAKN;IAAI;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAEfpB,OAAA;MAAKU,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5BX,OAAA;QAAMU,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAE4B,WAAW,CAACC,YAAY;MAAC;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACjEqB,WAAW,iBACRzC,OAAA,CAAAE,SAAA;QAAAS,QAAA,gBACIX,OAAA;UAAMU,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAE4B,WAAW,CAACG,KAAK;QAAC;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5DpB,OAAA;UAAMU,SAAS,EAAC,gBAAgB;UAAAC,QAAA,GAC3BsC,IAAI,CAACC,KAAK,CAAE,CAACR,KAAK,GAAGF,YAAY,IAAIE,KAAK,GAAI,GAAG,CAAC,EAAC,OACxD;QAAA;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,eACT,CACL;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAENpB,OAAA;MAAKU,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAChCX,OAAA;QAAAW,QAAA,EAAIoC;MAAW;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,EAEL4B,QAAQ,IAAIA,QAAQ,CAAChB,MAAM,GAAG,CAAC,iBAC5BhC,OAAA;MAAKU,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC7BX,OAAA;QAAAW,QAAA,EAAI;MAAY;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrBpB,OAAA;QAAAW,QAAA,EACKqC,QAAQ,CAACf,GAAG,CAAC,CAACkB,OAAO,EAAEhB,KAAK,kBACzBnC,OAAA;UAAAW,QAAA,EAAiBwC;QAAO,GAAfhB,KAAK;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAChC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACR,eAEDpB,OAAA;MAAKU,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5BX,OAAA;QAAKU,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAC9BX,OAAA;UAAOoD,OAAO,EAAC,UAAU;UAAAzC,QAAA,EAAC;QAAS;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC3CpB,OAAA;UAAKU,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAC9BX,OAAA;YACIqD,IAAI,EAAC,QAAQ;YACbhC,OAAO,EAAEA,CAAA,KAAMuB,WAAW,CAACK,IAAI,CAACK,GAAG,CAAC,CAAC,EAAEX,QAAQ,GAAG,CAAC,CAAC,CAAE;YACtDY,QAAQ,EAAEZ,QAAQ,IAAI,CAAE;YAAAhC,QAAA,EAC3B;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpB,OAAA;YACIwD,EAAE,EAAC,UAAU;YACbH,IAAI,EAAC,QAAQ;YACbI,GAAG,EAAC,GAAG;YACPC,KAAK,EAAEf,QAAS;YAChBgB,QAAQ,EAAG5C,CAAC,IAAK6B,WAAW,CAACK,IAAI,CAACK,GAAG,CAAC,CAAC,EAAEM,QAAQ,CAAC7C,CAAC,CAACC,MAAM,CAAC0C,KAAK,CAAC,IAAI,CAAC,CAAC;UAAE;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E,CAAC,eACFpB,OAAA;YACIqD,IAAI,EAAC,QAAQ;YACbhC,OAAO,EAAEA,CAAA,KAAMuB,WAAW,CAACD,QAAQ,GAAG,CAAC,CAAE;YAAAhC,QAAA,EAC5C;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENpB,OAAA;QAAQU,SAAS,EAAC,iBAAiB;QAACW,OAAO,EAAEwB,eAAgB;QAAAlC,QAAA,GAAC,gBAC5C,EAAC4B,WAAW,CAACC,YAAY,GAAGG,QAAQ,CAAC;MAAA;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACyC,GAAA,GApEIxB,WAAW;AAsEjB,MAAMyB,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEP;EAAG,CAAC,GAAG/D,SAAS,CAAC,CAAC;EAC1B,MAAM;IAAEuE;EAAU,CAAC,GAAGpE,OAAO,CAAC,CAAC;EAC/B,MAAM,CAAC0C,OAAO,EAAE2B,UAAU,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2E,OAAO,EAAEC,UAAU,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6E,KAAK,EAAEC,QAAQ,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACe,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAAC+E,kBAAkB,EAAE7D,qBAAqB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACgF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACkF,aAAa,EAAEC,gBAAgB,CAAC,GAAGnF,QAAQ,CAAC;IAC/CoF,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE;MAAEvD,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE,EAAE;MAAEuD,KAAK,EAAE;IAAG;EACpD,CAAC,CAAC;EACF,MAAM,CAACnC,QAAQ,EAAEC,WAAW,CAAC,GAAGrD,QAAQ,CAAC,CAAC,CAAC;EAE3CC,SAAS,CAAC,MAAM;IACZuF,WAAW,CAAC,CAAC;EACjB,CAAC,EAAE,CAACvB,EAAE,CAAC,CAAC;EAER,MAAMuB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMrF,cAAc,CAAC6D,EAAE,CAAC;MACzCS,UAAU,CAACe,QAAQ,CAAC1C,OAAO,CAAC;IAChC,CAAC,CAAC,OAAO8B,KAAK,EAAE;MACZC,QAAQ,CAAC,gCAAgC,CAAC;IAC9C,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAM5B,WAAW,GAAIG,KAAK,IAAK;IAC3B,OAAO,IAAIuC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MAClCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACd,CAAC,CAAC,CAACC,MAAM,CAAC3C,KAAK,CAAC;EACpB,CAAC;;EAED;EACA,MAAM4C,kBAAkB,GAAIhD,OAAO,IAAK;IACpC,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAC1B,MAAMiD,WAAW,GAAGjD,OAAO,CAACjC,IAAI,CAACmF,WAAW,CAAC,CAAC;IAC9C,MAAMC,iBAAiB,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,OAAO,CAAC;IAClG,OAAOA,iBAAiB,CAACC,IAAI,CAACrC,IAAI,IAAIkC,WAAW,CAACI,QAAQ,CAACtC,IAAI,CAAC,CAAC;EACrE,CAAC;;EAED;EACA,MAAMR,eAAe,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACP,OAAO,EAAE;IAEd,IAAI;MACA0B,SAAS,CAAC1B,OAAO,EAAEK,QAAQ,EAAE8B,aAAa,CAAC;MAC3CD,oBAAoB,CAAC,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACZwB,KAAK,CAAC,+CAA+C,CAAC;IAC1D;EACJ,CAAC;EAED,IAAI1B,OAAO,EAAE;IACT,oBACIlE,OAAA;MAAKU,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAChCX,OAAA;QAAKU,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtBX,OAAA;UAAKU,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAA0B;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAIgD,KAAK,IAAI,CAAC9B,OAAO,EAAE;IACnB,oBACItC,OAAA;MAAKU,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAChCX,OAAA;QAAKU,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtBX,OAAA;UAAKU,SAAS,EAAC,eAAe;UAAAC,QAAA,GACzByD,KAAK,IAAI,mBAAmB,eAC7BpE,OAAA,CAACN,IAAI;YAACmG,EAAE,EAAC,WAAW;YAACnF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAEjD;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,MAAM;IACFf,IAAI;IACJ0C,WAAW;IACXL,KAAK;IACLoD,aAAa;IACb1F,MAAM;IACN0C,YAAY;IACZiD;EACJ,CAAC,GAAGzD,OAAO;EAEX,MAAME,YAAY,GAAGsD,aAAa,IAAIpD,KAAK;EAC3C,MAAMD,WAAW,GAAGqD,aAAa,IAAIA,aAAa,GAAGpD,KAAK;;EAE1D;EACA,MAAMlC,gBAAgB,GAAG8E,kBAAkB,CAAChD,OAAO,CAAC;;EAEpD;EACA,IAAIgC,kBAAkB,IAAI9D,gBAAgB,EAAE;IACxC,oBAAOR,OAAA,CAACF,sBAAsB;MAACwC,OAAO,EAAEA,OAAQ;MAAC0D,MAAM,EAAEA,CAAA,KAAMvF,qBAAqB,CAAC,KAAK;IAAE;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnG;EAEA,oBACIpB,OAAA;IAAKU,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAChCX,OAAA;MAAKU,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACtBX,OAAA;QAAKU,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACvBX,OAAA,CAACN,IAAI;UAACmG,EAAE,EAAC,GAAG;UAAAlF,QAAA,EAAC;QAAI;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,MACxB,eAAApB,OAAA,CAACN,IAAI;UAACmG,EAAE,EAAC,WAAW;UAAAlF,QAAA,EAAC;QAAQ;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,MACpC,eAAApB,OAAA;UAAAW,QAAA,EAAON;QAAI;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eAENpB,OAAA;QAAKU,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC3BX,OAAA,CAACG,YAAY;UACTC,MAAM,EAAEA,MAAO;UACfC,IAAI,EAAEA,IAAK;UACXC,aAAa,EAAEA,aAAc;UAC7BC,gBAAgB,EAAEA,gBAAiB;UACnCC,gBAAgB,EAAEA,gBAAiB;UACnCC,qBAAqB,EAAEA;QAAsB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eAEFpB,OAAA,CAACqC,WAAW;UACRC,OAAO,EAAEA,OAAQ;UACjBC,WAAW,EAAEA,WAAY;UACzBC,YAAY,EAAEA,YAAa;UAC3BC,WAAW,EAAEA,WAAY;UACzBC,KAAK,EAAEA,KAAM;UACbC,QAAQ,EAAEA,QAAS;UACnBC,WAAW,EAAEA,WAAY;UACzBC,eAAe,EAAEA;QAAgB;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNpB,OAAA;QAAKU,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC7BX,OAAA;UAAAW,QAAA,EAAI;QAAgB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBpB,OAAA;UAAKU,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBAElCX,OAAA;YAAKU,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACjCX,OAAA;cAAKU,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eAClCX,OAAA;gBAAKY,GAAG,EAAC,oEAAoE;gBAACC,GAAG,EAAC;cAAiB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrG,CAAC,eACNpB,OAAA;cAAKU,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACjCX,OAAA;gBAAAW,QAAA,EAAI;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClBpB,OAAA;gBAAAW,QAAA,EAAG;cAAM;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNpB,OAAA;YAAKU,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACjCX,OAAA;cAAKU,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eAClCX,OAAA;gBAAKY,GAAG,EAAC,oEAAoE;gBAACC,GAAG,EAAC;cAAiB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrG,CAAC,eACNpB,OAAA;cAAKU,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACjCX,OAAA;gBAAAW,QAAA,EAAI;cAAgB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzBpB,OAAA;gBAAAW,QAAA,EAAG;cAAM;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNpB,OAAA;YAAKU,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACjCX,OAAA;cAAKU,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eAClCX,OAAA;gBAAKY,GAAG,EAAC,oEAAoE;gBAACC,GAAG,EAAC;cAAiB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrG,CAAC,eACNpB,OAAA;cAAKU,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACjCX,OAAA;gBAAAW,QAAA,EAAI;cAAc;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvBpB,OAAA;gBAAAW,QAAA,EAAG;cAAM;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNpB,OAAA,CAACH,aAAa;MACVoG,MAAM,EAAE1B,iBAAkB;MAC1B2B,OAAO,EAAEA,CAAA,KAAM1B,oBAAoB,CAAC,KAAK,CAAE;MAC3ClC,OAAO,EAAEA,OAAQ;MACjBK,QAAQ,EAAEA;IAAS;MAAA1B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAAC2C,EAAA,CApLID,aAAa;EAAA,QACArE,SAAS,EACFG,OAAO;AAAA;AAAAuG,GAAA,GAF3BrC,aAAa;AAsLnB,eAAeA,aAAa;AAAC,IAAA1B,EAAA,EAAAyB,GAAA,EAAAsC,GAAA;AAAAC,YAAA,CAAAhE,EAAA;AAAAgE,YAAA,CAAAvC,GAAA;AAAAuC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}