const ProductService = require('../productService');
const Product = require('../../models/Product');
const logger = require('../../utils/logger');

// Mock the Product model
jest.mock('../../models/Product');
jest.mock('../../utils/logger');

describe('ProductService', () => {
  let productService;
  
  beforeEach(() => {
    productService = new ProductService();
    jest.clearAllMocks();
  });

  describe('getProducts', () => {
    const mockProducts = [
      {
        id: 1,
        name: 'Office Chair',
        price: 299.99,
        categoryName: 'Chairs',
        inStock: true
      },
      {
        id: 2,
        name: 'Standing Desk',
        price: 599.99,
        categoryName: 'Desks',
        inStock: true
      }
    ];

    test('returns paginated products successfully', async () => {
      const filters = { page: 1, limit: 10 };
      const mockResult = {
        products: mockProducts,
        pagination: {
          currentPage: 1,
          totalPages: 1,
          totalItems: 2,
          itemsPerPage: 10
        }
      };

      Product.getAll.mockResolvedValue(mockResult);

      const result = await productService.getProducts(filters);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockResult);
      expect(Product.getAll).toHaveBeenCalledWith(filters);
    });

    test('applies search filter correctly', async () => {
      const filters = { search: 'chair', page: 1, limit: 10 };
      const mockResult = {
        products: [mockProducts[0]],
        pagination: {
          currentPage: 1,
          totalPages: 1,
          totalItems: 1,
          itemsPerPage: 10
        }
      };

      Product.getAll.mockResolvedValue(mockResult);

      const result = await productService.getProducts(filters);

      expect(result.success).toBe(true);
      expect(result.data.products).toHaveLength(1);
      expect(Product.getAll).toHaveBeenCalledWith(filters);
    });

    test('applies category filter correctly', async () => {
      const filters = { category: 'Chairs', page: 1, limit: 10 };
      
      Product.getAll.mockResolvedValue({
        products: [mockProducts[0]],
        pagination: { currentPage: 1, totalPages: 1, totalItems: 1, itemsPerPage: 10 }
      });

      const result = await productService.getProducts(filters);

      expect(result.success).toBe(true);
      expect(Product.getAll).toHaveBeenCalledWith(filters);
    });

    test('applies price range filters correctly', async () => {
      const filters = { minPrice: 200, maxPrice: 400, page: 1, limit: 10 };
      
      Product.getAll.mockResolvedValue({
        products: [mockProducts[0]],
        pagination: { currentPage: 1, totalPages: 1, totalItems: 1, itemsPerPage: 10 }
      });

      const result = await productService.getProducts(filters);

      expect(result.success).toBe(true);
      expect(Product.getAll).toHaveBeenCalledWith(filters);
    });

    test('handles database errors gracefully', async () => {
      const filters = { page: 1, limit: 10 };
      const error = new Error('Database connection failed');

      Product.getAll.mockRejectedValue(error);

      const result = await productService.getProducts(filters);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to fetch products');
      expect(logger.error).toHaveBeenCalledWith('ProductService.getProducts error:', error);
    });

    test('uses default pagination values', async () => {
      const filters = {};
      
      Product.getAll.mockResolvedValue({
        products: mockProducts,
        pagination: { currentPage: 1, totalPages: 1, totalItems: 2, itemsPerPage: 12 }
      });

      await productService.getProducts(filters);

      expect(Product.getAll).toHaveBeenCalledWith(
        expect.objectContaining({
          page: 1,
          limit: 12
        })
      );
    });
  });

  describe('getProductById', () => {
    const mockProduct = {
      id: 1,
      name: 'Office Chair',
      price: 299.99,
      description: 'Comfortable office chair',
      categoryName: 'Chairs',
      images: ['chair1.jpg', 'chair2.jpg'],
      specifications: { material: 'Leather', color: 'Black' }
    };

    test('returns product successfully', async () => {
      Product.getById.mockResolvedValue(mockProduct);

      const result = await productService.getProductById(1);

      expect(result.success).toBe(true);
      expect(result.data.product).toEqual(mockProduct);
      expect(Product.getById).toHaveBeenCalledWith(1);
    });

    test('handles invalid product ID', async () => {
      const result = await productService.getProductById('invalid');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid product ID');
      expect(result.code).toBe('INVALID_PRODUCT_ID');
      expect(Product.getById).not.toHaveBeenCalled();
    });

    test('handles product not found', async () => {
      Product.getById.mockResolvedValue(null);

      const result = await productService.getProductById(999);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Product not found');
      expect(result.code).toBe('PRODUCT_NOT_FOUND');
    });

    test('handles database errors', async () => {
      const error = new Error('Database error');
      Product.getById.mockRejectedValue(error);

      const result = await productService.getProductById(1);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to fetch product');
      expect(logger.error).toHaveBeenCalledWith('ProductService.getProductById error:', error);
    });
  });

  describe('createProduct', () => {
    const mockProductData = {
      name: 'New Office Chair',
      description: 'Brand new chair',
      price: 399.99,
      categoryId: 1,
      specifications: { material: 'Fabric' }
    };

    test('creates product successfully', async () => {
      const mockCreatedProduct = { id: 3, ...mockProductData };
      Product.create.mockResolvedValue(mockCreatedProduct);

      const result = await productService.createProduct(mockProductData);

      expect(result.success).toBe(true);
      expect(result.data.product).toEqual(mockCreatedProduct);
      expect(Product.create).toHaveBeenCalledWith(mockProductData);
    });

    test('validates required fields', async () => {
      const invalidData = { name: '', price: -10 };

      const result = await productService.createProduct(invalidData);

      expect(result.success).toBe(false);
      expect(result.error).toContain('validation');
      expect(Product.create).not.toHaveBeenCalled();
    });

    test('handles database errors during creation', async () => {
      const error = new Error('Duplicate entry');
      Product.create.mockRejectedValue(error);

      const result = await productService.createProduct(mockProductData);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to create product');
      expect(logger.error).toHaveBeenCalledWith('ProductService.createProduct error:', error);
    });
  });

  describe('updateProduct', () => {
    const mockUpdateData = {
      name: 'Updated Chair',
      price: 349.99
    };

    test('updates product successfully', async () => {
      const mockUpdatedProduct = { id: 1, ...mockUpdateData };
      Product.update.mockResolvedValue(mockUpdatedProduct);

      const result = await productService.updateProduct(1, mockUpdateData);

      expect(result.success).toBe(true);
      expect(result.data.product).toEqual(mockUpdatedProduct);
      expect(Product.update).toHaveBeenCalledWith(1, mockUpdateData);
    });

    test('handles invalid product ID', async () => {
      const result = await productService.updateProduct('invalid', mockUpdateData);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid product ID');
      expect(Product.update).not.toHaveBeenCalled();
    });

    test('handles product not found during update', async () => {
      Product.update.mockResolvedValue(null);

      const result = await productService.updateProduct(999, mockUpdateData);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Product not found');
    });
  });

  describe('deleteProduct', () => {
    test('deletes product successfully', async () => {
      Product.delete.mockResolvedValue(true);

      const result = await productService.deleteProduct(1);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Product deleted successfully');
      expect(Product.delete).toHaveBeenCalledWith(1);
    });

    test('handles invalid product ID', async () => {
      const result = await productService.deleteProduct('invalid');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid product ID');
      expect(Product.delete).not.toHaveBeenCalled();
    });

    test('handles product not found during deletion', async () => {
      Product.delete.mockResolvedValue(false);

      const result = await productService.deleteProduct(999);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Product not found');
    });
  });

  describe('searchProducts', () => {
    test('performs text search correctly', async () => {
      const searchTerm = 'office chair';
      const mockResults = [
        { id: 1, name: 'Office Chair', relevance: 0.95 },
        { id: 2, name: 'Executive Office Chair', relevance: 0.87 }
      ];

      Product.search.mockResolvedValue(mockResults);

      const result = await productService.searchProducts(searchTerm);

      expect(result.success).toBe(true);
      expect(result.data.products).toEqual(mockResults);
      expect(Product.search).toHaveBeenCalledWith(searchTerm);
    });

    test('handles empty search term', async () => {
      const result = await productService.searchProducts('');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Search term is required');
      expect(Product.search).not.toHaveBeenCalled();
    });
  });

  describe('getFeaturedProducts', () => {
    test('returns featured products', async () => {
      const mockFeaturedProducts = [
        { id: 1, name: 'Featured Chair', featured: true },
        { id: 2, name: 'Featured Desk', featured: true }
      ];

      Product.getFeatured.mockResolvedValue(mockFeaturedProducts);

      const result = await productService.getFeaturedProducts();

      expect(result.success).toBe(true);
      expect(result.data.products).toEqual(mockFeaturedProducts);
      expect(Product.getFeatured).toHaveBeenCalled();
    });
  });

  describe('getProductsByCategory', () => {
    test('returns products by category', async () => {
      const categoryId = 1;
      const mockCategoryProducts = [
        { id: 1, name: 'Chair 1', categoryId: 1 },
        { id: 2, name: 'Chair 2', categoryId: 1 }
      ];

      Product.getByCategory.mockResolvedValue(mockCategoryProducts);

      const result = await productService.getProductsByCategory(categoryId);

      expect(result.success).toBe(true);
      expect(result.data.products).toEqual(mockCategoryProducts);
      expect(Product.getByCategory).toHaveBeenCalledWith(categoryId);
    });

    test('handles invalid category ID', async () => {
      const result = await productService.getProductsByCategory('invalid');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid category ID');
      expect(Product.getByCategory).not.toHaveBeenCalled();
    });
  });
});
