{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\common\\\\Header.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../hooks/useAuth';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport CartIcon from '../cart/CartIcon';\nimport Logo from './Logo';\nimport CurrencyLanguageSelector from './CurrencyLanguageSelector';\nimport SearchInput from '../search/SearchInput';\nimport '../../styles/components.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  _s();\n  const {\n    user,\n    logout,\n    isAuthenticated\n  } = useAuth();\n  const {\n    t\n  } = useLanguage();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const navigate = useNavigate();\n\n  // Scroll-based navigation state\n  const [isNavHidden, setIsNavHidden] = useState(false);\n  const [isNavFloating, setIsNavFloating] = useState(false);\n  const lastScrollY = useRef(0);\n  const scrollThreshold = 100;\n  const handleLogout = useCallback(() => {\n    // Add confirmation dialog for better UX\n    if (window.confirm('Are you sure you want to logout?')) {\n      logout();\n      navigate('/');\n      setIsMenuOpen(false);\n    }\n  }, [logout, navigate]);\n\n  // Scroll behavior for navigation bar\n  useEffect(() => {\n    const handleScroll = () => {\n      const currentScrollY = window.scrollY;\n\n      // Only activate behavior after scrolling past threshold\n      if (currentScrollY < scrollThreshold) {\n        setIsNavHidden(false);\n        setIsNavFloating(false);\n        lastScrollY.current = currentScrollY;\n        return;\n      }\n\n      // Determine scroll direction\n      const scrollingDown = currentScrollY > lastScrollY.current;\n      const scrollingUp = currentScrollY < lastScrollY.current;\n\n      // Hide navigation when scrolling down, show when scrolling up\n      if (scrollingDown && currentScrollY > scrollThreshold) {\n        setIsNavHidden(true);\n        setIsNavFloating(true);\n      } else if (scrollingUp) {\n        setIsNavHidden(false);\n        setIsNavFloating(true);\n      }\n      lastScrollY.current = currentScrollY;\n    };\n\n    // Throttle scroll events for better performance\n    let ticking = false;\n    const throttledHandleScroll = () => {\n      if (!ticking) {\n        requestAnimationFrame(() => {\n          handleScroll();\n          ticking = false;\n        });\n        ticking = true;\n      }\n    };\n    window.addEventListener('scroll', throttledHandleScroll, {\n      passive: true\n    });\n    return () => {\n      window.removeEventListener('scroll', throttledHandleScroll);\n    };\n  }, []);\n  const toggleMenu = useCallback(() => {\n    setIsMenuOpen(prev => !prev);\n  }, []);\n  const closeMenu = useCallback(() => {\n    setIsMenuOpen(false);\n  }, []);\n\n  // Memoize navigation links to prevent unnecessary re-renders\n  const navigationLinks = useMemo(() => [{\n    to: '/',\n    label: t('home')\n  }, {\n    to: '/products',\n    label: t('products')\n  }, {\n    to: '/configurator',\n    label: t('customFurniture')\n  }, {\n    to: '/gallery',\n    label: t('gallery')\n  }, {\n    to: '/about',\n    label: t('about')\n  }, {\n    to: '/contact',\n    label: t('contact')\n  }, {\n    to: '/payment',\n    label: t('payments')\n  }], [t]);\n\n  // Memoize user actions to prevent re-renders\n  const userActions = useMemo(() => {\n    if (isAuthenticated) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-menu\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/account\",\n          className: \"action-btn user-btn\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z\",\n              stroke: \"#F0B21B\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"user-greeting\",\n          children: [\"Hello, \", user === null || user === void 0 ? void 0 : user.firstName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleLogout,\n          className: \"logout-btn\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"16\",\n            height: \"16\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9M16 17L21 12M21 12L16 7M21 12H9\",\n              stroke: \"#F0B21B\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 17\n      }, this);\n    } else {\n      return /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/login\",\n        className: \"action-btn login-btn\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"20\",\n          height: \"20\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15M10 17L15 12M15 12L10 7M15 12H3\",\n            stroke: \"#F0B21B\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 17\n      }, this);\n    }\n  }, [isAuthenticated, user === null || user === void 0 ? void 0 : user.firstName, handleLogout]);\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"header\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"special-offer-banner\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"offer-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"offer-icon\",\n            children: \"\\u26A1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"offer-text\",\n            children: t('specialOffer')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"offer-details\",\n            children: t('offerText')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"offer-shop-btn\",\n            children: t('shopNow')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"offer-close\",\n            onClick: () => document.querySelector('.special-offer-banner').style.display = 'none',\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"top-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"top-header-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"contact-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"14\",\n                height: \"14\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                style: {\n                  marginRight: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                  points: \"22,6 12,13 2,6\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 33\n              }, this), \"<EMAIL>\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"contact-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"14\",\n                height: \"14\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                style: {\n                  marginRight: '0.5rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M22 16.92V19.92C22.0011 20.1985 21.9441 20.4742 21.8325 20.7293C21.7209 20.9845 21.5573 21.2136 21.3521 21.4019C21.1468 21.5901 20.9046 21.7335 20.6407 21.8227C20.3769 21.9119 20.0974 21.9451 19.82 21.92C16.7428 21.5856 13.787 20.5341 11.19 18.85C8.77382 17.3147 6.72533 15.2662 5.18999 12.85C3.49997 10.2412 2.44824 7.27099 2.11999 4.18C2.095 3.90347 2.12787 3.62476 2.21649 3.36162C2.30512 3.09849 2.44756 2.85669 2.63476 2.65162C2.82196 2.44655 3.0498 2.28271 3.30379 2.17052C3.55777 2.05833 3.83233 2.00026 4.10999 2H7.10999C7.59531 1.99522 8.06579 2.16708 8.43376 2.48353C8.80173 2.79999 9.04207 3.23945 9.10999 3.72C9.23662 4.68007 9.47144 5.62273 9.80999 6.53C9.94454 6.88792 9.97366 7.27691 9.8939 7.65088C9.81415 8.02485 9.62886 8.36811 9.35999 8.64L8.08999 9.91C9.51355 12.4135 11.5865 14.4864 14.09 15.91L15.36 14.64C15.6319 14.3711 15.9751 14.1858 16.3491 14.1061C16.7231 14.0263 17.1121 14.0555 17.47 14.19C18.3773 14.5286 19.3199 14.7634 20.28 14.89C20.7658 14.9585 21.2094 15.2032 21.5265 15.5775C21.8437 15.9518 22.0122 16.4296 22 16.92Z\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 33\n              }, this), \"(02) 413-6682\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"location-info\",\n            children: \"#1 Binakla Street Cor Biak na Bato Brgy, Manresa, Quezon City\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(CurrencyLanguageSelector, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `unified-header-container ${isNavHidden ? 'unified-hidden' : ''} ${isNavFloating ? 'unified-floating' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"main-header-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"header-search\",\n              children: /*#__PURE__*/_jsxDEV(SearchInput, {\n                className: \"header-search-input\",\n                placeholder: t('searchProducts')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 29\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"header-logo\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/\",\n                className: \"logo\",\n                children: /*#__PURE__*/_jsxDEV(Logo, {\n                  size: \"default\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 33\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"logo-tagline\",\n                children: \"EXCELLENCE IN DESIGN\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 29\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"header-actions\",\n              children: [/*#__PURE__*/_jsxDEV(CartIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/contact\",\n                className: \"action-btn contact-btn\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"20\",\n                  height: \"20\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z\",\n                    stroke: \"#F0B21B\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 37\n                  }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                    points: \"22,6 12,13 2,6\",\n                    stroke: \"#F0B21B\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 37\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 33\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 29\n              }, this), isAuthenticated ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"user-menu\",\n                children: [/*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/account\",\n                  className: \"action-btn user-btn\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 266,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"user-greeting\",\n                  children: [\"Hello, \", user === null || user === void 0 ? void 0 : user.firstName]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleLogout,\n                  className: \"logout-btn\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"16\",\n                    height: \"16\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 286,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                      points: \"16,17 21,12 16,7\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 293,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                      x1: \"21\",\n                      y1: \"12\",\n                      x2: \"9\",\n                      y2: \"12\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 300,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Logout\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 33\n              }, this) : /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                className: \"action-btn user-btn\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"20\",\n                  height: \"20\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z\",\n                    stroke: \"#F0B21B\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"mobile-menu-toggle\",\n                onClick: toggleMenu,\n                \"aria-label\": \"Toggle menu\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 33\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 33\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 33\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 29\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"navigation-bar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: /*#__PURE__*/_jsxDEV(\"nav\", {\n            className: \"main-navigation\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/\",\n              className: \"nav-link\",\n              children: t('home')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/products\",\n              className: \"nav-link\",\n              children: t('products')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/configurator\",\n              className: \"nav-link\",\n              children: t('customFurniture')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/gallery\",\n              className: \"nav-link\",\n              children: t('gallery')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/about\",\n              className: \"nav-link\",\n              children: t('about')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/contact\",\n              className: \"nav-link\",\n              children: t('contact')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/payment\",\n              className: \"nav-link\",\n              children: t('payments')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: `mobile-nav ${isMenuOpen ? 'open' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        className: \"mobile-nav-link\",\n        onClick: () => setIsMenuOpen(false),\n        children: t('home')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/products\",\n        className: \"mobile-nav-link\",\n        onClick: () => setIsMenuOpen(false),\n        children: t('products')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/configurator\",\n        className: \"mobile-nav-link\",\n        onClick: () => setIsMenuOpen(false),\n        children: t('customFurniture')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/gallery\",\n        className: \"mobile-nav-link\",\n        onClick: () => setIsMenuOpen(false),\n        children: t('gallery')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/about\",\n        className: \"mobile-nav-link\",\n        onClick: () => setIsMenuOpen(false),\n        children: t('about')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/contact\",\n        className: \"mobile-nav-link\",\n        onClick: () => setIsMenuOpen(false),\n        children: t('contact')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/payment\",\n        className: \"mobile-nav-link\",\n        onClick: () => setIsMenuOpen(false),\n        children: t('payments')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 17\n      }, this), !isAuthenticated && /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/login\",\n        className: \"mobile-nav-link login\",\n        onClick: () => setIsMenuOpen(false),\n        children: \"Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 418,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 129,\n    columnNumber: 9\n  }, this);\n};\n_s(Header, \"ycTms872/t9iZsXIV5oeqFNMKIo=\", false, function () {\n  return [useAuth, useLanguage, useNavigate];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "useMemo", "Link", "useNavigate", "useAuth", "useLanguage", "CartIcon", "Logo", "CurrencyLanguageSelector", "SearchInput", "jsxDEV", "_jsxDEV", "Header", "_s", "user", "logout", "isAuthenticated", "t", "isMenuOpen", "setIsMenuOpen", "navigate", "isNavHidden", "setIsNavHidden", "isNavFloating", "setIsNavFloating", "lastScrollY", "scrollThreshold", "handleLogout", "window", "confirm", "handleScroll", "currentScrollY", "scrollY", "current", "scrollingDown", "scrollingUp", "ticking", "throttledHandleScroll", "requestAnimationFrame", "addEventListener", "passive", "removeEventListener", "toggleMenu", "prev", "closeMenu", "navigationLinks", "to", "label", "userActions", "className", "children", "width", "height", "viewBox", "fill", "xmlns", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "firstName", "onClick", "document", "querySelector", "style", "display", "marginRight", "points", "placeholder", "size", "x1", "y1", "x2", "y2", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/common/Header.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../hooks/useAuth';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport CartIcon from '../cart/CartIcon';\nimport Logo from './Logo';\nimport CurrencyLanguageSelector from './CurrencyLanguageSelector';\nimport SearchInput from '../search/SearchInput';\nimport '../../styles/components.css';\n\nconst Header = () => {\n    const { user, logout, isAuthenticated } = useAuth();\n    const { t } = useLanguage();\n    const [isMenuOpen, setIsMenuOpen] = useState(false);\n    const navigate = useNavigate();\n\n    // Scroll-based navigation state\n    const [isNavHidden, setIsNavHidden] = useState(false);\n    const [isNavFloating, setIsNavFloating] = useState(false);\n    const lastScrollY = useRef(0);\n    const scrollThreshold = 100;\n\n    const handleLogout = useCallback(() => {\n        // Add confirmation dialog for better UX\n        if (window.confirm('Are you sure you want to logout?')) {\n            logout();\n            navigate('/');\n            setIsMenuOpen(false);\n        }\n    }, [logout, navigate]);\n\n    // Scroll behavior for navigation bar\n    useEffect(() => {\n        const handleScroll = () => {\n            const currentScrollY = window.scrollY;\n\n            // Only activate behavior after scrolling past threshold\n            if (currentScrollY < scrollThreshold) {\n                setIsNavHidden(false);\n                setIsNavFloating(false);\n                lastScrollY.current = currentScrollY;\n                return;\n            }\n\n            // Determine scroll direction\n            const scrollingDown = currentScrollY > lastScrollY.current;\n            const scrollingUp = currentScrollY < lastScrollY.current;\n\n            // Hide navigation when scrolling down, show when scrolling up\n            if (scrollingDown && currentScrollY > scrollThreshold) {\n                setIsNavHidden(true);\n                setIsNavFloating(true);\n            } else if (scrollingUp) {\n                setIsNavHidden(false);\n                setIsNavFloating(true);\n            }\n\n            lastScrollY.current = currentScrollY;\n        };\n\n        // Throttle scroll events for better performance\n        let ticking = false;\n        const throttledHandleScroll = () => {\n            if (!ticking) {\n                requestAnimationFrame(() => {\n                    handleScroll();\n                    ticking = false;\n                });\n                ticking = true;\n            }\n        };\n\n        window.addEventListener('scroll', throttledHandleScroll, { passive: true });\n\n        return () => {\n            window.removeEventListener('scroll', throttledHandleScroll);\n        };\n    }, []);\n\n    const toggleMenu = useCallback(() => {\n        setIsMenuOpen(prev => !prev);\n    }, []);\n\n    const closeMenu = useCallback(() => {\n        setIsMenuOpen(false);\n    }, []);\n\n    // Memoize navigation links to prevent unnecessary re-renders\n    const navigationLinks = useMemo(() => [\n        { to: '/', label: t('home') },\n        { to: '/products', label: t('products') },\n        { to: '/configurator', label: t('customFurniture') },\n        { to: '/gallery', label: t('gallery') },\n        { to: '/about', label: t('about') },\n        { to: '/contact', label: t('contact') },\n        { to: '/payment', label: t('payments') }\n    ], [t]);\n\n    // Memoize user actions to prevent re-renders\n    const userActions = useMemo(() => {\n        if (isAuthenticated) {\n            return (\n                <div className=\"user-menu\">\n                    <Link to=\"/account\" className=\"action-btn user-btn\">\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                            <path d=\"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n                        </svg>\n                    </Link>\n                    <span className=\"user-greeting\">Hello, {user?.firstName}</span>\n                    <button onClick={handleLogout} className=\"logout-btn\">\n                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                            <path d=\"M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9M16 17L21 12M21 12L16 7M21 12H9\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n                        </svg>\n                    </button>\n                </div>\n            );\n        } else {\n            return (\n                <Link to=\"/login\" className=\"action-btn login-btn\">\n                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path d=\"M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15M10 17L15 12M15 12L10 7M15 12H3\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n                    </svg>\n                </Link>\n            );\n        }\n    }, [isAuthenticated, user?.firstName, handleLogout]);\n\n    return (\n        <header className=\"header\">\n            {/* Special Offer Banner */}\n            <div className=\"special-offer-banner\">\n                <div className=\"container\">\n                    <div className=\"offer-content\">\n                        <span className=\"offer-icon\">⚡</span>\n                        <span className=\"offer-text\">{t('specialOffer')}</span>\n                        <span className=\"offer-details\">{t('offerText')}</span>\n                        <button className=\"offer-shop-btn\">{t('shopNow')}</button>\n                        <button className=\"offer-close\" onClick={() => document.querySelector('.special-offer-banner').style.display = 'none'}>×</button>\n                    </div>\n                </div>\n            </div>\n\n            {/* Top Header Info */}\n            <div className=\"top-header\">\n                <div className=\"container\">\n                    <div className=\"top-header-content\">\n                        <div className=\"contact-info\">\n                            <span className=\"contact-item\">\n                                <svg\n                                    width=\"14\"\n                                    height=\"14\"\n                                    viewBox=\"0 0 24 24\"\n                                    fill=\"none\"\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    style={{ marginRight: '0.5rem' }}\n                                >\n                                    <path\n                                        d=\"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z\"\n                                        stroke=\"#F0B21B\"\n                                        strokeWidth=\"2\"\n                                        strokeLinecap=\"round\"\n                                        strokeLinejoin=\"round\"\n                                    />\n                                    <polyline\n                                        points=\"22,6 12,13 2,6\"\n                                        stroke=\"#F0B21B\"\n                                        strokeWidth=\"2\"\n                                        strokeLinecap=\"round\"\n                                        strokeLinejoin=\"round\"\n                                    />\n                                </svg>\n                                <EMAIL>\n                            </span>\n                            <span className=\"contact-item\">\n                                <svg\n                                    width=\"14\"\n                                    height=\"14\"\n                                    viewBox=\"0 0 24 24\"\n                                    fill=\"none\"\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    style={{ marginRight: '0.5rem' }}\n                                >\n                                    <path\n                                        d=\"M22 16.92V19.92C22.0011 20.1985 21.9441 20.4742 21.8325 20.7293C21.7209 20.9845 21.5573 21.2136 21.3521 21.4019C21.1468 21.5901 20.9046 21.7335 20.6407 21.8227C20.3769 21.9119 20.0974 21.9451 19.82 21.92C16.7428 21.5856 13.787 20.5341 11.19 18.85C8.77382 17.3147 6.72533 15.2662 5.18999 12.85C3.49997 10.2412 2.44824 7.27099 2.11999 4.18C2.095 3.90347 2.12787 3.62476 2.21649 3.36162C2.30512 3.09849 2.44756 2.85669 2.63476 2.65162C2.82196 2.44655 3.0498 2.28271 3.30379 2.17052C3.55777 2.05833 3.83233 2.00026 4.10999 2H7.10999C7.59531 1.99522 8.06579 2.16708 8.43376 2.48353C8.80173 2.79999 9.04207 3.23945 9.10999 3.72C9.23662 4.68007 9.47144 5.62273 9.80999 6.53C9.94454 6.88792 9.97366 7.27691 9.8939 7.65088C9.81415 8.02485 9.62886 8.36811 9.35999 8.64L8.08999 9.91C9.51355 12.4135 11.5865 14.4864 14.09 15.91L15.36 14.64C15.6319 14.3711 15.9751 14.1858 16.3491 14.1061C16.7231 14.0263 17.1121 14.0555 17.47 14.19C18.3773 14.5286 19.3199 14.7634 20.28 14.89C20.7658 14.9585 21.2094 15.2032 21.5265 15.5775C21.8437 15.9518 22.0122 16.4296 22 16.92Z\"\n                                        stroke=\"#F0B21B\"\n                                        strokeWidth=\"2\"\n                                        strokeLinecap=\"round\"\n                                        strokeLinejoin=\"round\"\n                                    />\n                                </svg>\n                                (02) 413-6682\n                            </span>\n                        </div>\n                        <div className=\"location-info\">\n                            #1 Binakla Street Cor Biak na Bato Brgy, Manresa, Quezon City\n                        </div>\n                        <CurrencyLanguageSelector />\n                    </div>\n                </div>\n            </div>\n\n            {/* Unified Header Container - Main Header + Navigation */}\n            <div className={`unified-header-container ${isNavHidden ? 'unified-hidden' : ''} ${isNavFloating ? 'unified-floating' : ''}`}>\n                {/* Main Header */}\n                <div className=\"main-header\">\n                <div className=\"container\">\n                    <div className=\"main-header-content\">\n                        {/* Search */}\n                        <div className=\"header-search\">\n                            <SearchInput\n                                className=\"header-search-input\"\n                                placeholder={t('searchProducts')}\n                            />\n                        </div>\n\n                        {/* Centered Logo */}\n                        <div className=\"header-logo\">\n                            <Link to=\"/\" className=\"logo\">\n                                <Logo size=\"default\" />\n                            </Link>\n                            <div className=\"logo-tagline\">EXCELLENCE IN DESIGN</div>\n                        </div>\n\n                        {/* User Actions */}\n                        <div className=\"header-actions\">\n                            {/* Shopping Cart */}\n                            <CartIcon />\n\n                            {/* Contact/Message */}\n                            <Link to=\"/contact\" className=\"action-btn contact-btn\">\n                                <svg\n                                    width=\"20\"\n                                    height=\"20\"\n                                    viewBox=\"0 0 24 24\"\n                                    fill=\"none\"\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                >\n                                    <path\n                                        d=\"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z\"\n                                        stroke=\"#F0B21B\"\n                                        strokeWidth=\"2\"\n                                        strokeLinecap=\"round\"\n                                        strokeLinejoin=\"round\"\n                                    />\n                                    <polyline\n                                        points=\"22,6 12,13 2,6\"\n                                        stroke=\"#F0B21B\"\n                                        strokeWidth=\"2\"\n                                        strokeLinecap=\"round\"\n                                        strokeLinejoin=\"round\"\n                                    />\n                                </svg>\n                            </Link>\n\n                            {/* User Authentication */}\n                            {isAuthenticated ? (\n                                <div className=\"user-menu\">\n                                    <Link to=\"/account\" className=\"action-btn user-btn\">\n                                        <svg\n                                            width=\"20\"\n                                            height=\"20\"\n                                            viewBox=\"0 0 24 24\"\n                                            fill=\"none\"\n                                            xmlns=\"http://www.w3.org/2000/svg\"\n                                        >\n                                            <path\n                                                d=\"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z\"\n                                                stroke=\"#F0B21B\"\n                                                strokeWidth=\"2\"\n                                                strokeLinecap=\"round\"\n                                                strokeLinejoin=\"round\"\n                                            />\n                                        </svg>\n                                    </Link>\n                                    <span className=\"user-greeting\">\n                                        Hello, {user?.firstName}\n                                    </span>\n                                    <button onClick={handleLogout} className=\"logout-btn\">\n                                        <svg\n                                            width=\"16\"\n                                            height=\"16\"\n                                            viewBox=\"0 0 24 24\"\n                                            fill=\"none\"\n                                            xmlns=\"http://www.w3.org/2000/svg\"\n                                        >\n                                            <path\n                                                d=\"M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9\"\n                                                stroke=\"currentColor\"\n                                                strokeWidth=\"2\"\n                                                strokeLinecap=\"round\"\n                                                strokeLinejoin=\"round\"\n                                            />\n                                            <polyline\n                                                points=\"16,17 21,12 16,7\"\n                                                stroke=\"currentColor\"\n                                                strokeWidth=\"2\"\n                                                strokeLinecap=\"round\"\n                                                strokeLinejoin=\"round\"\n                                            />\n                                            <line\n                                                x1=\"21\"\n                                                y1=\"12\"\n                                                x2=\"9\"\n                                                y2=\"12\"\n                                                stroke=\"currentColor\"\n                                                strokeWidth=\"2\"\n                                                strokeLinecap=\"round\"\n                                                strokeLinejoin=\"round\"\n                                            />\n                                        </svg>\n                                        <span>Logout</span>\n                                    </button>\n                                </div>\n                            ) : (\n                                <Link to=\"/login\" className=\"action-btn user-btn\">\n                                    <svg\n                                        width=\"20\"\n                                        height=\"20\"\n                                        viewBox=\"0 0 24 24\"\n                                        fill=\"none\"\n                                        xmlns=\"http://www.w3.org/2000/svg\"\n                                    >\n                                        <path\n                                            d=\"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z\"\n                                            stroke=\"#F0B21B\"\n                                            strokeWidth=\"2\"\n                                            strokeLinecap=\"round\"\n                                            strokeLinejoin=\"round\"\n                                        />\n                                    </svg>\n                                </Link>\n                            )}\n\n                            {/* Mobile Menu Toggle */}\n                            <button\n                                className=\"mobile-menu-toggle\"\n                                onClick={toggleMenu}\n                                aria-label=\"Toggle menu\"\n                            >\n                                <span></span>\n                                <span></span>\n                                <span></span>\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n                {/* Navigation Bar */}\n                <div className=\"navigation-bar\">\n                    <div className=\"container\">\n                        <nav className=\"main-navigation\">\n                            <Link to=\"/\" className=\"nav-link\">{t('home')}</Link>\n                            <Link to=\"/products\" className=\"nav-link\">{t('products')}</Link>\n                            <Link to=\"/configurator\" className=\"nav-link\">{t('customFurniture')}</Link>\n                            <Link to=\"/gallery\" className=\"nav-link\">{t('gallery')}</Link>\n                            <Link to=\"/about\" className=\"nav-link\">{t('about')}</Link>\n                            <Link to=\"/contact\" className=\"nav-link\">{t('contact')}</Link>\n                            <Link to=\"/payment\" className=\"nav-link\">{t('payments')}</Link>\n                        </nav>\n                    </div>\n                </div>\n            </div>\n\n            {/* Mobile Navigation */}\n            <nav className={`mobile-nav ${isMenuOpen ? 'open' : ''}`}>\n                <Link\n                    to=\"/\"\n                    className=\"mobile-nav-link\"\n                    onClick={() => setIsMenuOpen(false)}\n                >\n                    {t('home')}\n                </Link>\n                <Link\n                    to=\"/products\"\n                    className=\"mobile-nav-link\"\n                    onClick={() => setIsMenuOpen(false)}\n                >\n                    {t('products')}\n                </Link>\n                <Link\n                    to=\"/configurator\"\n                    className=\"mobile-nav-link\"\n                    onClick={() => setIsMenuOpen(false)}\n                >\n                    {t('customFurniture')}\n                </Link>\n                <Link\n                    to=\"/gallery\"\n                    className=\"mobile-nav-link\"\n                    onClick={() => setIsMenuOpen(false)}\n                >\n                    {t('gallery')}\n                </Link>\n                <Link\n                    to=\"/about\"\n                    className=\"mobile-nav-link\"\n                    onClick={() => setIsMenuOpen(false)}\n                >\n                    {t('about')}\n                </Link>\n                <Link\n                    to=\"/contact\"\n                    className=\"mobile-nav-link\"\n                    onClick={() => setIsMenuOpen(false)}\n                >\n                    {t('contact')}\n                </Link>\n                <Link\n                    to=\"/payment\"\n                    className=\"mobile-nav-link\"\n                    onClick={() => setIsMenuOpen(false)}\n                >\n                    {t('payments')}\n                </Link>\n\n                {!isAuthenticated && (\n                    <Link\n                        to=\"/login\"\n                        className=\"mobile-nav-link login\"\n                        onClick={() => setIsMenuOpen(false)}\n                    >\n                        Login\n                    </Link>\n                )}\n            </nav>\n        </header>\n    );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AAChF,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM;IAAEC,IAAI;IAAEC,MAAM;IAAEC;EAAgB,CAAC,GAAGZ,OAAO,CAAC,CAAC;EACnD,MAAM;IAAEa;EAAE,CAAC,GAAGZ,WAAW,CAAC,CAAC;EAC3B,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMuB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM4B,WAAW,GAAG1B,MAAM,CAAC,CAAC,CAAC;EAC7B,MAAM2B,eAAe,GAAG,GAAG;EAE3B,MAAMC,YAAY,GAAG3B,WAAW,CAAC,MAAM;IACnC;IACA,IAAI4B,MAAM,CAACC,OAAO,CAAC,kCAAkC,CAAC,EAAE;MACpDd,MAAM,CAAC,CAAC;MACRK,QAAQ,CAAC,GAAG,CAAC;MACbD,aAAa,CAAC,KAAK,CAAC;IACxB;EACJ,CAAC,EAAE,CAACJ,MAAM,EAAEK,QAAQ,CAAC,CAAC;;EAEtB;EACAtB,SAAS,CAAC,MAAM;IACZ,MAAMgC,YAAY,GAAGA,CAAA,KAAM;MACvB,MAAMC,cAAc,GAAGH,MAAM,CAACI,OAAO;;MAErC;MACA,IAAID,cAAc,GAAGL,eAAe,EAAE;QAClCJ,cAAc,CAAC,KAAK,CAAC;QACrBE,gBAAgB,CAAC,KAAK,CAAC;QACvBC,WAAW,CAACQ,OAAO,GAAGF,cAAc;QACpC;MACJ;;MAEA;MACA,MAAMG,aAAa,GAAGH,cAAc,GAAGN,WAAW,CAACQ,OAAO;MAC1D,MAAME,WAAW,GAAGJ,cAAc,GAAGN,WAAW,CAACQ,OAAO;;MAExD;MACA,IAAIC,aAAa,IAAIH,cAAc,GAAGL,eAAe,EAAE;QACnDJ,cAAc,CAAC,IAAI,CAAC;QACpBE,gBAAgB,CAAC,IAAI,CAAC;MAC1B,CAAC,MAAM,IAAIW,WAAW,EAAE;QACpBb,cAAc,CAAC,KAAK,CAAC;QACrBE,gBAAgB,CAAC,IAAI,CAAC;MAC1B;MAEAC,WAAW,CAACQ,OAAO,GAAGF,cAAc;IACxC,CAAC;;IAED;IACA,IAAIK,OAAO,GAAG,KAAK;IACnB,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;MAChC,IAAI,CAACD,OAAO,EAAE;QACVE,qBAAqB,CAAC,MAAM;UACxBR,YAAY,CAAC,CAAC;UACdM,OAAO,GAAG,KAAK;QACnB,CAAC,CAAC;QACFA,OAAO,GAAG,IAAI;MAClB;IACJ,CAAC;IAEDR,MAAM,CAACW,gBAAgB,CAAC,QAAQ,EAAEF,qBAAqB,EAAE;MAAEG,OAAO,EAAE;IAAK,CAAC,CAAC;IAE3E,OAAO,MAAM;MACTZ,MAAM,CAACa,mBAAmB,CAAC,QAAQ,EAAEJ,qBAAqB,CAAC;IAC/D,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,UAAU,GAAG1C,WAAW,CAAC,MAAM;IACjCmB,aAAa,CAACwB,IAAI,IAAI,CAACA,IAAI,CAAC;EAChC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,SAAS,GAAG5C,WAAW,CAAC,MAAM;IAChCmB,aAAa,CAAC,KAAK,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM0B,eAAe,GAAG5C,OAAO,CAAC,MAAM,CAClC;IAAE6C,EAAE,EAAE,GAAG;IAAEC,KAAK,EAAE9B,CAAC,CAAC,MAAM;EAAE,CAAC,EAC7B;IAAE6B,EAAE,EAAE,WAAW;IAAEC,KAAK,EAAE9B,CAAC,CAAC,UAAU;EAAE,CAAC,EACzC;IAAE6B,EAAE,EAAE,eAAe;IAAEC,KAAK,EAAE9B,CAAC,CAAC,iBAAiB;EAAE,CAAC,EACpD;IAAE6B,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE9B,CAAC,CAAC,SAAS;EAAE,CAAC,EACvC;IAAE6B,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE9B,CAAC,CAAC,OAAO;EAAE,CAAC,EACnC;IAAE6B,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE9B,CAAC,CAAC,SAAS;EAAE,CAAC,EACvC;IAAE6B,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE9B,CAAC,CAAC,UAAU;EAAE,CAAC,CAC3C,EAAE,CAACA,CAAC,CAAC,CAAC;;EAEP;EACA,MAAM+B,WAAW,GAAG/C,OAAO,CAAC,MAAM;IAC9B,IAAIe,eAAe,EAAE;MACjB,oBACIL,OAAA;QAAKsC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACtBvC,OAAA,CAACT,IAAI;UAAC4C,EAAE,EAAC,UAAU;UAACG,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAC/CvC,OAAA;YAAKwC,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,KAAK,EAAC,4BAA4B;YAAAL,QAAA,eAC1FvC,OAAA;cAAM6C,CAAC,EAAC,oRAAoR;cAACC,MAAM,EAAC,SAAS;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5W;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACPrD,OAAA;UAAMsC,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAC,SAAO,EAACpC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmD,SAAS;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC/DrD,OAAA;UAAQuD,OAAO,EAAEvC,YAAa;UAACsB,SAAS,EAAC,YAAY;UAAAC,QAAA,eACjDvC,OAAA;YAAKwC,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,KAAK,EAAC,4BAA4B;YAAAL,QAAA,eAC1FvC,OAAA;cAAM6C,CAAC,EAAC,8LAA8L;cAACC,MAAM,EAAC,SAAS;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAEd,CAAC,MAAM;MACH,oBACIrD,OAAA,CAACT,IAAI;QAAC4C,EAAE,EAAC,QAAQ;QAACG,SAAS,EAAC,sBAAsB;QAAAC,QAAA,eAC9CvC,OAAA;UAAKwC,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,KAAK,EAAC,4BAA4B;UAAAL,QAAA,eAC1FvC,OAAA;YAAM6C,CAAC,EAAC,qMAAqM;YAACC,MAAM,EAAC,SAAS;YAACC,WAAW,EAAC,GAAG;YAACC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7R;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAEf;EACJ,CAAC,EAAE,CAAChD,eAAe,EAAEF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmD,SAAS,EAAEtC,YAAY,CAAC,CAAC;EAEpD,oBACIhB,OAAA;IAAQsC,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBAEtBvC,OAAA;MAAKsC,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACjCvC,OAAA;QAAKsC,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtBvC,OAAA;UAAKsC,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC1BvC,OAAA;YAAMsC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAC;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrCrD,OAAA;YAAMsC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEjC,CAAC,CAAC,cAAc;UAAC;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvDrD,OAAA;YAAMsC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEjC,CAAC,CAAC,WAAW;UAAC;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvDrD,OAAA;YAAQsC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAEjC,CAAC,CAAC,SAAS;UAAC;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAC1DrD,OAAA;YAAQsC,SAAS,EAAC,aAAa;YAACiB,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,aAAa,CAAC,uBAAuB,CAAC,CAACC,KAAK,CAACC,OAAO,GAAG,MAAO;YAAApB,QAAA,EAAC;UAAC;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNrD,OAAA;MAAKsC,SAAS,EAAC,YAAY;MAAAC,QAAA,eACvBvC,OAAA;QAAKsC,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtBvC,OAAA;UAAKsC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBAC/BvC,OAAA;YAAKsC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBvC,OAAA;cAAMsC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC1BvC,OAAA;gBACIwC,KAAK,EAAC,IAAI;gBACVC,MAAM,EAAC,IAAI;gBACXC,OAAO,EAAC,WAAW;gBACnBC,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAC,4BAA4B;gBAClCc,KAAK,EAAE;kBAAEE,WAAW,EAAE;gBAAS,CAAE;gBAAArB,QAAA,gBAEjCvC,OAAA;kBACI6C,CAAC,EAAC,6FAA6F;kBAC/FC,MAAM,EAAC,SAAS;kBAChBC,WAAW,EAAC,GAAG;kBACfC,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACFrD,OAAA;kBACI6D,MAAM,EAAC,gBAAgB;kBACvBf,MAAM,EAAC,SAAS;kBAChBC,WAAW,EAAC,GAAG;kBACfC,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,+BAEV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPrD,OAAA;cAAMsC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC1BvC,OAAA;gBACIwC,KAAK,EAAC,IAAI;gBACVC,MAAM,EAAC,IAAI;gBACXC,OAAO,EAAC,WAAW;gBACnBC,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAC,4BAA4B;gBAClCc,KAAK,EAAE;kBAAEE,WAAW,EAAE;gBAAS,CAAE;gBAAArB,QAAA,eAEjCvC,OAAA;kBACI6C,CAAC,EAAC,+hCAA+hC;kBACjiCC,MAAM,EAAC,SAAS;kBAChBC,WAAW,EAAC,GAAG;kBACfC,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,iBAEV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNrD,OAAA;YAAKsC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE/B;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNrD,OAAA,CAACH,wBAAwB;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNrD,OAAA;MAAKsC,SAAS,EAAE,4BAA4B5B,WAAW,GAAG,gBAAgB,GAAG,EAAE,IAAIE,aAAa,GAAG,kBAAkB,GAAG,EAAE,EAAG;MAAA2B,QAAA,gBAEzHvC,OAAA;QAAKsC,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC5BvC,OAAA;UAAKsC,SAAS,EAAC,WAAW;UAAAC,QAAA,eACtBvC,OAAA;YAAKsC,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAEhCvC,OAAA;cAAKsC,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC1BvC,OAAA,CAACF,WAAW;gBACRwC,SAAS,EAAC,qBAAqB;gBAC/BwB,WAAW,EAAExD,CAAC,CAAC,gBAAgB;cAAE;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGNrD,OAAA;cAAKsC,SAAS,EAAC,aAAa;cAAAC,QAAA,gBACxBvC,OAAA,CAACT,IAAI;gBAAC4C,EAAE,EAAC,GAAG;gBAACG,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACzBvC,OAAA,CAACJ,IAAI;kBAACmE,IAAI,EAAC;gBAAS;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eACPrD,OAAA;gBAAKsC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAoB;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eAGNrD,OAAA;cAAKsC,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAE3BvC,OAAA,CAACL,QAAQ;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAGZrD,OAAA,CAACT,IAAI;gBAAC4C,EAAE,EAAC,UAAU;gBAACG,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eAClDvC,OAAA;kBACIwC,KAAK,EAAC,IAAI;kBACVC,MAAM,EAAC,IAAI;kBACXC,OAAO,EAAC,WAAW;kBACnBC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAC,4BAA4B;kBAAAL,QAAA,gBAElCvC,OAAA;oBACI6C,CAAC,EAAC,6FAA6F;oBAC/FC,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAC,GAAG;oBACfC,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACFrD,OAAA;oBACI6D,MAAM,EAAC,gBAAgB;oBACvBf,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAC,GAAG;oBACfC,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,EAGNhD,eAAe,gBACZL,OAAA;gBAAKsC,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACtBvC,OAAA,CAACT,IAAI;kBAAC4C,EAAE,EAAC,UAAU;kBAACG,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,eAC/CvC,OAAA;oBACIwC,KAAK,EAAC,IAAI;oBACVC,MAAM,EAAC,IAAI;oBACXC,OAAO,EAAC,WAAW;oBACnBC,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAC,4BAA4B;oBAAAL,QAAA,eAElCvC,OAAA;sBACI6C,CAAC,EAAC,oRAAoR;sBACtRC,MAAM,EAAC,SAAS;sBAChBC,WAAW,EAAC,GAAG;sBACfC,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACPrD,OAAA;kBAAMsC,SAAS,EAAC,eAAe;kBAAAC,QAAA,GAAC,SACrB,EAACpC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmD,SAAS;gBAAA;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACPrD,OAAA;kBAAQuD,OAAO,EAAEvC,YAAa;kBAACsB,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACjDvC,OAAA;oBACIwC,KAAK,EAAC,IAAI;oBACVC,MAAM,EAAC,IAAI;oBACXC,OAAO,EAAC,WAAW;oBACnBC,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAC,4BAA4B;oBAAAL,QAAA,gBAElCvC,OAAA;sBACI6C,CAAC,EAAC,+JAA+J;sBACjKC,MAAM,EAAC,cAAc;sBACrBC,WAAW,EAAC,GAAG;sBACfC,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC,eACFrD,OAAA;sBACI6D,MAAM,EAAC,kBAAkB;sBACzBf,MAAM,EAAC,cAAc;sBACrBC,WAAW,EAAC,GAAG;sBACfC,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC,eACFrD,OAAA;sBACIgE,EAAE,EAAC,IAAI;sBACPC,EAAE,EAAC,IAAI;sBACPC,EAAE,EAAC,GAAG;sBACNC,EAAE,EAAC,IAAI;sBACPrB,MAAM,EAAC,cAAc;sBACrBC,WAAW,EAAC,GAAG;sBACfC,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACNrD,OAAA;oBAAAuC,QAAA,EAAM;kBAAM;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,gBAENrD,OAAA,CAACT,IAAI;gBAAC4C,EAAE,EAAC,QAAQ;gBAACG,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,eAC7CvC,OAAA;kBACIwC,KAAK,EAAC,IAAI;kBACVC,MAAM,EAAC,IAAI;kBACXC,OAAO,EAAC,WAAW;kBACnBC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAC,4BAA4B;kBAAAL,QAAA,eAElCvC,OAAA;oBACI6C,CAAC,EAAC,oRAAoR;oBACtRC,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAC,GAAG;oBACfC,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACT,eAGDrD,OAAA;gBACIsC,SAAS,EAAC,oBAAoB;gBAC9BiB,OAAO,EAAExB,UAAW;gBACpB,cAAW,aAAa;gBAAAQ,QAAA,gBAExBvC,OAAA;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbrD,OAAA;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbrD,OAAA;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGFrD,OAAA;QAAKsC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC3BvC,OAAA;UAAKsC,SAAS,EAAC,WAAW;UAAAC,QAAA,eACtBvC,OAAA;YAAKsC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC5BvC,OAAA,CAACT,IAAI;cAAC4C,EAAE,EAAC,GAAG;cAACG,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAEjC,CAAC,CAAC,MAAM;YAAC;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpDrD,OAAA,CAACT,IAAI;cAAC4C,EAAE,EAAC,WAAW;cAACG,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAEjC,CAAC,CAAC,UAAU;YAAC;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChErD,OAAA,CAACT,IAAI;cAAC4C,EAAE,EAAC,eAAe;cAACG,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAEjC,CAAC,CAAC,iBAAiB;YAAC;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3ErD,OAAA,CAACT,IAAI;cAAC4C,EAAE,EAAC,UAAU;cAACG,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAEjC,CAAC,CAAC,SAAS;YAAC;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9DrD,OAAA,CAACT,IAAI;cAAC4C,EAAE,EAAC,QAAQ;cAACG,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAEjC,CAAC,CAAC,OAAO;YAAC;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1DrD,OAAA,CAACT,IAAI;cAAC4C,EAAE,EAAC,UAAU;cAACG,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAEjC,CAAC,CAAC,SAAS;YAAC;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9DrD,OAAA,CAACT,IAAI;cAAC4C,EAAE,EAAC,UAAU;cAACG,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAEjC,CAAC,CAAC,UAAU;YAAC;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNrD,OAAA;MAAKsC,SAAS,EAAE,cAAc/B,UAAU,GAAG,MAAM,GAAG,EAAE,EAAG;MAAAgC,QAAA,gBACrDvC,OAAA,CAACT,IAAI;QACD4C,EAAE,EAAC,GAAG;QACNG,SAAS,EAAC,iBAAiB;QAC3BiB,OAAO,EAAEA,CAAA,KAAM/C,aAAa,CAAC,KAAK,CAAE;QAAA+B,QAAA,EAEnCjC,CAAC,CAAC,MAAM;MAAC;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eACPrD,OAAA,CAACT,IAAI;QACD4C,EAAE,EAAC,WAAW;QACdG,SAAS,EAAC,iBAAiB;QAC3BiB,OAAO,EAAEA,CAAA,KAAM/C,aAAa,CAAC,KAAK,CAAE;QAAA+B,QAAA,EAEnCjC,CAAC,CAAC,UAAU;MAAC;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eACPrD,OAAA,CAACT,IAAI;QACD4C,EAAE,EAAC,eAAe;QAClBG,SAAS,EAAC,iBAAiB;QAC3BiB,OAAO,EAAEA,CAAA,KAAM/C,aAAa,CAAC,KAAK,CAAE;QAAA+B,QAAA,EAEnCjC,CAAC,CAAC,iBAAiB;MAAC;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eACPrD,OAAA,CAACT,IAAI;QACD4C,EAAE,EAAC,UAAU;QACbG,SAAS,EAAC,iBAAiB;QAC3BiB,OAAO,EAAEA,CAAA,KAAM/C,aAAa,CAAC,KAAK,CAAE;QAAA+B,QAAA,EAEnCjC,CAAC,CAAC,SAAS;MAAC;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eACPrD,OAAA,CAACT,IAAI;QACD4C,EAAE,EAAC,QAAQ;QACXG,SAAS,EAAC,iBAAiB;QAC3BiB,OAAO,EAAEA,CAAA,KAAM/C,aAAa,CAAC,KAAK,CAAE;QAAA+B,QAAA,EAEnCjC,CAAC,CAAC,OAAO;MAAC;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACPrD,OAAA,CAACT,IAAI;QACD4C,EAAE,EAAC,UAAU;QACbG,SAAS,EAAC,iBAAiB;QAC3BiB,OAAO,EAAEA,CAAA,KAAM/C,aAAa,CAAC,KAAK,CAAE;QAAA+B,QAAA,EAEnCjC,CAAC,CAAC,SAAS;MAAC;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eACPrD,OAAA,CAACT,IAAI;QACD4C,EAAE,EAAC,UAAU;QACbG,SAAS,EAAC,iBAAiB;QAC3BiB,OAAO,EAAEA,CAAA,KAAM/C,aAAa,CAAC,KAAK,CAAE;QAAA+B,QAAA,EAEnCjC,CAAC,CAAC,UAAU;MAAC;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,EAEN,CAAChD,eAAe,iBACbL,OAAA,CAACT,IAAI;QACD4C,EAAE,EAAC,QAAQ;QACXG,SAAS,EAAC,uBAAuB;QACjCiB,OAAO,EAAEA,CAAA,KAAM/C,aAAa,CAAC,KAAK,CAAE;QAAA+B,QAAA,EACvC;MAED;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEjB,CAAC;AAACnD,EAAA,CAlaID,MAAM;EAAA,QACkCR,OAAO,EACnCC,WAAW,EAERF,WAAW;AAAA;AAAA4E,EAAA,GAJ1BnE,MAAM;AAoaZ,eAAeA,MAAM;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}