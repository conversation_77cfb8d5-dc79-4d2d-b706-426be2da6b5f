@echo off
title Office E-commerce Development Servers
echo.
echo ========================================
echo   Office E-commerce Development Setup
echo ========================================
echo.

echo [1/3] Starting Backend Server...
cd /d "%~dp0backend"
start "Backend Server - Port 5001" cmd /k "echo Starting Backend Server... && node server.js"

echo [2/3] Waiting 5 seconds for backend to initialize...
timeout /t 5 /nobreak > nul

echo [3/3] Starting Frontend Server...
cd /d "%~dp0frontend"
start "Frontend Server - Port 3000" cmd /k "echo Starting Frontend Server... && npx react-scripts start"

echo.
echo ========================================
echo   Servers Starting...
echo ========================================
echo.
echo Backend:  http://localhost:5001
echo Frontend: http://localhost:3000
echo Admin:    http://localhost:3000/admin
echo.
echo Login Credentials:
echo - Admin: <EMAIL> / admin123
echo - Manager: <EMAIL> / admin123
echo.
echo Both servers will open in separate windows.
echo Close those windows to stop the servers.
echo.
echo Press any key to open the application in browser...
pause > nul

echo Opening application...
start http://localhost:3000

echo.
echo Setup complete! Check the server windows for any errors.
pause
