[{"C:\\DesignXcel\\office-ecommerce\\frontend\\src\\index.js": "1", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\reportWebVitals.js": "2", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\App.js": "3", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CartContext.js": "4", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CurrencyContext.js": "5", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\LanguageContext.js": "6", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Login.js": "7", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Home.js": "8", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useAuth.js": "9", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductCatalog.js": "10", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductDetail.js": "11", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Gallery.js": "12", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Payment.js": "13", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\CheckoutPage.js": "14", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Account.js": "15", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\OrderSuccessPage.js": "16", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Cart.js": "17", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\About.js": "18", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\admin\\AdminDashboard.js": "19", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Footer.js": "20", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\demo\\ProductCardDemo.js": "21", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Header.js": "22", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\AdminRoute.js": "23", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\RoleBasedRoute.js": "24", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\ProtectedRoute.js": "25", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePermissions.js": "26", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartItem.js": "27", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\3d\\3DConfigurator.js": "28", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\paymentService.js": "29", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\products.js": "30", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CheckoutModal.js": "31", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\auth.js": "32", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useWebSocket.js": "33", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\AdminLogo.js": "34", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductCard.js": "35", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\payment\\PayMongoCheckout.js": "36", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductFilter.js": "37", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\ProfileManagement.js": "38", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\OrderHistory.js": "39", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\SecuritySettings.js": "40", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AddressBook.js": "41", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AccountPreferences.js": "42", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\modals\\ConfirmationModal.js": "43", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\OrderManagement.js": "44", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ProductManagement.js": "45", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\Analytics.js": "46", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\SupplierManagement.js": "47", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\DashboardOverview.js": "48", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Logo.js": "49", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\UnauthorizedAccess.js": "50", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartIcon.js": "51", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\CurrencyLanguageSelector.js": "52", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\InventoryManagement.js": "53", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\UserManagement.js": "54", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\icons\\AdminIcons.js": "55", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchInput.js": "56", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiConfig.js": "57", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiClient.js": "58", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\api.js": "59", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePrice.js": "60", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\websocketService.js": "61", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartSidebar.js": "62", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchSuggestions.js": "63", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\modals\\ProductDetailsModal.js": "64", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\modals\\ProductFormModal.js": "65", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\FileUploadZone.js": "66", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ThreeJSPreview.js": "67", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ProductManagementNew.js": "68", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductCard.js": "69", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductForm.js": "70", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\SearchFilters.js": "71", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\index.js": "72", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\App.js": "73", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\reportWebVitals.js": "74", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CartContext.js": "75", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CurrencyContext.js": "76", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useAuth.js": "77", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\LanguageContext.js": "78", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Login.js": "79", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\OrderSuccessPage.js": "80", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Payment.js": "81", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductCatalog.js": "82", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\About.js": "83", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Account.js": "84", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductDetail.js": "85", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Home.js": "86", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Cart.js": "87", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\CheckoutPage.js": "88", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Gallery.js": "89", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\admin\\AdminDashboard.js": "90", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Header.js": "91", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\demo\\ProductCardDemo.js": "92", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Footer.js": "93", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\ProtectedRoute.js": "94", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\RoleBasedRoute.js": "95", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\AdminRoute.js": "96", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePermissions.js": "97", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\products.js": "98", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\paymentService.js": "99", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\auth.js": "100", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductFilter.js": "101", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductCard.js": "102", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useWebSocket.js": "103", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\AdminLogo.js": "104", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\OrderHistory.js": "105", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\ProfileManagement.js": "106", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AccountPreferences.js": "107", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\modals\\ConfirmationModal.js": "108", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\SecuritySettings.js": "109", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CheckoutModal.js": "110", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AddressBook.js": "111", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\3d\\3DConfigurator.js": "112", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartItem.js": "113", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\OrderManagement.js": "114", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\payment\\PayMongoCheckout.js": "115", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\DashboardOverview.js": "116", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\UserManagement.js": "117", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\SupplierManagement.js": "118", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ProductManagementNew.js": "119", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\Analytics.js": "120", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\InventoryManagement.js": "121", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartIcon.js": "122", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\CurrencyLanguageSelector.js": "123", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Logo.js": "124", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\UnauthorizedAccess.js": "125", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\icons\\AdminIcons.js": "126", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchInput.js": "127", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\websocketService.js": "128", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\api.js": "129", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiClient.js": "130", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiConfig.js": "131", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePrice.js": "132", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartSidebar.js": "133", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchSuggestions.js": "134", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\SearchFilters.js": "135", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductCard.js": "136", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductForm.js": "137", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ThreeJSPreview.js": "138", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\FileUploadZone.js": "139", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\constants\\index.js": "140", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\utils\\index.js": "141"}, {"size": 535, "mtime": 1748532508000, "results": "142", "hashOfConfig": "143"}, {"size": 362, "mtime": 1748532510000, "results": "144", "hashOfConfig": "143"}, {"size": 6974, "mtime": 1752638501090, "results": "145", "hashOfConfig": "143"}, {"size": 5553, "mtime": 1748536576000, "results": "146", "hashOfConfig": "143"}, {"size": 4139, "mtime": 1751076622000, "results": "147", "hashOfConfig": "143"}, {"size": 6574, "mtime": 1751098548000, "results": "148", "hashOfConfig": "143"}, {"size": 21653, "mtime": 1752638528971, "results": "149", "hashOfConfig": "143"}, {"size": 9763, "mtime": 1751248890000, "results": "150", "hashOfConfig": "143"}, {"size": 4069, "mtime": 1752638340976, "results": "151", "hashOfConfig": "143"}, {"size": 18118, "mtime": 1751248890000, "results": "152", "hashOfConfig": "143"}, {"size": 12309, "mtime": 1752639276135, "results": "153", "hashOfConfig": "143"}, {"size": 7100, "mtime": 1750777438000, "results": "154", "hashOfConfig": "143"}, {"size": 13424, "mtime": 1748592600000, "results": "155", "hashOfConfig": "143"}, {"size": 23206, "mtime": 1751291576000, "results": "156", "hashOfConfig": "143"}, {"size": 5296, "mtime": 1751375904000, "results": "157", "hashOfConfig": "143"}, {"size": 11069, "mtime": 1751278342000, "results": "158", "hashOfConfig": "143"}, {"size": 9980, "mtime": 1751289364000, "results": "159", "hashOfConfig": "143"}, {"size": 11059, "mtime": 1750776056000, "results": "160", "hashOfConfig": "143"}, {"size": 8110, "mtime": 1752560617468, "results": "161", "hashOfConfig": "143"}, {"size": 8266, "mtime": 1748588852000, "results": "162", "hashOfConfig": "143"}, {"size": 5949, "mtime": 1748749522000, "results": "163", "hashOfConfig": "143"}, {"size": 15183, "mtime": 1752639970600, "results": "164", "hashOfConfig": "143"}, {"size": 1304, "mtime": 1751274452000, "results": "165", "hashOfConfig": "143"}, {"size": 1283, "mtime": 1751274444000, "results": "166", "hashOfConfig": "143"}, {"size": 809, "mtime": 1751274436000, "results": "167", "hashOfConfig": "143"}, {"size": 4827, "mtime": 1751274510000, "results": "168", "hashOfConfig": "143"}, {"size": 5314, "mtime": 1751292028000, "results": "169", "hashOfConfig": "143"}, {"size": 53614, "mtime": 1751251970000, "results": "170", "hashOfConfig": "143"}, {"size": 7098, "mtime": 1751277960000, "results": "171", "hashOfConfig": "143"}, {"size": 11351, "mtime": 1751114552000, "results": "172", "hashOfConfig": "143"}, {"size": 3375, "mtime": 1751249990000, "results": "173", "hashOfConfig": "143"}, {"size": 975, "mtime": 1752563029324, "results": "174", "hashOfConfig": "143"}, {"size": 5117, "mtime": 1752560617473, "results": "175", "hashOfConfig": "143"}, {"size": 2754, "mtime": 1751275216000, "results": "176", "hashOfConfig": "143"}, {"size": 11325, "mtime": 1752640390854, "results": "177", "hashOfConfig": "143"}, {"size": 10084, "mtime": 1751440302000, "results": "178", "hashOfConfig": "143"}, {"size": 5674, "mtime": 1748533678000, "results": "179", "hashOfConfig": "143"}, {"size": 10237, "mtime": 1751375976000, "results": "180", "hashOfConfig": "143"}, {"size": 13243, "mtime": 1751376032000, "results": "181", "hashOfConfig": "143"}, {"size": 20686, "mtime": 1751376374000, "results": "182", "hashOfConfig": "143"}, {"size": 19762, "mtime": 1751376110000, "results": "183", "hashOfConfig": "143"}, {"size": 13935, "mtime": 1751376296000, "results": "184", "hashOfConfig": "143"}, {"size": 3302, "mtime": 1751251038000, "results": "185", "hashOfConfig": "143"}, {"size": 23502, "mtime": 1751440302000, "results": "186", "hashOfConfig": "143"}, {"size": 17469, "mtime": 1751459722103, "results": "187", "hashOfConfig": "188"}, {"size": 8949, "mtime": 1751275712000, "results": "189", "hashOfConfig": "143"}, {"size": 2536, "mtime": 1751268692000, "results": "190", "hashOfConfig": "143"}, {"size": 8611, "mtime": 1751273494000, "results": "191", "hashOfConfig": "143"}, {"size": 2632, "mtime": 1749573724000, "results": "192", "hashOfConfig": "143"}, {"size": 3344, "mtime": 1751274470000, "results": "193", "hashOfConfig": "143"}, {"size": 2001, "mtime": 1751448750000, "results": "194", "hashOfConfig": "143"}, {"size": 5719, "mtime": 1751076762000, "results": "195", "hashOfConfig": "143"}, {"size": 11918, "mtime": 1751270770000, "results": "196", "hashOfConfig": "143"}, {"size": 3006, "mtime": 1751268708000, "results": "197", "hashOfConfig": "143"}, {"size": 11855, "mtime": 1752560617455, "results": "198", "hashOfConfig": "143"}, {"size": 9556, "mtime": 1751175720000, "results": "199", "hashOfConfig": "143"}, {"size": 3823, "mtime": 1751272136000, "results": "200", "hashOfConfig": "143"}, {"size": 6129, "mtime": 1752638445938, "results": "201", "hashOfConfig": "143"}, {"size": 2327, "mtime": 1752638893258, "results": "202", "hashOfConfig": "143"}, {"size": 2725, "mtime": 1751076778000, "results": "203", "hashOfConfig": "143"}, {"size": 6794, "mtime": 1752560617473, "results": "204", "hashOfConfig": "143"}, {"size": 5487, "mtime": 1748536714000, "results": "205", "hashOfConfig": "143"}, {"size": 4182, "mtime": 1751175562000, "results": "206", "hashOfConfig": "143"}, {"size": 14750, "mtime": 1751454906562, "results": "207", "hashOfConfig": "188"}, {"size": 16346, "mtime": 1751460354209, "results": "208", "hashOfConfig": "188"}, {"size": 6346, "mtime": 1751454744000, "results": "209", "hashOfConfig": "143"}, {"size": 6908, "mtime": 1751459962000, "results": "210", "hashOfConfig": "143"}, {"size": 10679, "mtime": 1751460404000, "results": "211", "hashOfConfig": "143"}, {"size": 6209, "mtime": 1751460516000, "results": "212", "hashOfConfig": "143"}, {"size": 17111, "mtime": 1752036839812, "results": "213", "hashOfConfig": "143"}, {"size": 5819, "mtime": 1752036805302, "results": "214", "hashOfConfig": "143"}, {"size": 535, "mtime": 1748532508000, "results": "215", "hashOfConfig": "216"}, {"size": 7288, "mtime": 1752550163170, "results": "217", "hashOfConfig": "216"}, {"size": 362, "mtime": 1748532510000, "results": "218", "hashOfConfig": "216"}, {"size": 5553, "mtime": 1748536576000, "results": "219", "hashOfConfig": "216"}, {"size": 4139, "mtime": 1751076622000, "results": "220", "hashOfConfig": "216"}, {"size": 4954, "mtime": 1752549460528, "results": "221", "hashOfConfig": "216"}, {"size": 6574, "mtime": 1751098548000, "results": "222", "hashOfConfig": "216"}, {"size": 22019, "mtime": 1751370338000, "results": "223", "hashOfConfig": "216"}, {"size": 11069, "mtime": 1751278342000, "results": "224", "hashOfConfig": "216"}, {"size": 13424, "mtime": 1748592600000, "results": "225", "hashOfConfig": "216"}, {"size": 18118, "mtime": 1751248890000, "results": "226", "hashOfConfig": "216"}, {"size": 11059, "mtime": 1750776056000, "results": "227", "hashOfConfig": "216"}, {"size": 5296, "mtime": 1751375904000, "results": "228", "hashOfConfig": "216"}, {"size": 14768, "mtime": 1751099198000, "results": "229", "hashOfConfig": "216"}, {"size": 9763, "mtime": 1751248890000, "results": "230", "hashOfConfig": "216"}, {"size": 9980, "mtime": 1751289364000, "results": "231", "hashOfConfig": "216"}, {"size": 23206, "mtime": 1751291576000, "results": "232", "hashOfConfig": "216"}, {"size": 7100, "mtime": 1750777438000, "results": "233", "hashOfConfig": "216"}, {"size": 8538, "mtime": 1752551742429, "results": "234", "hashOfConfig": "216"}, {"size": 19148, "mtime": 1751447640000, "results": "235", "hashOfConfig": "216"}, {"size": 5949, "mtime": 1748749522000, "results": "236", "hashOfConfig": "216"}, {"size": 8266, "mtime": 1748588852000, "results": "237", "hashOfConfig": "216"}, {"size": 809, "mtime": 1751274436000, "results": "238", "hashOfConfig": "216"}, {"size": 1283, "mtime": 1751274444000, "results": "239", "hashOfConfig": "216"}, {"size": 1304, "mtime": 1751274452000, "results": "240", "hashOfConfig": "216"}, {"size": 4827, "mtime": 1751274510000, "results": "241", "hashOfConfig": "216"}, {"size": 11351, "mtime": 1751114552000, "results": "242", "hashOfConfig": "216"}, {"size": 7098, "mtime": 1751277960000, "results": "243", "hashOfConfig": "216"}, {"size": 2764, "mtime": 1752550266258, "results": "244", "hashOfConfig": "216"}, {"size": 5674, "mtime": 1748533678000, "results": "245", "hashOfConfig": "216"}, {"size": 9289, "mtime": 1751076836000, "results": "246", "hashOfConfig": "216"}, {"size": 5687, "mtime": 1752551704438, "results": "247", "hashOfConfig": "216"}, {"size": 2754, "mtime": 1751275216000, "results": "248", "hashOfConfig": "216"}, {"size": 13243, "mtime": 1751376032000, "results": "249", "hashOfConfig": "216"}, {"size": 10237, "mtime": 1751375976000, "results": "250", "hashOfConfig": "216"}, {"size": 13935, "mtime": 1751376296000, "results": "251", "hashOfConfig": "216"}, {"size": 3302, "mtime": 1751251038000, "results": "252", "hashOfConfig": "216"}, {"size": 20686, "mtime": 1751376374000, "results": "253", "hashOfConfig": "216"}, {"size": 3375, "mtime": 1751249990000, "results": "254", "hashOfConfig": "216"}, {"size": 19762, "mtime": 1751376110000, "results": "255", "hashOfConfig": "216"}, {"size": 53614, "mtime": 1751251970000, "results": "256", "hashOfConfig": "216"}, {"size": 5314, "mtime": 1751292028000, "results": "257", "hashOfConfig": "216"}, {"size": 23502, "mtime": 1751440302000, "results": "258", "hashOfConfig": "216"}, {"size": 10084, "mtime": 1751440302000, "results": "259", "hashOfConfig": "216"}, {"size": 8611, "mtime": 1751273494000, "results": "260", "hashOfConfig": "216"}, {"size": 3006, "mtime": 1751268708000, "results": "261", "hashOfConfig": "216"}, {"size": 2536, "mtime": 1751268692000, "results": "262", "hashOfConfig": "216"}, {"size": 10679, "mtime": 1751460404000, "results": "263", "hashOfConfig": "216"}, {"size": 8949, "mtime": 1751275712000, "results": "264", "hashOfConfig": "216"}, {"size": 11918, "mtime": 1751270770000, "results": "265", "hashOfConfig": "216"}, {"size": 2001, "mtime": 1751448750000, "results": "266", "hashOfConfig": "216"}, {"size": 5719, "mtime": 1751076762000, "results": "267", "hashOfConfig": "216"}, {"size": 2632, "mtime": 1749573724000, "results": "268", "hashOfConfig": "216"}, {"size": 3344, "mtime": 1751274470000, "results": "269", "hashOfConfig": "216"}, {"size": 12092, "mtime": 1752550729643, "results": "270", "hashOfConfig": "216"}, {"size": 9556, "mtime": 1751175720000, "results": "271", "hashOfConfig": "216"}, {"size": 7053, "mtime": 1752551721433, "results": "272", "hashOfConfig": "216"}, {"size": 6705, "mtime": 1751460330000, "results": "273", "hashOfConfig": "216"}, {"size": 6242, "mtime": 1751387088000, "results": "274", "hashOfConfig": "216"}, {"size": 3823, "mtime": 1751272136000, "results": "275", "hashOfConfig": "216"}, {"size": 2725, "mtime": 1751076778000, "results": "276", "hashOfConfig": "216"}, {"size": 5487, "mtime": 1748536714000, "results": "277", "hashOfConfig": "216"}, {"size": 4182, "mtime": 1751175562000, "results": "278", "hashOfConfig": "216"}, {"size": 5819, "mtime": 1752036805302, "results": "279", "hashOfConfig": "216"}, {"size": 6209, "mtime": 1751460516000, "results": "280", "hashOfConfig": "216"}, {"size": 17111, "mtime": 1752036839812, "results": "281", "hashOfConfig": "216"}, {"size": 6908, "mtime": 1751459962000, "results": "282", "hashOfConfig": "216"}, {"size": 6346, "mtime": 1751454744000, "results": "283", "hashOfConfig": "216"}, {"size": 4800, "mtime": 1752640282433, "results": "284", "hashOfConfig": "143"}, {"size": 7564, "mtime": 1752640320003, "results": "285", "hashOfConfig": "143"}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "l36c5a", {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1hbkk32", {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1craelc", {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\index.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\reportWebVitals.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\App.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CartContext.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CurrencyContext.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\LanguageContext.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Login.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Home.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useAuth.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductCatalog.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductDetail.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Gallery.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Payment.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\CheckoutPage.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Account.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\OrderSuccessPage.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Cart.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\About.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\admin\\AdminDashboard.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Footer.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\demo\\ProductCardDemo.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Header.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\AdminRoute.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\RoleBasedRoute.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\ProtectedRoute.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePermissions.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartItem.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\3d\\3DConfigurator.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\paymentService.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\products.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CheckoutModal.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\auth.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useWebSocket.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\AdminLogo.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductCard.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\payment\\PayMongoCheckout.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductFilter.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\ProfileManagement.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\OrderHistory.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\SecuritySettings.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AddressBook.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AccountPreferences.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\modals\\ConfirmationModal.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\OrderManagement.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ProductManagement.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\Analytics.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\SupplierManagement.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\DashboardOverview.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Logo.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\UnauthorizedAccess.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartIcon.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\CurrencyLanguageSelector.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\InventoryManagement.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\UserManagement.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\icons\\AdminIcons.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchInput.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiConfig.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiClient.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\api.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePrice.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\websocketService.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartSidebar.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchSuggestions.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\modals\\ProductDetailsModal.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\modals\\ProductFormModal.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\FileUploadZone.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ThreeJSPreview.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ProductManagementNew.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductCard.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductForm.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\SearchFilters.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\index.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\App.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\reportWebVitals.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CartContext.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CurrencyContext.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useAuth.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\LanguageContext.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Login.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\OrderSuccessPage.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Payment.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductCatalog.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\About.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Account.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductDetail.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Home.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Cart.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\CheckoutPage.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Gallery.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\admin\\AdminDashboard.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Header.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\demo\\ProductCardDemo.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Footer.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\ProtectedRoute.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\RoleBasedRoute.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\AdminRoute.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePermissions.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\products.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\paymentService.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\auth.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductFilter.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductCard.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useWebSocket.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\AdminLogo.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\OrderHistory.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\ProfileManagement.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AccountPreferences.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\modals\\ConfirmationModal.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\SecuritySettings.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CheckoutModal.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AddressBook.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\3d\\3DConfigurator.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartItem.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\OrderManagement.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\payment\\PayMongoCheckout.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\DashboardOverview.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\UserManagement.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\SupplierManagement.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ProductManagementNew.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\Analytics.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\InventoryManagement.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartIcon.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\CurrencyLanguageSelector.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Logo.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\UnauthorizedAccess.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\icons\\AdminIcons.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchInput.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\websocketService.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\api.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiClient.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiConfig.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePrice.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartSidebar.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchSuggestions.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\SearchFilters.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductCard.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductForm.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ThreeJSPreview.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\FileUploadZone.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\constants\\index.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\utils\\index.js", [], []]