{"ast": null, "code": "// Centralized API Client\n// This service provides a configured axios instance for all API calls\n\nimport axios from 'axios';\nimport apiConfig from './apiConfig';\nclass ApiClient {\n  constructor() {\n    // Create axios instance with base configuration\n    this.client = axios.create({\n      baseURL: apiConfig.baseURL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n      }\n    });\n\n    // Setup request interceptor for authentication\n    this.client.interceptors.request.use(config => {\n      // Add auth token if available\n      const token = this.getAuthToken();\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n\n      // Add request timestamp for debugging\n      if (apiConfig.debugMode) {\n        config.metadata = {\n          startTime: new Date()\n        };\n      }\n      return config;\n    }, error => {\n      return Promise.reject(error);\n    });\n\n    // Setup response interceptor for error handling\n    this.client.interceptors.response.use(response => {\n      // Log response time in debug mode\n      if (apiConfig.debugMode && response.config.metadata) {\n        var _response$config$meth;\n        const duration = new Date() - response.config.metadata.startTime;\n        console.log(`✅ API Response: ${(_response$config$meth = response.config.method) === null || _response$config$meth === void 0 ? void 0 : _response$config$meth.toUpperCase()} ${response.config.url} (${duration}ms)`);\n      }\n      return response;\n    }, error => {\n      // Handle different error types\n      if (error.response) {\n        // Server responded with error status\n        const {\n          status,\n          data\n        } = error.response;\n        if (status === 401) {\n          // Unauthorized - clear auth token and redirect to login\n          this.clearAuthToken();\n          if (window.location.pathname !== '/login') {\n            window.location.href = '/login';\n          }\n        } else if (status === 403) {\n          // Forbidden - check if it's an invalid token issue\n          if (data.message && (data.message.includes('token') || data.message.includes('expired') || data.message.includes('invalid'))) {\n            // Invalid/expired token - clear auth and redirect to login\n            this.clearAuthToken();\n            if (window.location.pathname !== '/login') {\n              window.location.href = '/login';\n            }\n          }\n        } else if (status >= 500 && apiConfig.debugMode) {\n          // Server error (debug mode only)\n          console.error('❌ Server Error:', data.message || 'Internal server error');\n        }\n\n        // Log error details in debug mode\n        if (apiConfig.debugMode) {\n          var _error$config, _error$config$method, _error$config2;\n          console.error(`❌ API Error: ${(_error$config = error.config) === null || _error$config === void 0 ? void 0 : (_error$config$method = _error$config.method) === null || _error$config$method === void 0 ? void 0 : _error$config$method.toUpperCase()} ${(_error$config2 = error.config) === null || _error$config2 === void 0 ? void 0 : _error$config2.url}`, {\n            status,\n            data,\n            headers: error.response.headers\n          });\n        }\n      } else if (error.request && apiConfig.debugMode) {\n        // Network error (debug mode only)\n        console.error('❌ Network Error:', error.message);\n      } else if (apiConfig.debugMode) {\n        // Other error (debug mode only)\n        console.error('❌ Request Setup Error:', error.message);\n      }\n      return Promise.reject(error);\n    });\n  }\n\n  // Get authentication token from localStorage\n  getAuthToken() {\n    return localStorage.getItem('token') || localStorage.getItem('authToken');\n  }\n\n  // Clear authentication token\n  clearAuthToken() {\n    localStorage.removeItem('token');\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('user');\n  }\n\n  // Generic GET request\n  async get(url, config = {}) {\n    try {\n      const response = await this.client.get(url, config);\n      return response.data;\n    } catch (error) {\n      throw this.handleError(error);\n    }\n  }\n\n  // Generic POST request\n  async post(url, data = {}, config = {}) {\n    try {\n      const response = await this.client.post(url, data, config);\n      return response.data;\n    } catch (error) {\n      throw this.handleError(error);\n    }\n  }\n\n  // Generic PUT request\n  async put(url, data = {}, config = {}) {\n    try {\n      const response = await this.client.put(url, data, config);\n      return response.data;\n    } catch (error) {\n      throw this.handleError(error);\n    }\n  }\n\n  // Generic PATCH request\n  async patch(url, data = {}, config = {}) {\n    try {\n      const response = await this.client.patch(url, data, config);\n      return response.data;\n    } catch (error) {\n      throw this.handleError(error);\n    }\n  }\n\n  // Generic DELETE request\n  async delete(url, config = {}) {\n    try {\n      const response = await this.client.delete(url, config);\n      return response.data;\n    } catch (error) {\n      throw this.handleError(error);\n    }\n  }\n\n  // Handle and format errors\n  handleError(error) {\n    if (error.response) {\n      var _error$response$data, _error$response$data2;\n      // Server responded with error\n      const message = ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || ((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : _error$response$data2.error) || 'Server error occurred';\n      return new Error(message);\n    } else if (error.request) {\n      // Network error\n      return new Error('Network error - please check your connection');\n    } else {\n      // Other error\n      return new Error(error.message || 'An unexpected error occurred');\n    }\n  }\n\n  // Health check\n  async healthCheck() {\n    try {\n      const response = await this.get('/health');\n      return response;\n    } catch (error) {\n      console.error('❌ Backend health check failed:', error.message);\n      return null;\n    }\n  }\n\n  // Test connection to backend\n  async testConnection() {\n    try {\n      const health = await this.healthCheck();\n      if (health) {\n        console.log('✅ Backend connection successful');\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error('❌ Backend connection failed:', error.message);\n      return false;\n    }\n  }\n}\n\n// Create singleton instance\nconst apiClient = new ApiClient();\nexport default apiClient;", "map": {"version": 3, "names": ["axios", "apiConfig", "ApiClient", "constructor", "client", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "getAuthToken", "Authorization", "debugMode", "metadata", "startTime", "Date", "error", "Promise", "reject", "response", "_response$config$meth", "duration", "console", "log", "method", "toUpperCase", "url", "status", "data", "clearAuthToken", "window", "location", "pathname", "href", "message", "includes", "_error$config", "_error$config$method", "_error$config2", "localStorage", "getItem", "removeItem", "get", "handleError", "post", "put", "patch", "delete", "_error$response$data", "_error$response$data2", "Error", "healthCheck", "testConnection", "health", "apiClient"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/services/apiClient.js"], "sourcesContent": ["// Centralized API Client\n// This service provides a configured axios instance for all API calls\n\nimport axios from 'axios';\nimport apiConfig from './apiConfig';\n\nclass ApiClient {\n  constructor() {\n    // Create axios instance with base configuration\n    this.client = axios.create({\n      baseURL: apiConfig.baseURL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n      }\n    });\n\n    // Setup request interceptor for authentication\n    this.client.interceptors.request.use(\n      (config) => {\n        // Add auth token if available\n        const token = this.getAuthToken();\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n\n        // Add request timestamp for debugging\n        if (apiConfig.debugMode) {\n          config.metadata = { startTime: new Date() };\n        }\n\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // Setup response interceptor for error handling\n    this.client.interceptors.response.use(\n      (response) => {\n        // Log response time in debug mode\n        if (apiConfig.debugMode && response.config.metadata) {\n          const duration = new Date() - response.config.metadata.startTime;\n          console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`);\n        }\n\n        return response;\n      },\n      (error) => {\n        // Handle different error types\n        if (error.response) {\n          // Server responded with error status\n          const { status, data } = error.response;\n          \n          if (status === 401) {\n            // Unauthorized - clear auth token and redirect to login\n            this.clearAuthToken();\n            if (window.location.pathname !== '/login') {\n              window.location.href = '/login';\n            }\n          } else if (status === 403) {\n            // Forbidden - check if it's an invalid token issue\n            if (data.message && (data.message.includes('token') || data.message.includes('expired') || data.message.includes('invalid'))) {\n              // Invalid/expired token - clear auth and redirect to login\n              this.clearAuthToken();\n              if (window.location.pathname !== '/login') {\n                window.location.href = '/login';\n              }\n            }\n          } else if (status >= 500 && apiConfig.debugMode) {\n            // Server error (debug mode only)\n            console.error('❌ Server Error:', data.message || 'Internal server error');\n          }\n\n          // Log error details in debug mode\n          if (apiConfig.debugMode) {\n            console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {\n              status,\n              data,\n              headers: error.response.headers\n            });\n          }\n        } else if (error.request && apiConfig.debugMode) {\n          // Network error (debug mode only)\n          console.error('❌ Network Error:', error.message);\n        } else if (apiConfig.debugMode) {\n          // Other error (debug mode only)\n          console.error('❌ Request Setup Error:', error.message);\n        }\n\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  // Get authentication token from localStorage\n  getAuthToken() {\n    return localStorage.getItem('token') || localStorage.getItem('authToken');\n  }\n\n  // Clear authentication token\n  clearAuthToken() {\n    localStorage.removeItem('token');\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('user');\n  }\n\n  // Generic GET request\n  async get(url, config = {}) {\n    try {\n      const response = await this.client.get(url, config);\n      return response.data;\n    } catch (error) {\n      throw this.handleError(error);\n    }\n  }\n\n  // Generic POST request\n  async post(url, data = {}, config = {}) {\n    try {\n      const response = await this.client.post(url, data, config);\n      return response.data;\n    } catch (error) {\n      throw this.handleError(error);\n    }\n  }\n\n  // Generic PUT request\n  async put(url, data = {}, config = {}) {\n    try {\n      const response = await this.client.put(url, data, config);\n      return response.data;\n    } catch (error) {\n      throw this.handleError(error);\n    }\n  }\n\n  // Generic PATCH request\n  async patch(url, data = {}, config = {}) {\n    try {\n      const response = await this.client.patch(url, data, config);\n      return response.data;\n    } catch (error) {\n      throw this.handleError(error);\n    }\n  }\n\n  // Generic DELETE request\n  async delete(url, config = {}) {\n    try {\n      const response = await this.client.delete(url, config);\n      return response.data;\n    } catch (error) {\n      throw this.handleError(error);\n    }\n  }\n\n  // Handle and format errors\n  handleError(error) {\n    if (error.response) {\n      // Server responded with error\n      const message = error.response.data?.message || error.response.data?.error || 'Server error occurred';\n      return new Error(message);\n    } else if (error.request) {\n      // Network error\n      return new Error('Network error - please check your connection');\n    } else {\n      // Other error\n      return new Error(error.message || 'An unexpected error occurred');\n    }\n  }\n\n  // Health check\n  async healthCheck() {\n    try {\n      const response = await this.get('/health');\n      return response;\n    } catch (error) {\n      console.error('❌ Backend health check failed:', error.message);\n      return null;\n    }\n  }\n\n  // Test connection to backend\n  async testConnection() {\n    try {\n      const health = await this.healthCheck();\n      if (health) {\n        console.log('✅ Backend connection successful');\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error('❌ Backend connection failed:', error.message);\n      return false;\n    }\n  }\n}\n\n// Create singleton instance\nconst apiClient = new ApiClient();\n\nexport default apiClient;\n"], "mappings": "AAAA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,aAAa;AAEnC,MAAMC,SAAS,CAAC;EACdC,WAAWA,CAAA,EAAG;IACZ;IACA,IAAI,CAACC,MAAM,GAAGJ,KAAK,CAACK,MAAM,CAAC;MACzBC,OAAO,EAAEL,SAAS,CAACK,OAAO;MAC1BC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClC,QAAQ,EAAE;MACZ;IACF,CAAC,CAAC;;IAEF;IACA,IAAI,CAACJ,MAAM,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACjCC,MAAM,IAAK;MACV;MACA,MAAMC,KAAK,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;MACjC,IAAID,KAAK,EAAE;QACTD,MAAM,CAACJ,OAAO,CAACO,aAAa,GAAG,UAAUF,KAAK,EAAE;MAClD;;MAEA;MACA,IAAIZ,SAAS,CAACe,SAAS,EAAE;QACvBJ,MAAM,CAACK,QAAQ,GAAG;UAAEC,SAAS,EAAE,IAAIC,IAAI,CAAC;QAAE,CAAC;MAC7C;MAEA,OAAOP,MAAM;IACf,CAAC,EACAQ,KAAK,IAAK;MACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;;IAED;IACA,IAAI,CAAChB,MAAM,CAACK,YAAY,CAACc,QAAQ,CAACZ,GAAG,CAClCY,QAAQ,IAAK;MACZ;MACA,IAAItB,SAAS,CAACe,SAAS,IAAIO,QAAQ,CAACX,MAAM,CAACK,QAAQ,EAAE;QAAA,IAAAO,qBAAA;QACnD,MAAMC,QAAQ,GAAG,IAAIN,IAAI,CAAC,CAAC,GAAGI,QAAQ,CAACX,MAAM,CAACK,QAAQ,CAACC,SAAS;QAChEQ,OAAO,CAACC,GAAG,CAAC,oBAAAH,qBAAA,GAAmBD,QAAQ,CAACX,MAAM,CAACgB,MAAM,cAAAJ,qBAAA,uBAAtBA,qBAAA,CAAwBK,WAAW,CAAC,CAAC,IAAIN,QAAQ,CAACX,MAAM,CAACkB,GAAG,KAAKL,QAAQ,KAAK,CAAC;MAChH;MAEA,OAAOF,QAAQ;IACjB,CAAC,EACAH,KAAK,IAAK;MACT;MACA,IAAIA,KAAK,CAACG,QAAQ,EAAE;QAClB;QACA,MAAM;UAAEQ,MAAM;UAAEC;QAAK,CAAC,GAAGZ,KAAK,CAACG,QAAQ;QAEvC,IAAIQ,MAAM,KAAK,GAAG,EAAE;UAClB;UACA,IAAI,CAACE,cAAc,CAAC,CAAC;UACrB,IAAIC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,QAAQ,EAAE;YACzCF,MAAM,CAACC,QAAQ,CAACE,IAAI,GAAG,QAAQ;UACjC;QACF,CAAC,MAAM,IAAIN,MAAM,KAAK,GAAG,EAAE;UACzB;UACA,IAAIC,IAAI,CAACM,OAAO,KAAKN,IAAI,CAACM,OAAO,CAACC,QAAQ,CAAC,OAAO,CAAC,IAAIP,IAAI,CAACM,OAAO,CAACC,QAAQ,CAAC,SAAS,CAAC,IAAIP,IAAI,CAACM,OAAO,CAACC,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE;YAC5H;YACA,IAAI,CAACN,cAAc,CAAC,CAAC;YACrB,IAAIC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,QAAQ,EAAE;cACzCF,MAAM,CAACC,QAAQ,CAACE,IAAI,GAAG,QAAQ;YACjC;UACF;QACF,CAAC,MAAM,IAAIN,MAAM,IAAI,GAAG,IAAI9B,SAAS,CAACe,SAAS,EAAE;UAC/C;UACAU,OAAO,CAACN,KAAK,CAAC,iBAAiB,EAAEY,IAAI,CAACM,OAAO,IAAI,uBAAuB,CAAC;QAC3E;;QAEA;QACA,IAAIrC,SAAS,CAACe,SAAS,EAAE;UAAA,IAAAwB,aAAA,EAAAC,oBAAA,EAAAC,cAAA;UACvBhB,OAAO,CAACN,KAAK,CAAC,iBAAAoB,aAAA,GAAgBpB,KAAK,CAACR,MAAM,cAAA4B,aAAA,wBAAAC,oBAAA,GAAZD,aAAA,CAAcZ,MAAM,cAAAa,oBAAA,uBAApBA,oBAAA,CAAsBZ,WAAW,CAAC,CAAC,KAAAa,cAAA,GAAItB,KAAK,CAACR,MAAM,cAAA8B,cAAA,uBAAZA,cAAA,CAAcZ,GAAG,EAAE,EAAE;YACxFC,MAAM;YACNC,IAAI;YACJxB,OAAO,EAAEY,KAAK,CAACG,QAAQ,CAACf;UAC1B,CAAC,CAAC;QACJ;MACF,CAAC,MAAM,IAAIY,KAAK,CAACV,OAAO,IAAIT,SAAS,CAACe,SAAS,EAAE;QAC/C;QACAU,OAAO,CAACN,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAACkB,OAAO,CAAC;MAClD,CAAC,MAAM,IAAIrC,SAAS,CAACe,SAAS,EAAE;QAC9B;QACAU,OAAO,CAACN,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAACkB,OAAO,CAAC;MACxD;MAEA,OAAOjB,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;EACH;;EAEA;EACAN,YAAYA,CAAA,EAAG;IACb,OAAO6B,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAID,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAC3E;;EAEA;EACAX,cAAcA,CAAA,EAAG;IACfU,YAAY,CAACE,UAAU,CAAC,OAAO,CAAC;IAChCF,YAAY,CAACE,UAAU,CAAC,WAAW,CAAC;IACpCF,YAAY,CAACE,UAAU,CAAC,MAAM,CAAC;EACjC;;EAEA;EACA,MAAMC,GAAGA,CAAChB,GAAG,EAAElB,MAAM,GAAG,CAAC,CAAC,EAAE;IAC1B,IAAI;MACF,MAAMW,QAAQ,GAAG,MAAM,IAAI,CAACnB,MAAM,CAAC0C,GAAG,CAAChB,GAAG,EAAElB,MAAM,CAAC;MACnD,OAAOW,QAAQ,CAACS,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,MAAM,IAAI,CAAC2B,WAAW,CAAC3B,KAAK,CAAC;IAC/B;EACF;;EAEA;EACA,MAAM4B,IAAIA,CAAClB,GAAG,EAAEE,IAAI,GAAG,CAAC,CAAC,EAAEpB,MAAM,GAAG,CAAC,CAAC,EAAE;IACtC,IAAI;MACF,MAAMW,QAAQ,GAAG,MAAM,IAAI,CAACnB,MAAM,CAAC4C,IAAI,CAAClB,GAAG,EAAEE,IAAI,EAAEpB,MAAM,CAAC;MAC1D,OAAOW,QAAQ,CAACS,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,MAAM,IAAI,CAAC2B,WAAW,CAAC3B,KAAK,CAAC;IAC/B;EACF;;EAEA;EACA,MAAM6B,GAAGA,CAACnB,GAAG,EAAEE,IAAI,GAAG,CAAC,CAAC,EAAEpB,MAAM,GAAG,CAAC,CAAC,EAAE;IACrC,IAAI;MACF,MAAMW,QAAQ,GAAG,MAAM,IAAI,CAACnB,MAAM,CAAC6C,GAAG,CAACnB,GAAG,EAAEE,IAAI,EAAEpB,MAAM,CAAC;MACzD,OAAOW,QAAQ,CAACS,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,MAAM,IAAI,CAAC2B,WAAW,CAAC3B,KAAK,CAAC;IAC/B;EACF;;EAEA;EACA,MAAM8B,KAAKA,CAACpB,GAAG,EAAEE,IAAI,GAAG,CAAC,CAAC,EAAEpB,MAAM,GAAG,CAAC,CAAC,EAAE;IACvC,IAAI;MACF,MAAMW,QAAQ,GAAG,MAAM,IAAI,CAACnB,MAAM,CAAC8C,KAAK,CAACpB,GAAG,EAAEE,IAAI,EAAEpB,MAAM,CAAC;MAC3D,OAAOW,QAAQ,CAACS,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,MAAM,IAAI,CAAC2B,WAAW,CAAC3B,KAAK,CAAC;IAC/B;EACF;;EAEA;EACA,MAAM+B,MAAMA,CAACrB,GAAG,EAAElB,MAAM,GAAG,CAAC,CAAC,EAAE;IAC7B,IAAI;MACF,MAAMW,QAAQ,GAAG,MAAM,IAAI,CAACnB,MAAM,CAAC+C,MAAM,CAACrB,GAAG,EAAElB,MAAM,CAAC;MACtD,OAAOW,QAAQ,CAACS,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,MAAM,IAAI,CAAC2B,WAAW,CAAC3B,KAAK,CAAC;IAC/B;EACF;;EAEA;EACA2B,WAAWA,CAAC3B,KAAK,EAAE;IACjB,IAAIA,KAAK,CAACG,QAAQ,EAAE;MAAA,IAAA6B,oBAAA,EAAAC,qBAAA;MAClB;MACA,MAAMf,OAAO,GAAG,EAAAc,oBAAA,GAAAhC,KAAK,CAACG,QAAQ,CAACS,IAAI,cAAAoB,oBAAA,uBAAnBA,oBAAA,CAAqBd,OAAO,OAAAe,qBAAA,GAAIjC,KAAK,CAACG,QAAQ,CAACS,IAAI,cAAAqB,qBAAA,uBAAnBA,qBAAA,CAAqBjC,KAAK,KAAI,uBAAuB;MACrG,OAAO,IAAIkC,KAAK,CAAChB,OAAO,CAAC;IAC3B,CAAC,MAAM,IAAIlB,KAAK,CAACV,OAAO,EAAE;MACxB;MACA,OAAO,IAAI4C,KAAK,CAAC,8CAA8C,CAAC;IAClE,CAAC,MAAM;MACL;MACA,OAAO,IAAIA,KAAK,CAAClC,KAAK,CAACkB,OAAO,IAAI,8BAA8B,CAAC;IACnE;EACF;;EAEA;EACA,MAAMiB,WAAWA,CAAA,EAAG;IAClB,IAAI;MACF,MAAMhC,QAAQ,GAAG,MAAM,IAAI,CAACuB,GAAG,CAAC,SAAS,CAAC;MAC1C,OAAOvB,QAAQ;IACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAACkB,OAAO,CAAC;MAC9D,OAAO,IAAI;IACb;EACF;;EAEA;EACA,MAAMkB,cAAcA,CAAA,EAAG;IACrB,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM,IAAI,CAACF,WAAW,CAAC,CAAC;MACvC,IAAIE,MAAM,EAAE;QACV/B,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAACkB,OAAO,CAAC;MAC5D,OAAO,KAAK;IACd;EACF;AACF;;AAEA;AACA,MAAMoB,SAAS,GAAG,IAAIxD,SAAS,CAAC,CAAC;AAEjC,eAAewD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}