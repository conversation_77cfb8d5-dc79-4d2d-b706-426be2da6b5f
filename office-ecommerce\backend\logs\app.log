[2025-06-30T06:56:53.608Z] INFO: Connecting to MSSQL database...
[2025-06-30T06:56:53.627Z] ERROR: Database connection failed: {"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}
[2025-06-30T06:56:53.628Z] ERROR: Database connection failed: {"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}
[2025-06-30T06:57:20.300Z] INFO: Connecting to MSSQL database...
[2025-06-30T06:57:20.317Z] ERROR: Database connection failed: {"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}
[2025-06-30T06:57:20.318Z] ERROR: Database connection failed: {"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}
[2025-06-30T06:57:58.063Z] INFO: Connecting to MSSQL database...
[2025-06-30T06:57:58.081Z] ERROR: Database connection failed: {"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}
[2025-06-30T06:57:58.082Z] ERROR: Database connection failed: {"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}
[2025-06-30T06:58:17.636Z] INFO: Connecting to MSSQL database...
[2025-06-30T06:58:17.653Z] ERROR: Database connection failed: {"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}
[2025-06-30T06:58:17.654Z] ERROR: Database connection failed: {"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}
[2025-06-30T06:58:31.596Z] INFO: Connecting to MSSQL database...
[2025-06-30T06:58:31.612Z] ERROR: Database connection failed: {"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}
[2025-06-30T06:58:31.614Z] ERROR: Database connection failed: {"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}
[2025-06-30T07:01:08.489Z] INFO: Checking database connection...
[2025-06-30T07:01:08.498Z] INFO: Connecting to SQLite database...
[2025-06-30T07:01:08.503Z] INFO: SQLite database connected successfully
[2025-06-30T07:01:08.504Z] INFO: Database connection test successful
[2025-06-30T07:01:08.506Z] INFO: Database connection successful
[2025-06-30T07:01:08.507Z] INFO: Test query result: {"recordset":[{"test":1}]}
[2025-06-30T07:01:08.509Z] INFO: Existing tables:
[2025-06-30T07:01:08.510Z] INFO: Database connection closed
[2025-06-30T07:01:13.615Z] INFO: Starting SQLite database migration...
[2025-06-30T07:01:13.625Z] INFO: Connecting to SQLite database...
[2025-06-30T07:01:13.629Z] INFO: SQLite database connected successfully
[2025-06-30T07:01:13.630Z] INFO: Database connection test successful
[2025-06-30T07:01:13.630Z] INFO: Connected to SQLite database
[2025-06-30T07:01:13.631Z] INFO: Executing database schema...
[2025-06-30T07:01:13.633Z] INFO: Executed statement 1/37
[2025-06-30T07:01:13.643Z] INFO: Executed statement 2/37
[2025-06-30T07:01:13.650Z] INFO: Executed statement 3/37
[2025-06-30T07:01:13.657Z] INFO: Executed statement 4/37
[2025-06-30T07:01:13.664Z] INFO: Executed statement 5/37
[2025-06-30T07:01:13.671Z] INFO: Executed statement 6/37
[2025-06-30T07:01:13.677Z] INFO: Executed statement 7/37
[2025-06-30T07:01:13.683Z] INFO: Executed statement 8/37
[2025-06-30T07:01:13.689Z] INFO: Executed statement 9/37
[2025-06-30T07:01:13.696Z] INFO: Executed statement 10/37
[2025-06-30T07:01:13.703Z] INFO: Executed statement 11/37
[2025-06-30T07:01:13.709Z] INFO: Executed statement 12/37
[2025-06-30T07:01:13.715Z] INFO: Executed statement 13/37
[2025-06-30T07:01:13.721Z] INFO: Executed statement 14/37
[2025-06-30T07:01:13.729Z] INFO: Executed statement 15/37
[2025-06-30T07:01:13.734Z] INFO: Executed statement 16/37
[2025-06-30T07:01:13.740Z] INFO: Executed statement 17/37
[2025-06-30T07:01:13.745Z] INFO: Executed statement 18/37
[2025-06-30T07:01:13.753Z] INFO: Executed statement 19/37
[2025-06-30T07:01:13.758Z] INFO: Executed statement 20/37
[2025-06-30T07:01:13.764Z] INFO: Executed statement 21/37
[2025-06-30T07:01:13.772Z] INFO: Executed statement 22/37
[2025-06-30T07:01:13.777Z] INFO: Executed statement 23/37
[2025-06-30T07:01:13.785Z] INFO: Executed statement 24/37
[2025-06-30T07:01:13.791Z] INFO: Executed statement 25/37
[2025-06-30T07:01:13.800Z] INFO: Executed statement 26/37
[2025-06-30T07:01:13.807Z] INFO: Executed statement 27/37
[2025-06-30T07:01:13.813Z] INFO: Executed statement 28/37
[2025-06-30T07:01:13.822Z] INFO: Executed statement 29/37
[2025-06-30T07:01:13.828Z] INFO: Executed statement 30/37
[2025-06-30T07:01:13.833Z] INFO: Executed statement 31/37
[2025-06-30T07:01:13.840Z] INFO: Executed statement 32/37
[2025-06-30T07:01:13.847Z] INFO: Executed statement 33/37
[2025-06-30T07:01:13.853Z] INFO: Executed statement 34/37
[2025-06-30T07:01:13.858Z] INFO: Executed statement 35/37
[2025-06-30T07:01:13.864Z] INFO: Executed statement 36/37
[2025-06-30T07:01:13.871Z] INFO: Executed statement 37/37
[2025-06-30T07:01:13.871Z] INFO: Database schema created successfully
[2025-06-30T07:01:13.872Z] INFO: No sample data file found, skipping...
[2025-06-30T07:01:13.872Z] INFO: Database migration completed successfully
[2025-06-30T07:01:13.873Z] INFO: Database connection closed
[2025-06-30T07:01:13.874Z] INFO: Migration completed successfully
[2025-06-30T07:12:50.480Z] INFO: Connecting to MSSQL database...
[2025-06-30T07:12:50.519Z] ERROR: Database connection failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-06-30T07:12:50.519Z] ERROR: Database connection failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-06-30T07:13:08.516Z] INFO: Connecting to MSSQL database...
[2025-06-30T07:13:08.547Z] ERROR: Database connection failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-06-30T07:13:08.548Z] ERROR: Database connection failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-06-30T07:13:28.497Z] INFO: Connecting to MSSQL database...
[2025-06-30T07:13:28.530Z] ERROR: Database connection failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-06-30T07:13:28.531Z] ERROR: Database connection failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-06-30T07:14:10.956Z] INFO: Connecting to MSSQL database...
[2025-06-30T07:14:10.989Z] ERROR: Database connection failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-06-30T07:14:10.990Z] ERROR: Database connection failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-06-30T07:17:22.650Z] INFO: Connecting to MSSQL database...
[2025-06-30T07:17:23.933Z] ERROR: Database connection failed: {"code":"EINSTLOOKUP","originalError":{"code":"EINSTLOOKUP"},"name":"ConnectionError"}
[2025-06-30T07:17:23.934Z] ERROR: Database connection failed: {"code":"EINSTLOOKUP","originalError":{"code":"EINSTLOOKUP"},"name":"ConnectionError"}
[2025-06-30T07:18:02.517Z] INFO: Connecting to MSSQL database...
[2025-06-30T07:18:02.550Z] ERROR: Database connection failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-06-30T07:18:02.550Z] ERROR: Database connection failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-06-30T07:26:23.158Z] INFO: Connecting to MSSQL database...
[2025-06-30T07:26:23.186Z] ERROR: Database connection failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-06-30T07:26:23.186Z] ERROR: Failed to start server: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-06-30T07:26:44.927Z] INFO: Connecting to MSSQL database...
[2025-06-30T07:26:44.955Z] ERROR: Database connection failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-06-30T07:26:44.955Z] WARN: Database connection failed, starting server without database: "Login failed for user ''."
[2025-06-30T07:26:44.955Z] INFO: Server will use mock data until database is available
[2025-06-30T07:26:44.958Z] INFO: Server running on port 5000 in development mode
[2025-06-30T07:37:37.719Z] INFO: Connecting to MSSQL database...
[2025-06-30T07:37:37.751Z] ERROR: Database connection failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-06-30T07:37:37.751Z] WARN: Database connection failed, starting server without database: "Login failed for user ''."
[2025-06-30T07:37:37.752Z] INFO: Server will use mock data until database is available
[2025-06-30T07:37:37.755Z] INFO: Server running on port 5000 in development mode
[2025-06-30T07:43:54.623Z] INFO: Connecting to MSSQL database...
[2025-06-30T07:43:54.697Z] ERROR: Database connection failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-06-30T07:43:54.698Z] WARN: Database connection failed, starting server without database: "Login failed for user ''."
[2025-06-30T07:43:54.698Z] INFO: Server will use mock data until database is available
[2025-06-30T07:43:54.701Z] INFO: WebSocket service initialized
[2025-06-30T07:43:54.703Z] INFO: Server running on port 5000 in development mode
[2025-06-30T07:43:54.704Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-06-30T08:06:34.214Z] INFO: Connecting to MSSQL database...
[2025-06-30T08:06:34.251Z] ERROR: Database connection failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-06-30T08:06:34.252Z] WARN: Database connection failed, starting server without database: "Login failed for user ''."
[2025-06-30T08:06:34.252Z] INFO: Server will use mock data until database is available
[2025-06-30T08:06:34.255Z] INFO: WebSocket service initialized
[2025-06-30T08:12:15.281Z] INFO: Connecting to MSSQL database...
[2025-06-30T08:12:15.320Z] INFO: Database connected successfully
[2025-06-30T08:12:15.336Z] INFO: Database connection test successful
[2025-06-30T08:12:15.339Z] INFO: Database connection test successful
[2025-06-30T08:12:15.340Z] INFO: Database connection closed
[2025-06-30T08:12:25.308Z] INFO: Starting database migration...
[2025-06-30T08:12:25.309Z] INFO: Connecting to MSSQL database...
[2025-06-30T08:12:25.349Z] INFO: Database connected successfully
[2025-06-30T08:12:25.366Z] INFO: Database connection test successful
[2025-06-30T08:12:25.366Z] INFO: Connected to database
[2025-06-30T08:12:25.373Z] INFO: Executing database schema...
[2025-06-30T08:12:25.422Z] INFO: Executed batch 1/9
[2025-06-30T08:12:25.618Z] INFO: Executed batch 2/9
[2025-06-30T08:12:25.621Z] INFO: Executed batch 3/9
[2025-06-30T08:12:25.694Z] INFO: Executed batch 4/9
[2025-06-30T08:12:25.702Z] ERROR: Query execution failed: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":111,"state":1,"class":15,"message":"'CREATE/ALTER PROCEDURE' must be the first statement in a query batch.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"sp_UpdateInventoryLevels","lineNumber":416}},"name":"RequestError","number":111,"lineNumber":416,"state":1,"class":15,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"sp_UpdateInventoryLevels","precedingErrors":[]}
[2025-06-30T08:12:25.703Z] ERROR: Error in batch 5: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":111,"state":1,"class":15,"message":"'CREATE/ALTER PROCEDURE' must be the first statement in a query batch.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"sp_UpdateInventoryLevels","lineNumber":416}},"name":"RequestError","number":111,"lineNumber":416,"state":1,"class":15,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"sp_UpdateInventoryLevels","precedingErrors":[]}
[2025-06-30T08:12:25.703Z] ERROR: Migration failed: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":111,"state":1,"class":15,"message":"'CREATE/ALTER PROCEDURE' must be the first statement in a query batch.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"sp_UpdateInventoryLevels","lineNumber":416}},"name":"RequestError","number":111,"lineNumber":416,"state":1,"class":15,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"sp_UpdateInventoryLevels","precedingErrors":[]}
[2025-06-30T08:13:20.008Z] INFO: Starting database migration...
[2025-06-30T08:13:20.010Z] INFO: Connecting to MSSQL database...
[2025-06-30T08:13:20.046Z] INFO: Database connected successfully
[2025-06-30T08:13:20.064Z] INFO: Database connection test successful
[2025-06-30T08:13:20.065Z] INFO: Connected to database
[2025-06-30T08:13:20.066Z] INFO: Executing database schema...
[2025-06-30T08:13:20.069Z] INFO: Executed batch 1/10
[2025-06-30T08:13:20.071Z] INFO: Executed batch 2/10
[2025-06-30T08:13:20.107Z] INFO: Executed batch 3/10
[2025-06-30T08:13:20.111Z] INFO: Executed batch 4/10
[2025-06-30T08:13:20.172Z] INFO: Executed batch 5/10
[2025-06-30T08:13:20.182Z] INFO: Executed batch 6/10
[2025-06-30T08:13:20.186Z] INFO: Executed batch 7/10
[2025-06-30T08:13:20.189Z] ERROR: Query execution failed: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":156,"state":1,"class":15,"message":"Incorrect syntax near the keyword 'VIEW'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"sp_ReleaseReservedInventory","lineNumber":29}},"name":"RequestError","number":156,"lineNumber":29,"state":1,"class":15,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"sp_ReleaseReservedInventory","precedingErrors":[]}
[2025-06-30T08:13:20.190Z] ERROR: Error in batch 8: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":156,"state":1,"class":15,"message":"Incorrect syntax near the keyword 'VIEW'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"sp_ReleaseReservedInventory","lineNumber":29}},"name":"RequestError","number":156,"lineNumber":29,"state":1,"class":15,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"sp_ReleaseReservedInventory","precedingErrors":[]}
[2025-06-30T08:13:20.190Z] ERROR: Migration failed: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":156,"state":1,"class":15,"message":"Incorrect syntax near the keyword 'VIEW'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"sp_ReleaseReservedInventory","lineNumber":29}},"name":"RequestError","number":156,"lineNumber":29,"state":1,"class":15,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"sp_ReleaseReservedInventory","precedingErrors":[]}
[2025-06-30T08:13:48.014Z] INFO: Starting database migration...
[2025-06-30T08:13:48.016Z] INFO: Connecting to MSSQL database...
[2025-06-30T08:13:48.049Z] INFO: Database connected successfully
[2025-06-30T08:13:48.065Z] INFO: Database connection test successful
[2025-06-30T08:13:48.066Z] INFO: Connected to database
[2025-06-30T08:13:48.066Z] INFO: Executing database schema...
[2025-06-30T08:13:48.069Z] INFO: Executed batch 1/11
[2025-06-30T08:13:48.097Z] INFO: Executed batch 2/11
[2025-06-30T08:13:48.130Z] INFO: Executed batch 3/11
[2025-06-30T08:13:48.133Z] INFO: Executed batch 4/11
[2025-06-30T08:13:48.150Z] ERROR: Query execution failed: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":2714,"state":6,"class":16,"message":"There is already an object named 'Users' in the database.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":6}},"name":"RequestError","number":2714,"lineNumber":6,"state":6,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-06-30T08:13:48.151Z] ERROR: Error in batch 5: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":2714,"state":6,"class":16,"message":"There is already an object named 'Users' in the database.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":6}},"name":"RequestError","number":2714,"lineNumber":6,"state":6,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-06-30T08:13:48.151Z] ERROR: Migration failed: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":2714,"state":6,"class":16,"message":"There is already an object named 'Users' in the database.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":6}},"name":"RequestError","number":2714,"lineNumber":6,"state":6,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-06-30T08:13:53.940Z] WARN: WARNING: This will drop all tables and data!
[2025-06-30T08:13:53.942Z] INFO: Connecting to MSSQL database...
[2025-06-30T08:13:53.978Z] INFO: Database connected successfully
[2025-06-30T08:13:53.995Z] INFO: Database connection test successful
[2025-06-30T08:13:54.015Z] ERROR: Query execution failed: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":209,"state":1,"class":16,"message":"Ambiguous column name 'name'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":4}},"name":"RequestError","number":209,"lineNumber":4,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":209,"state":1,"class":16,"message":"Ambiguous column name 'schema_id'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":4}},"name":"RequestError","number":209,"lineNumber":4,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":""}]}
[2025-06-30T08:13:54.015Z] ERROR: Error dropping tables: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":209,"state":1,"class":16,"message":"Ambiguous column name 'name'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":4}},"name":"RequestError","number":209,"lineNumber":4,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":209,"state":1,"class":16,"message":"Ambiguous column name 'schema_id'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":4}},"name":"RequestError","number":209,"lineNumber":4,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":""}]}
[2025-06-30T08:13:54.017Z] INFO: Database connection closed
[2025-06-30T08:13:54.017Z] ERROR: Unhandled Promise Rejection: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":209,"state":1,"class":16,"message":"Ambiguous column name 'name'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":4}},"name":"RequestError","number":209,"lineNumber":4,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":209,"state":1,"class":16,"message":"Ambiguous column name 'schema_id'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":4}},"name":"RequestError","number":209,"lineNumber":4,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":""}]}
[2025-06-30T08:14:11.476Z] INFO: Connecting to MSSQL database...
[2025-06-30T08:14:11.513Z] INFO: Database connected successfully
[2025-06-30T08:14:11.531Z] INFO: Database connection test successful
[2025-06-30T08:14:11.595Z] INFO: Database connection closed
[2025-06-30T08:14:44.180Z] INFO: Connecting to master database...
[2025-06-30T08:14:44.218Z] INFO: Connected to master database
[2025-06-30T08:14:44.257Z] INFO: OfficeEcommerce database already exists
[2025-06-30T08:14:44.259Z] INFO: Testing connection to OfficeEcommerce database...
[2025-06-30T08:14:44.305Z] INFO: Connection to OfficeEcommerce database successful
[2025-06-30T08:14:44.306Z] INFO: Database creation completed successfully
[2025-06-30T08:14:49.899Z] INFO: Starting database migration...
[2025-06-30T08:14:49.901Z] INFO: Connecting to MSSQL database...
[2025-06-30T08:14:49.975Z] INFO: Database connected successfully
[2025-06-30T08:14:49.994Z] INFO: Database connection test successful
[2025-06-30T08:14:49.995Z] INFO: Connected to database
[2025-06-30T08:14:49.995Z] INFO: Executing database schema...
[2025-06-30T08:14:49.998Z] INFO: Executed batch 1/11
[2025-06-30T08:14:50.010Z] INFO: Executed batch 2/11
[2025-06-30T08:14:50.013Z] INFO: Executed batch 3/11
[2025-06-30T08:14:50.017Z] INFO: Executed batch 4/11
[2025-06-30T08:14:50.030Z] ERROR: Query execution failed: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":2714,"state":6,"class":16,"message":"There is already an object named 'Users' in the database.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":6}},"name":"RequestError","number":2714,"lineNumber":6,"state":6,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-06-30T08:14:50.030Z] ERROR: Error in batch 5: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":2714,"state":6,"class":16,"message":"There is already an object named 'Users' in the database.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":6}},"name":"RequestError","number":2714,"lineNumber":6,"state":6,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-06-30T08:14:50.031Z] ERROR: Migration failed: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":2714,"state":6,"class":16,"message":"There is already an object named 'Users' in the database.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":6}},"name":"RequestError","number":2714,"lineNumber":6,"state":6,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-06-30T08:15:27.611Z] INFO: Connecting to MSSQL database...
[2025-06-30T08:15:27.680Z] INFO: Database connected successfully
[2025-06-30T08:15:27.697Z] INFO: Database connection test successful
[2025-06-30T08:15:27.762Z] INFO: Database connection closed
[2025-06-30T08:15:45.832Z] INFO: Connecting to MSSQL database...
[2025-06-30T08:15:45.901Z] INFO: Database connected successfully
[2025-06-30T08:15:45.916Z] INFO: Database connection test successful
[2025-06-30T08:15:45.951Z] ERROR: Query execution failed: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'Username'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":207,"lineNumber":2,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-06-30T08:15:45.952Z] ERROR: Error checking data: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'Username'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":207,"lineNumber":2,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-06-30T08:15:45.953Z] INFO: Database connection closed
[2025-06-30T08:16:01.631Z] INFO: Connecting to MSSQL database...
[2025-06-30T08:16:01.709Z] INFO: Database connected successfully
[2025-06-30T08:16:01.728Z] INFO: Database connection test successful
[2025-06-30T08:16:01.793Z] INFO: Database connection closed
[2025-06-30T08:17:10.208Z] INFO: Connecting to MSSQL database...
[2025-06-30T08:17:10.281Z] INFO: Database connected successfully
[2025-06-30T08:17:10.297Z] INFO: Database connection test successful
[2025-06-30T08:17:10.297Z] INFO: Connected to database
[2025-06-30T08:17:10.298Z] INFO: Loading sample data...
[2025-06-30T08:17:10.302Z] INFO: Executed sample data batch 1/3
[2025-06-30T08:17:10.376Z] INFO: Executed sample data batch 2/3
[2025-06-30T08:17:10.377Z] INFO: Sample data loaded successfully
[2025-06-30T08:17:10.387Z] INFO: Database connection closed
[2025-06-30T08:17:10.389Z] INFO: Sample data loading completed successfully
[2025-06-30T08:17:17.823Z] INFO: Connecting to MSSQL database...
[2025-06-30T08:17:17.854Z] INFO: Database connected successfully
[2025-06-30T08:17:17.871Z] INFO: Database connection test successful
[2025-06-30T08:17:17.872Z] INFO: Database connected successfully
[2025-06-30T08:17:17.875Z] INFO: WebSocket service initialized
[2025-06-30T08:17:39.430Z] INFO: ::1 - - [30/Jun/2025:08:17:39 +0000] "GET /api/health HTTP/1.1" 404 45 "-" "axios/1.10.0"
[2025-06-30T08:18:32.230Z] INFO: ::1 - - [30/Jun/2025:08:18:32 +0000] "GET / HTTP/1.1" 404 45 "-" "axios/1.10.0"
[2025-06-30T08:18:53.729Z] INFO: ::1 - - [30/Jun/2025:08:18:53 +0000] "GET / HTTP/1.1" 404 45 "-" "axios/1.10.0"
[2025-06-30T08:20:23.081Z] INFO: Connecting to MSSQL database...
[2025-06-30T08:20:23.112Z] INFO: Database connected successfully
[2025-06-30T08:20:23.128Z] INFO: Database connection test successful
[2025-06-30T08:20:23.129Z] INFO: Database connected successfully
[2025-06-30T08:20:23.131Z] INFO: WebSocket service initialized
[2025-06-30T08:20:23.133Z] INFO: Server running on port 5000 in development mode
[2025-06-30T08:20:23.133Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-06-30T08:20:42.175Z] INFO: ::1 - - [30/Jun/2025:08:20:42 +0000] "GET / HTTP/1.1" 200 195 "-" "axios/1.10.0"
[2025-06-30T08:20:49.716Z] INFO: ::1 - - [30/Jun/2025:08:20:49 +0000] "GET / HTTP/1.1" 200 195 "-" "axios/1.10.0"
[2025-06-30T08:20:49.726Z] INFO: ::1 - - [30/Jun/2025:08:20:49 +0000] "GET /api/products HTTP/1.1" 200 - "-" "axios/1.10.0"
[2025-06-30T08:20:49.738Z] INFO: ::1 - - [30/Jun/2025:08:20:49 +0000] "GET /api/inventory HTTP/1.1" 200 1003 "-" "axios/1.10.0"
[2025-06-30T08:21:11.122Z] INFO: ::1 - - [30/Jun/2025:08:21:11 +0000] "GET / HTTP/1.1" 200 195 "-" "axios/1.10.0"
[2025-06-30T08:21:11.129Z] INFO: ::1 - - [30/Jun/2025:08:21:11 +0000] "GET /api/products HTTP/1.1" 200 - "-" "axios/1.10.0"
[2025-06-30T08:21:11.135Z] INFO: ::1 - - [30/Jun/2025:08:21:11 +0000] "GET /api/inventory HTTP/1.1" 200 1003 "-" "axios/1.10.0"
[2025-06-30T08:34:39.885Z] INFO: Connecting to MSSQL database...
[2025-06-30T08:34:39.921Z] INFO: Database connected successfully
[2025-06-30T08:34:39.939Z] INFO: Database connection test successful
[2025-06-30T08:34:39.939Z] INFO: Database connected successfully
[2025-06-30T08:34:39.942Z] INFO: WebSocket service initialized
[2025-06-30T08:34:39.944Z] INFO: Server running on port 5000 in development mode
[2025-06-30T08:34:39.944Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-06-30T08:35:06.041Z] INFO: ::1 - - [30/Jun/2025:08:35:06 +0000] "GET /health HTTP/1.1" 200 101 "-" "axios/1.9.0"
[2025-06-30T08:35:06.057Z] INFO: ::1 - - [30/Jun/2025:08:35:06 +0000] "GET /api/products HTTP/1.1" 200 - "-" "axios/1.9.0"
[2025-06-30T08:35:06.068Z] INFO: ::1 - - [30/Jun/2025:08:35:06 +0000] "GET /api/auth HTTP/1.1" 404 45 "-" "axios/1.9.0"
[2025-06-30T08:35:06.078Z] INFO: ::1 - - [30/Jun/2025:08:35:06 +0000] "GET /api/inventory HTTP/1.1" 200 1003 "-" "axios/1.9.0"
[2025-06-30T08:36:59.072Z] INFO: ::1 - - [30/Jun/2025:08:36:59 +0000] "GET / HTTP/1.1" 200 195 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T08:36:59.364Z] INFO: ::1 - - [30/Jun/2025:08:36:59 +0000] "GET /favicon.ico HTTP/1.1" 404 45 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T08:37:18.999Z] INFO: ::1 - - [30/Jun/2025:08:37:18 +0000] "GET / HTTP/1.1" 304 - "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T08:38:25.040Z] INFO: Connecting to MSSQL database...
[2025-06-30T08:38:25.083Z] INFO: Database connected successfully
[2025-06-30T08:38:25.106Z] INFO: Database connection test successful
[2025-06-30T08:38:25.107Z] INFO: Database connected successfully
[2025-06-30T08:38:25.111Z] INFO: WebSocket service initialized
[2025-06-30T08:38:25.114Z] INFO: Server running on port 5000 in development mode
[2025-06-30T08:38:25.114Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-06-30T10:19:42.890Z] INFO: Connecting to MSSQL database...
[2025-06-30T10:19:42.947Z] INFO: Database connected successfully
[2025-06-30T10:19:42.965Z] INFO: Database connection test successful
[2025-06-30T10:19:42.966Z] INFO: Database connected successfully
[2025-06-30T10:19:42.969Z] INFO: WebSocket service initialized
[2025-06-30T10:20:01.128Z] INFO: Connecting to MSSQL database...
[2025-06-30T10:20:01.159Z] INFO: Database connected successfully
[2025-06-30T10:20:01.181Z] INFO: Database connection test successful
[2025-06-30T10:20:01.182Z] INFO: Database connected successfully
[2025-06-30T10:20:01.184Z] INFO: WebSocket service initialized
[2025-06-30T10:20:01.186Z] INFO: Server running on port 5000 in development mode
[2025-06-30T10:20:01.186Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-06-30T10:25:08.105Z] INFO: ::1 - - [30/Jun/2025:10:25:08 +0000] "GET /api/health HTTP/1.1" 404 45 "-" "axios/1.10.0"
[2025-06-30T10:25:18.649Z] INFO: ::1 - - [30/Jun/2025:10:25:18 +0000] "GET /api/health HTTP/1.1" 404 45 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-06-30T10:25:42.052Z] INFO: Connecting to MSSQL database...
[2025-06-30T10:25:42.086Z] INFO: Database connected successfully
[2025-06-30T10:25:42.109Z] INFO: Database connection test successful
[2025-06-30T10:25:42.110Z] INFO: Database connected successfully
[2025-06-30T10:25:42.113Z] INFO: WebSocket service initialized
[2025-06-30T10:25:42.115Z] INFO: Server running on port 5000 in development mode
[2025-06-30T10:25:42.115Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-06-30T10:25:59.978Z] INFO: ::1 - - [30/Jun/2025:10:25:59 +0000] "GET /api/health HTTP/1.1" 200 212 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-06-30T10:33:30.772Z] INFO: Connecting to MSSQL database...
[2025-06-30T10:33:30.809Z] INFO: Database connected successfully
[2025-06-30T10:33:30.829Z] INFO: Database connection test successful
[2025-06-30T10:33:30.830Z] INFO: Database connected successfully
[2025-06-30T10:33:30.834Z] INFO: WebSocket service initialized
[2025-06-30T10:33:30.835Z] INFO: Server running on port 5000 in development mode
[2025-06-30T10:33:30.836Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-06-30T10:33:48.980Z] INFO: ::1 - - [30/Jun/2025:10:33:48 +0000] "GET /api/health HTTP/1.1" 200 212 "-" "axios/1.10.0"
[2025-06-30T10:33:54.047Z] INFO: ::1 - - [30/Jun/2025:10:33:54 +0000] "POST /api/payments/webhook HTTP/1.1" 400 68 "-" "axios/1.10.0"
[2025-06-30T10:40:05.317Z] INFO: ::1 - - [30/Jun/2025:10:40:05 +0000] "GET /api/health HTTP/1.1" 200 213 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-06-30T11:02:35.174Z] INFO: Connecting to MSSQL database...
[2025-06-30T11:02:35.210Z] INFO: Database connected successfully
[2025-06-30T11:02:35.228Z] INFO: Database connection test successful
[2025-06-30T11:02:35.228Z] INFO: Database connected successfully
[2025-06-30T11:02:35.231Z] INFO: WebSocket service initialized
[2025-06-30T12:55:47.714Z] INFO: Connecting to MSSQL database...
[2025-06-30T12:55:47.750Z] INFO: Database connected successfully
[2025-06-30T12:55:47.768Z] INFO: Database connection test successful
[2025-06-30T12:55:47.769Z] INFO: Database connected successfully
[2025-06-30T12:55:47.772Z] INFO: WebSocket service initialized
[2025-06-30T12:55:47.773Z] INFO: Server running on port 5000 in development mode
[2025-06-30T12:55:47.774Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-06-30T12:56:13.731Z] INFO: ::1 - - [30/Jun/2025:12:56:13 +0000] "GET /api/health HTTP/1.1" 200 212 "-" "curl/8.10.1"
[2025-06-30T12:56:21.809Z] INFO: ::1 - - [30/Jun/2025:12:56:21 +0000] "POST /api/payments/create-payment-link HTTP/1.1" 404 45 "-" "curl/8.10.1"
[2025-06-30T12:57:45.799Z] INFO: ::1 - - [30/Jun/2025:12:57:45 +0000] "POST /api/payments/create-link HTTP/1.1" 401 51 "-" "curl/8.10.1"
[2025-06-30T12:57:53.653Z] INFO: ::1 - - [30/Jun/2025:12:57:53 +0000] "POST /api/payments/webhook HTTP/1.1" 400 68 "-" "curl/8.10.1"
[2025-06-30T12:59:18.138Z] INFO: ::1 - - [30/Jun/2025:12:59:18 +0000] "GET /api/health HTTP/1.1" 200 213 "-" "axios/1.10.0"
[2025-06-30T12:59:18.145Z] INFO: ::1 - - [30/Jun/2025:12:59:18 +0000] "GET /api/health HTTP/1.1" 200 213 "-" "axios/1.10.0"
[2025-06-30T12:59:18.153Z] INFO: ::1 - - [30/Jun/2025:12:59:18 +0000] "POST /api/payments/create-link HTTP/1.1" 401 51 "-" "axios/1.10.0"
[2025-06-30T12:59:18.160Z] INFO: ::1 - - [30/Jun/2025:12:59:18 +0000] "POST /api/payments/webhook HTTP/1.1" 400 68 "-" "axios/1.10.0"
[2025-06-30T13:00:03.380Z] INFO: ::1 - - [30/Jun/2025:13:00:03 +0000] "GET /api/health HTTP/1.1" 200 213 "-" "axios/1.10.0"
[2025-06-30T13:00:03.388Z] INFO: ::1 - - [30/Jun/2025:13:00:03 +0000] "GET /api/health HTTP/1.1" 200 213 "-" "axios/1.10.0"
[2025-06-30T13:00:03.392Z] INFO: ::1 - - [30/Jun/2025:13:00:03 +0000] "POST /api/payments/create-link HTTP/1.1" 401 51 "-" "axios/1.10.0"
[2025-06-30T13:00:03.397Z] INFO: ::1 - - [30/Jun/2025:13:00:03 +0000] "POST /api/payments/webhook HTTP/1.1" 400 68 "-" "axios/1.10.0"
[2025-06-30T13:00:40.784Z] INFO: ::1 - - [30/Jun/2025:13:00:40 +0000] "GET /api/health HTTP/1.1" 200 212 "-" "axios/1.10.0"
[2025-06-30T13:00:40.792Z] INFO: ::1 - - [30/Jun/2025:13:00:40 +0000] "GET /api/health HTTP/1.1" 200 213 "-" "axios/1.10.0"
[2025-06-30T13:00:40.797Z] INFO: ::1 - - [30/Jun/2025:13:00:40 +0000] "POST /api/payments/create-link HTTP/1.1" 401 51 "-" "axios/1.10.0"
[2025-06-30T13:00:40.803Z] INFO: ::1 - - [30/Jun/2025:13:00:40 +0000] "POST /api/payments/webhook HTTP/1.1" 400 68 "-" "axios/1.10.0"
[2025-06-30T13:01:20.653Z] INFO: ::1 - - [30/Jun/2025:13:01:20 +0000] "GET /api/health HTTP/1.1" 200 213 "-" "axios/1.10.0"
[2025-06-30T13:01:20.661Z] INFO: ::1 - - [30/Jun/2025:13:01:20 +0000] "GET /api/health HTTP/1.1" 200 213 "-" "axios/1.10.0"
[2025-06-30T13:01:20.665Z] INFO: ::1 - - [30/Jun/2025:13:01:20 +0000] "POST /api/payments/create-link HTTP/1.1" 401 51 "-" "axios/1.10.0"
[2025-06-30T13:01:20.670Z] INFO: ::1 - - [30/Jun/2025:13:01:20 +0000] "POST /api/payments/webhook HTTP/1.1" 400 68 "-" "axios/1.10.0"
[2025-06-30T13:44:40.950Z] INFO: ::1 - - [30/Jun/2025:13:44:40 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:44:40.955Z] INFO: ::1 - - [30/Jun/2025:13:44:40 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:44:55.064Z] INFO: ::1 - - [30/Jun/2025:13:44:55 +0000] "POST /api/payments/create-link HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:44:57.802Z] INFO: ::1 - - [30/Jun/2025:13:44:57 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:44:59.044Z] INFO: ::1 - - [30/Jun/2025:13:44:59 +0000] "POST /api/payments/create-link HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:44:59.597Z] INFO: ::1 - - [30/Jun/2025:13:44:59 +0000] "POST /api/payments/create-link HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:45:14.019Z] INFO: ::1 - - [30/Jun/2025:13:45:14 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:45:14.024Z] INFO: ::1 - - [30/Jun/2025:13:45:14 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:45:16.450Z] INFO: ::1 - - [30/Jun/2025:13:45:16 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:45:17.713Z] INFO: ::1 - - [30/Jun/2025:13:45:17 +0000] "POST /api/payments/create-link HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:45:38.967Z] INFO: ::1 - - [30/Jun/2025:13:45:38 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:45:40.482Z] INFO: ::1 - - [30/Jun/2025:13:45:40 +0000] "POST /api/payments/create-link HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:45:41.099Z] INFO: ::1 - - [30/Jun/2025:13:45:41 +0000] "POST /api/payments/create-link HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:51:02.272Z] INFO: ::1 - - [30/Jun/2025:13:51:02 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:51:03.979Z] INFO: ::1 - - [30/Jun/2025:13:51:03 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:51:04.785Z] INFO: ::1 - - [30/Jun/2025:13:51:04 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:51:05.549Z] INFO: ::1 - - [30/Jun/2025:13:51:05 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:54:06.385Z] INFO: ::1 - - [30/Jun/2025:13:54:06 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:54:06.387Z] INFO: ::1 - - [30/Jun/2025:13:54:06 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:54:50.757Z] INFO: ::1 - - [30/Jun/2025:13:54:50 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:54:50.759Z] INFO: ::1 - - [30/Jun/2025:13:54:50 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:54:52.811Z] INFO: ::1 - - [30/Jun/2025:13:54:52 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:54:54.258Z] INFO: ::1 - - [30/Jun/2025:13:54:54 +0000] "POST /api/payments/create-link HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:55:05.474Z] INFO: ::1 - - [30/Jun/2025:13:55:05 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:55:53.446Z] INFO: ::1 - - [30/Jun/2025:13:55:53 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T13:55:54.154Z] INFO: ::1 - - [30/Jun/2025:13:55:54 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T14:15:15.156Z] INFO: ::1 - - [30/Jun/2025:14:15:15 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T14:15:15.158Z] INFO: ::1 - - [30/Jun/2025:14:15:15 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T14:19:01.891Z] INFO: ::1 - - [30/Jun/2025:14:19:01 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T14:19:01.894Z] INFO: ::1 - - [30/Jun/2025:14:19:01 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T15:11:29.915Z] INFO: ::1 - - [30/Jun/2025:15:11:29 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-06-30T15:11:29.918Z] INFO: ::1 - - [30/Jun/2025:15:11:29 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T11:53:38.841Z] INFO: Connecting to MSSQL database...
[2025-07-01T11:53:38.878Z] INFO: Database connected successfully
[2025-07-01T11:53:38.898Z] INFO: Database connection test successful
[2025-07-01T11:53:38.898Z] INFO: Database connected successfully
[2025-07-01T11:53:38.901Z] INFO: WebSocket service initialized
[2025-07-01T11:53:38.903Z] INFO: Server running on port 5000 in development mode
[2025-07-01T11:53:38.904Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-01T11:55:33.335Z] INFO: ::1 - - [01/Jul/2025:11:55:33 +0000] "GET /api/health HTTP/1.1" 200 213 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-01T11:56:11.728Z] INFO: ::1 - - [01/Jul/2025:11:56:11 +0000] "GET /api/payments/health HTTP/1.1" 404 47 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-01T11:56:18.333Z] INFO: ::1 - - [01/Jul/2025:11:56:18 +0000] "GET /api/payments HTTP/1.1" 200 340 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-01T11:56:48.131Z] INFO: ::1 - - [01/Jul/2025:11:56:48 +0000] "GET /api/health HTTP/1.1" 200 213 "-" "node"
[2025-07-01T11:56:48.145Z] INFO: ::1 - - [01/Jul/2025:11:56:48 +0000] "GET /api/payments HTTP/1.1" 200 340 "-" "node"
[2025-07-01T11:56:48.166Z] INFO: ::1 - - [01/Jul/2025:11:56:48 +0000] "POST /api/payments/create-link HTTP/1.1" 401 51 "-" "node"
[2025-07-01T12:00:12.199Z] INFO: ::1 - - [01/Jul/2025:12:00:12 +0000] "GET /api/health HTTP/1.1" 200 213 "-" "node"
[2025-07-01T12:00:12.212Z] INFO: ::1 - - [01/Jul/2025:12:00:12 +0000] "GET /api/payments HTTP/1.1" 200 340 "-" "node"
[2025-07-01T12:00:12.221Z] INFO: ::1 - - [01/Jul/2025:12:00:12 +0000] "POST /api/payments/create-link HTTP/1.1" 401 51 "-" "node"
[2025-07-01T12:00:27.885Z] INFO: Connecting to MSSQL database...
[2025-07-01T12:00:27.919Z] INFO: Database connected successfully
[2025-07-01T12:00:27.940Z] INFO: Database connection test successful
[2025-07-01T12:00:27.941Z] INFO: Database connected successfully
[2025-07-01T12:00:27.943Z] INFO: WebSocket service initialized
[2025-07-01T12:00:27.945Z] INFO: Server running on port 5000 in development mode
[2025-07-01T12:00:27.945Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-01T12:00:50.454Z] INFO: ::1 - - [01/Jul/2025:12:00:50 +0000] "GET /api/health HTTP/1.1" 200 212 "-" "node"
[2025-07-01T12:00:50.465Z] INFO: ::1 - - [01/Jul/2025:12:00:50 +0000] "GET /api/payments HTTP/1.1" 200 340 "-" "node"
[2025-07-01T12:00:50.681Z] INFO: ::1 - - [01/Jul/2025:12:00:50 +0000] "POST /api/payments/create-link HTTP/1.1" 400 134 "-" "node"
[2025-07-01T12:02:20.655Z] INFO: Connecting to MSSQL database...
[2025-07-01T12:02:20.692Z] INFO: Database connected successfully
[2025-07-01T12:02:20.710Z] INFO: Database connection test successful
[2025-07-01T12:02:20.711Z] INFO: Database connected successfully
[2025-07-01T12:02:20.713Z] INFO: WebSocket service initialized
[2025-07-01T12:02:20.715Z] INFO: Server running on port 5000 in development mode
[2025-07-01T12:02:20.716Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-01T12:02:41.853Z] INFO: ::1 - - [01/Jul/2025:12:02:41 +0000] "GET /api/health HTTP/1.1" 200 211 "-" "node"
[2025-07-01T12:02:41.865Z] INFO: ::1 - - [01/Jul/2025:12:02:41 +0000] "GET /api/payments HTTP/1.1" 200 340 "-" "node"
[2025-07-01T12:02:42.356Z] INFO: ::1 - - [01/Jul/2025:12:02:42 +0000] "POST /api/payments/create-link HTTP/1.1" 201 919 "-" "node"
[2025-07-01T12:03:21.505Z] INFO: ::1 - - [01/Jul/2025:12:03:21 +0000] "POST /api/payments/create-link HTTP/1.1" 400 160 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:03:22.533Z] INFO: ::1 - - [01/Jul/2025:12:03:22 +0000] "POST /api/payments/create-link HTTP/1.1" 400 160 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:03:29.526Z] INFO: ::1 - - [01/Jul/2025:12:03:29 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 122 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:03:31.496Z] INFO: ::1 - - [01/Jul/2025:12:03:31 +0000] "POST /api/payments/create-link HTTP/1.1" 201 981 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:03:33.447Z] INFO: ::1 - - [01/Jul/2025:12:03:33 +0000] "POST /api/payments/create-link HTTP/1.1" 201 981 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:03:55.020Z] INFO: ::1 - - [01/Jul/2025:12:03:55 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 117 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:03:55.023Z] INFO: ::1 - - [01/Jul/2025:12:03:55 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 117 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:04:01.573Z] INFO: ::1 - - [01/Jul/2025:12:04:01 +0000] "POST /api/payments/create-link HTTP/1.1" 201 964 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:04:03.216Z] INFO: ::1 - - [01/Jul/2025:12:04:03 +0000] "POST /api/payments/create-link HTTP/1.1" 201 964 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:06:57.379Z] INFO: ::1 - - [01/Jul/2025:12:06:57 +0000] "POST /api/payments/create-link HTTP/1.1" 201 860 "-" "axios/1.10.0"
[2025-07-01T12:06:57.799Z] INFO: ::1 - - [01/Jul/2025:12:06:57 +0000] "POST /api/payments/create-link HTTP/1.1" 201 889 "-" "axios/1.10.0"
[2025-07-01T12:06:57.803Z] INFO: ::1 - - [01/Jul/2025:12:06:57 +0000] "POST /api/payments/create-link HTTP/1.1" 400 260 "-" "axios/1.10.0"
[2025-07-01T12:07:40.925Z] INFO: ::1 - - [01/Jul/2025:12:07:40 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 117 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:07:59.929Z] INFO: ::1 - - [01/Jul/2025:12:07:59 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:13:37.636Z] INFO: ::1 - - [01/Jul/2025:12:13:37 +0000] "POST /api/payments/create-link HTTP/1.1" 201 960 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:13:38.720Z] INFO: ::1 - - [01/Jul/2025:12:13:38 +0000] "POST /api/payments/create-link HTTP/1.1" 201 960 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:13:43.855Z] INFO: ::1 - - [01/Jul/2025:12:13:43 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:13:45.346Z] INFO: ::1 - - [01/Jul/2025:12:13:45 +0000] "POST /api/payments/create-link HTTP/1.1" 201 961 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:13:46.210Z] INFO: ::1 - - [01/Jul/2025:12:13:46 +0000] "POST /api/payments/create-link HTTP/1.1" 201 961 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:14:33.039Z] INFO: ::1 - - [01/Jul/2025:12:14:33 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:14:33.041Z] INFO: ::1 - - [01/Jul/2025:12:14:33 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:14:41.970Z] INFO: ::1 - - [01/Jul/2025:12:14:41 +0000] "POST /api/payments/create-link HTTP/1.1" 201 960 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:14:53.261Z] INFO: ::1 - - [01/Jul/2025:12:14:53 +0000] "POST /api/payments/create-link HTTP/1.1" 201 960 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:16:11.354Z] INFO: ::1 - - [01/Jul/2025:12:16:11 +0000] "POST /api/payments/create-link HTTP/1.1" 201 945 "-" "axios/1.10.0"
[2025-07-01T12:16:24.934Z] INFO: ::1 - - [01/Jul/2025:12:16:24 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:18:14.925Z] INFO: ::1 - - [01/Jul/2025:12:18:14 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:20:43.924Z] INFO: ::1 - - [01/Jul/2025:12:20:43 +0000] "POST /api/payments/create-link HTTP/1.1" 201 960 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:21:02.011Z] INFO: ::1 - - [01/Jul/2025:12:21:02 +0000] "POST /api/payments/create-link HTTP/1.1" 201 960 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:21:26.917Z] INFO: ::1 - - [01/Jul/2025:12:21:26 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 116 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:21:26.920Z] INFO: ::1 - - [01/Jul/2025:12:21:26 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 116 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:21:38.321Z] INFO: ::1 - - [01/Jul/2025:12:21:38 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:21:39.656Z] INFO: ::1 - - [01/Jul/2025:12:21:39 +0000] "POST /api/payments/create-link HTTP/1.1" 201 958 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:21:40.977Z] INFO: ::1 - - [01/Jul/2025:12:21:40 +0000] "POST /api/payments/create-link HTTP/1.1" 201 958 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:22:38.830Z] INFO: ::1 - - [01/Jul/2025:12:22:38 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:22:45.510Z] INFO: ::1 - - [01/Jul/2025:12:22:45 +0000] "POST /api/payments/create-link HTTP/1.1" 201 960 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:22:46.840Z] INFO: ::1 - - [01/Jul/2025:12:22:46 +0000] "POST /api/payments/create-link HTTP/1.1" 201 960 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:22:47.891Z] INFO: ::1 - - [01/Jul/2025:12:22:47 +0000] "POST /api/payments/create-link HTTP/1.1" 201 960 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:22:51.064Z] INFO: ::1 - - [01/Jul/2025:12:22:51 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 116 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:22:51.067Z] INFO: ::1 - - [01/Jul/2025:12:22:51 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 116 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:23:00.394Z] INFO: ::1 - - [01/Jul/2025:12:23:00 +0000] "POST /api/payments/create-link HTTP/1.1" 201 957 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:23:09.890Z] INFO: ::1 - - [01/Jul/2025:12:23:09 +0000] "POST /api/payments/create-link HTTP/1.1" 201 957 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:23:56.883Z] INFO: ::1 - - [01/Jul/2025:12:23:56 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:23:57.893Z] INFO: ::1 - - [01/Jul/2025:12:23:57 +0000] "POST /api/payments/create-link HTTP/1.1" 201 957 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:23:59.423Z] INFO: ::1 - - [01/Jul/2025:12:23:59 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:24:00.403Z] INFO: ::1 - - [01/Jul/2025:12:24:00 +0000] "POST /api/payments/create-link HTTP/1.1" 201 960 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:24:01.109Z] INFO: ::1 - - [01/Jul/2025:12:24:01 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:24:02.247Z] INFO: ::1 - - [01/Jul/2025:12:24:02 +0000] "POST /api/payments/create-link HTTP/1.1" 201 958 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:24:03.311Z] INFO: ::1 - - [01/Jul/2025:12:24:03 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 116 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:24:05.777Z] INFO: ::1 - - [01/Jul/2025:12:24:05 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:24:07.733Z] INFO: ::1 - - [01/Jul/2025:12:24:07 +0000] "POST /api/payments/create-link HTTP/1.1" 201 957 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:24:09.568Z] INFO: ::1 - - [01/Jul/2025:12:24:09 +0000] "POST /api/payments/create-link HTTP/1.1" 201 957 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:37:46.338Z] INFO: ::1 - - [01/Jul/2025:12:37:46 +0000] "POST /api/payments/create-link HTTP/1.1" 201 903 "-" "axios/1.10.0"
[2025-07-01T12:40:33.096Z] INFO: ::1 - - [01/Jul/2025:12:40:33 +0000] "POST /api/payments/create-link HTTP/1.1" 201 957 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:40:45.656Z] INFO: ::1 - - [01/Jul/2025:12:40:45 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:40:45.660Z] INFO: ::1 - - [01/Jul/2025:12:40:45 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:40:52.100Z] INFO: ::1 - - [01/Jul/2025:12:40:52 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:40:53.034Z] INFO: ::1 - - [01/Jul/2025:12:40:53 +0000] "POST /api/payments/create-link HTTP/1.1" 201 960 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:40:54.211Z] INFO: ::1 - - [01/Jul/2025:12:40:54 +0000] "POST /api/payments/create-link HTTP/1.1" 201 960 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:41:55.645Z] INFO: ::1 - - [01/Jul/2025:12:41:55 +0000] "POST /api/payments/create-link HTTP/1.1" 201 960 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
[2025-07-01T12:42:38.925Z] INFO: ::1 - - [01/Jul/2025:12:42:38 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:42:52.932Z] INFO: ::1 - - [01/Jul/2025:12:42:52 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:43:05.941Z] INFO: ::1 - - [01/Jul/2025:12:43:05 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:43:20.439Z] INFO: ::1 - - [01/Jul/2025:12:43:20 +0000] "POST /api/payments/create-link HTTP/1.1" 201 960 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:43:26.126Z] INFO: ::1 - - [01/Jul/2025:12:43:26 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 200 272 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:43:31.086Z] INFO: ::1 - - [01/Jul/2025:12:43:31 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:43:36.177Z] INFO: ::1 - - [01/Jul/2025:12:43:36 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:43:41.117Z] INFO: ::1 - - [01/Jul/2025:12:43:41 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:43:46.142Z] INFO: ::1 - - [01/Jul/2025:12:43:46 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:43:51.086Z] INFO: ::1 - - [01/Jul/2025:12:43:51 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:43:56.130Z] INFO: ::1 - - [01/Jul/2025:12:43:56 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:44:01.191Z] INFO: ::1 - - [01/Jul/2025:12:44:01 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:44:06.111Z] INFO: ::1 - - [01/Jul/2025:12:44:06 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:44:11.127Z] INFO: ::1 - - [01/Jul/2025:12:44:11 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:44:16.083Z] INFO: ::1 - - [01/Jul/2025:12:44:16 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:44:21.110Z] INFO: ::1 - - [01/Jul/2025:12:44:21 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:44:26.188Z] INFO: ::1 - - [01/Jul/2025:12:44:26 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:44:31.079Z] INFO: ::1 - - [01/Jul/2025:12:44:31 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:44:36.136Z] INFO: ::1 - - [01/Jul/2025:12:44:36 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:44:41.250Z] INFO: ::1 - - [01/Jul/2025:12:44:41 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 200 270 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:44:45.774Z] INFO: ::1 - - [01/Jul/2025:12:44:45 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:44:50.791Z] INFO: ::1 - - [01/Jul/2025:12:44:50 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:44:56.279Z] INFO: ::1 - - [01/Jul/2025:12:44:56 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:45:01.213Z] INFO: ::1 - - [01/Jul/2025:12:45:01 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:45:06.213Z] INFO: ::1 - - [01/Jul/2025:12:45:06 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:45:11.222Z] INFO: ::1 - - [01/Jul/2025:12:45:11 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:45:16.216Z] INFO: ::1 - - [01/Jul/2025:12:45:16 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:45:21.244Z] INFO: ::1 - - [01/Jul/2025:12:45:21 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:45:25.778Z] INFO: ::1 - - [01/Jul/2025:12:45:25 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:45:30.727Z] INFO: ::1 - - [01/Jul/2025:12:45:30 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:45:35.746Z] INFO: ::1 - - [01/Jul/2025:12:45:35 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:45:40.769Z] INFO: ::1 - - [01/Jul/2025:12:45:40 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:45:45.763Z] INFO: ::1 - - [01/Jul/2025:12:45:45 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:45:50.752Z] INFO: ::1 - - [01/Jul/2025:12:45:50 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:45:56.366Z] INFO: ::1 - - [01/Jul/2025:12:45:56 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:46:01.237Z] INFO: ::1 - - [01/Jul/2025:12:46:01 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:46:06.190Z] INFO: ::1 - - [01/Jul/2025:12:46:06 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:46:11.373Z] INFO: ::1 - - [01/Jul/2025:12:46:11 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:46:16.183Z] INFO: ::1 - - [01/Jul/2025:12:46:16 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:46:21.216Z] INFO: ::1 - - [01/Jul/2025:12:46:21 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:46:26.235Z] INFO: ::1 - - [01/Jul/2025:12:46:26 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:46:31.218Z] INFO: ::1 - - [01/Jul/2025:12:46:31 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:46:36.219Z] INFO: ::1 - - [01/Jul/2025:12:46:36 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:46:41.430Z] INFO: ::1 - - [01/Jul/2025:12:46:41 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:46:46.208Z] INFO: ::1 - - [01/Jul/2025:12:46:46 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:46:51.330Z] INFO: ::1 - - [01/Jul/2025:12:46:51 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:47:39.323Z] INFO: ::1 - - [01/Jul/2025:12:47:39 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:47:45.516Z] INFO: ::1 - - [01/Jul/2025:12:47:45 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:47:45.863Z] INFO: ::1 - - [01/Jul/2025:12:47:45 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:47:50.786Z] INFO: ::1 - - [01/Jul/2025:12:47:50 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:47:55.788Z] INFO: ::1 - - [01/Jul/2025:12:47:55 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:48:00.783Z] INFO: ::1 - - [01/Jul/2025:12:48:00 +0000] "GET /api/payments/status/ORDER_1751373799997/link_UE9EuwZ4VktP9XbXBvBQYVrp HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:53:54.934Z] INFO: ::1 - - [01/Jul/2025:12:53:54 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:54:14.933Z] INFO: ::1 - - [01/Jul/2025:12:54:14 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:55:36.064Z] INFO: ::1 - - [01/Jul/2025:12:55:36 +0000] "POST /api/payments/create-link HTTP/1.1" 201 960 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:55:51.278Z] INFO: ::1 - - [01/Jul/2025:12:55:51 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 200 272 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:56:07.186Z] INFO: ::1 - - [01/Jul/2025:12:56:07 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:56:22.118Z] INFO: ::1 - - [01/Jul/2025:12:56:22 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:56:37.179Z] INFO: ::1 - - [01/Jul/2025:12:56:37 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:56:52.195Z] INFO: ::1 - - [01/Jul/2025:12:56:52 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:57:07.143Z] INFO: ::1 - - [01/Jul/2025:12:57:07 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:57:39.189Z] INFO: ::1 - - [01/Jul/2025:12:57:39 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:58:39.304Z] INFO: ::1 - - [01/Jul/2025:12:58:39 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T12:59:39.174Z] INFO: ::1 - - [01/Jul/2025:12:59:39 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:00:39.248Z] INFO: ::1 - - [01/Jul/2025:13:00:39 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:01:39.336Z] INFO: ::1 - - [01/Jul/2025:13:01:39 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:02:39.173Z] INFO: ::1 - - [01/Jul/2025:13:02:39 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:03:39.285Z] INFO: ::1 - - [01/Jul/2025:13:03:39 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:04:39.225Z] INFO: ::1 - - [01/Jul/2025:13:04:39 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:05:39.193Z] INFO: ::1 - - [01/Jul/2025:13:05:39 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:06:39.184Z] INFO: ::1 - - [01/Jul/2025:13:06:39 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:07:10.372Z] INFO: ::1 - - [01/Jul/2025:13:07:10 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:07:21.428Z] INFO: ::1 - - [01/Jul/2025:13:07:21 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:07:26.641Z] INFO: ::1 - - [01/Jul/2025:13:07:26 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:07:26.643Z] INFO: ::1 - - [01/Jul/2025:13:07:26 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:07:31.835Z] INFO: ::1 - - [01/Jul/2025:13:07:31 +0000] "POST /api/payments/create-link HTTP/1.1" 201 960 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:07:37.117Z] INFO: ::1 - - [01/Jul/2025:13:07:37 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:07:47.121Z] INFO: ::1 - - [01/Jul/2025:13:07:47 +0000] "GET /api/payments/status/ORDER_1751375251523/link_A8x9iftLDCKnse6QyKirnVN6 HTTP/1.1" 200 272 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:07:52.070Z] INFO: ::1 - - [01/Jul/2025:13:07:52 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:08:02.231Z] INFO: ::1 - - [01/Jul/2025:13:08:02 +0000] "GET /api/payments/status/ORDER_1751375251523/link_A8x9iftLDCKnse6QyKirnVN6 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:08:07.150Z] INFO: ::1 - - [01/Jul/2025:13:08:07 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:08:17.192Z] INFO: ::1 - - [01/Jul/2025:13:08:17 +0000] "GET /api/payments/status/ORDER_1751375251523/link_A8x9iftLDCKnse6QyKirnVN6 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:08:22.169Z] INFO: ::1 - - [01/Jul/2025:13:08:22 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:08:32.181Z] INFO: ::1 - - [01/Jul/2025:13:08:32 +0000] "GET /api/payments/status/ORDER_1751375251523/link_A8x9iftLDCKnse6QyKirnVN6 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:08:36.258Z] INFO: ::1 - - [01/Jul/2025:13:08:36 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:08:47.326Z] INFO: ::1 - - [01/Jul/2025:13:08:47 +0000] "GET /api/payments/status/ORDER_1751375251523/link_A8x9iftLDCKnse6QyKirnVN6 HTTP/1.1" 200 270 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:08:51.274Z] INFO: ::1 - - [01/Jul/2025:13:08:51 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:09:06.331Z] INFO: ::1 - - [01/Jul/2025:13:09:06 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:09:21.274Z] INFO: ::1 - - [01/Jul/2025:13:09:21 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:09:23.099Z] INFO: ::1 - - [01/Jul/2025:13:09:23 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:09:23.102Z] INFO: ::1 - - [01/Jul/2025:13:09:23 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:09:29.451Z] INFO: ::1 - - [01/Jul/2025:13:09:29 +0000] "POST /api/payments/create-link HTTP/1.1" 201 960 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:09:37.181Z] INFO: ::1 - - [01/Jul/2025:13:09:37 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:09:45.133Z] INFO: ::1 - - [01/Jul/2025:13:09:45 +0000] "GET /api/payments/status/ORDER_1751375369182/link_Nnd44dRbtjgwY1FBfMAvGLPo HTTP/1.1" 200 272 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:09:52.166Z] INFO: ::1 - - [01/Jul/2025:13:09:52 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:10:00.337Z] INFO: ::1 - - [01/Jul/2025:13:10:00 +0000] "GET /api/payments/status/ORDER_1751375369182/link_Nnd44dRbtjgwY1FBfMAvGLPo HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:10:07.312Z] INFO: ::1 - - [01/Jul/2025:13:10:07 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:10:15.179Z] INFO: ::1 - - [01/Jul/2025:13:10:15 +0000] "GET /api/payments/status/ORDER_1751375369182/link_Nnd44dRbtjgwY1FBfMAvGLPo HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:10:22.293Z] INFO: ::1 - - [01/Jul/2025:13:10:22 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:10:30.190Z] INFO: ::1 - - [01/Jul/2025:13:10:30 +0000] "GET /api/payments/status/ORDER_1751375369182/link_Nnd44dRbtjgwY1FBfMAvGLPo HTTP/1.1" 200 270 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T13:10:36.276Z] INFO: ::1 - - [01/Jul/2025:13:10:36 +0000] "GET /api/payments/status/ORDER_1751374535646/link_BVyQLmJ6XfHDufz4EDgtFQFP HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T15:56:04.201Z] INFO: Connecting to MSSQL database...
[2025-07-01T15:56:04.256Z] INFO: Database connected successfully
[2025-07-01T15:56:04.279Z] INFO: Database connection test successful
[2025-07-01T15:56:04.279Z] INFO: Database connected successfully
[2025-07-01T15:56:04.283Z] INFO: WebSocket service initialized
[2025-07-01T15:56:04.285Z] INFO: Server running on port 5000 in development mode
[2025-07-01T15:56:04.285Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-01T15:56:10.685Z] INFO: ::1 - - [01/Jul/2025:15:56:10 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T15:56:11.130Z] INFO: ::1 - - [01/Jul/2025:15:56:11 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T15:56:54.440Z] INFO: Connecting to MSSQL database...
[2025-07-01T15:56:54.474Z] INFO: Database connected successfully
[2025-07-01T15:56:54.497Z] INFO: Database connection test successful
[2025-07-01T15:56:54.498Z] INFO: Database connected successfully
[2025-07-01T15:56:54.500Z] INFO: WebSocket service initialized
[2025-07-01T15:56:54.502Z] INFO: Server running on port 5000 in development mode
[2025-07-01T15:56:54.503Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-01T15:57:11.589Z] INFO: ::1 - - [01/Jul/2025:15:57:11 +0000] "GET /api/orders HTTP/1.1" 401 51 "-" "curl/8.10.1"
[2025-07-01T15:59:11.749Z] INFO: ::1 - - [01/Jul/2025:15:59:11 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T15:59:11.754Z] INFO: ::1 - - [01/Jul/2025:15:59:11 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T15:59:40.515Z] INFO: ::1 - - [01/Jul/2025:15:59:40 +0000] "POST /api/auth/login HTTP/1.1" 401 49 "-" "curl/8.10.1"
[2025-07-01T15:59:41.801Z] INFO: ::1 - - [01/Jul/2025:15:59:41 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T15:59:41.804Z] INFO: ::1 - - [01/Jul/2025:15:59:41 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T15:59:42.556Z] INFO: ::1 - - [01/Jul/2025:15:59:42 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T15:59:48.068Z] INFO: ::1 - - [01/Jul/2025:15:59:48 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T15:59:48.070Z] INFO: ::1 - - [01/Jul/2025:15:59:48 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T15:59:48.932Z] INFO: ::1 - - [01/Jul/2025:15:59:48 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T15:59:49.541Z] INFO: ::1 - - [01/Jul/2025:15:59:49 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T15:59:51.449Z] INFO: ::1 - - [01/Jul/2025:15:59:51 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T15:59:52.028Z] INFO: ::1 - - [01/Jul/2025:15:59:52 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:00:03.376Z] INFO: ::1 - - [01/Jul/2025:16:00:03 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
[2025-07-01T16:00:46.320Z] INFO: ::1 - - [01/Jul/2025:16:00:46 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:00:56.928Z] INFO: Connecting to MSSQL database...
[2025-07-01T16:00:56.965Z] INFO: Database connected successfully
[2025-07-01T16:00:56.983Z] INFO: Database connection test successful
[2025-07-01T16:00:56.984Z] INFO: Database connected successfully
[2025-07-01T16:00:56.986Z] INFO: WebSocket service initialized
[2025-07-01T16:00:56.988Z] INFO: Server running on port 5000 in development mode
[2025-07-01T16:00:56.989Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-01T16:01:15.126Z] INFO: ::1 - - [01/Jul/2025:16:01:15 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "-" "curl/8.10.1"
[2025-07-01T16:01:23.935Z] ERROR: Error executing custom query: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":208,"state":1,"class":16,"message":"Invalid object name 'Orders'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":208,"lineNumber":2,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-01T16:01:23.936Z] ERROR: Error getting orders with filters: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":208,"state":1,"class":16,"message":"Invalid object name 'Orders'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":208,"lineNumber":2,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-01T16:01:23.936Z] ERROR: Get orders error: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":208,"state":1,"class":16,"message":"Invalid object name 'Orders'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":208,"lineNumber":2,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-01T16:01:23.938Z] INFO: ::1 - - [01/Jul/2025:16:01:23 +0000] "GET /api/orders HTTP/1.1" 500 95 "-" "curl/8.10.1"
[2025-07-01T16:02:22.539Z] INFO: ::1 - - [01/Jul/2025:16:02:22 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:02:22.965Z] INFO: ::1 - - [01/Jul/2025:16:02:22 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:03:16.258Z] INFO: Connecting to MSSQL database...
[2025-07-01T16:03:16.327Z] INFO: Database connected successfully
[2025-07-01T16:03:16.346Z] INFO: Database connection test successful
[2025-07-01T16:03:41.399Z] INFO: ::1 - - [01/Jul/2025:16:03:41 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:04:14.518Z] INFO: Connecting to MSSQL database...
[2025-07-01T16:04:14.561Z] INFO: Database connected successfully
[2025-07-01T16:04:14.579Z] INFO: Database connection test successful
[2025-07-01T16:04:31.504Z] INFO: Connecting to MSSQL database...
[2025-07-01T16:04:31.543Z] INFO: Database connected successfully
[2025-07-01T16:04:31.561Z] INFO: Database connection test successful
[2025-07-01T16:05:06.417Z] INFO: ::1 - - [01/Jul/2025:16:05:06 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:05:07.814Z] INFO: ::1 - - [01/Jul/2025:16:05:07 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:05:08.470Z] INFO: ::1 - - [01/Jul/2025:16:05:08 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:05:09.057Z] INFO: ::1 - - [01/Jul/2025:16:05:09 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:05:09.506Z] INFO: ::1 - - [01/Jul/2025:16:05:09 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:05:09.899Z] INFO: ::1 - - [01/Jul/2025:16:05:09 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:05:10.250Z] INFO: ::1 - - [01/Jul/2025:16:05:10 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:05:10.596Z] INFO: ::1 - - [01/Jul/2025:16:05:10 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:06:22.747Z] INFO: ::1 - - [01/Jul/2025:16:06:22 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:06:22.748Z] INFO: ::1 - - [01/Jul/2025:16:06:22 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:06:23.672Z] INFO: ::1 - - [01/Jul/2025:16:06:23 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:06:24.179Z] INFO: ::1 - - [01/Jul/2025:16:06:24 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:06:48.108Z] INFO: ::1 - - [01/Jul/2025:16:06:48 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:06:48.499Z] INFO: ::1 - - [01/Jul/2025:16:06:48 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:06:48.663Z] INFO: ::1 - - [01/Jul/2025:16:06:48 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:06:50.395Z] INFO: Connecting to MSSQL database...
[2025-07-01T16:06:50.441Z] INFO: Database connected successfully
[2025-07-01T16:06:50.460Z] INFO: Database connection test successful
[2025-07-01T16:08:35.984Z] ERROR: Error executing custom query: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":208,"state":1,"class":16,"message":"Invalid object name 'Orders'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":208,"lineNumber":2,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-01T16:08:35.985Z] ERROR: Error getting orders with filters: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":208,"state":1,"class":16,"message":"Invalid object name 'Orders'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":208,"lineNumber":2,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-01T16:08:35.985Z] ERROR: Get orders error: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":208,"state":1,"class":16,"message":"Invalid object name 'Orders'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":208,"lineNumber":2,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-01T16:08:35.986Z] INFO: ::1 - - [01/Jul/2025:16:08:35 +0000] "GET /api/orders HTTP/1.1" 500 95 "-" "curl/8.10.1"
[2025-07-01T16:08:52.603Z] INFO: Connecting to MSSQL database...
[2025-07-01T16:08:52.635Z] INFO: Database connected successfully
[2025-07-01T16:08:52.652Z] INFO: Database connection test successful
[2025-07-01T16:08:52.653Z] INFO: Database connected successfully
[2025-07-01T16:08:52.655Z] INFO: WebSocket service initialized
[2025-07-01T16:08:52.657Z] INFO: Server running on port 5000 in development mode
[2025-07-01T16:08:52.657Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-01T16:09:25.400Z] INFO: Connecting to MSSQL database...
[2025-07-01T16:09:25.443Z] INFO: Database connected successfully
[2025-07-01T16:09:25.462Z] INFO: Database connection test successful
[2025-07-01T16:09:51.635Z] ERROR: Error executing custom query: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":208,"state":1,"class":16,"message":"Invalid object name 'Orders'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":208,"lineNumber":2,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-01T16:09:51.636Z] ERROR: Error getting orders with filters: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":208,"state":1,"class":16,"message":"Invalid object name 'Orders'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":208,"lineNumber":2,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-01T16:09:51.637Z] ERROR: Get orders error: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":208,"state":1,"class":16,"message":"Invalid object name 'Orders'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":208,"lineNumber":2,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-01T16:09:51.643Z] INFO: ::1 - - [01/Jul/2025:16:09:51 +0000] "GET /api/orders HTTP/1.1" 500 95 "-" "curl/8.10.1"
[2025-07-01T16:10:37.321Z] ERROR: Error executing custom query: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":208,"state":1,"class":16,"message":"Invalid object name 'Orders'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":208,"lineNumber":2,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-01T16:10:37.322Z] ERROR: Error getting orders with filters: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":208,"state":1,"class":16,"message":"Invalid object name 'Orders'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":208,"lineNumber":2,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-01T16:10:37.322Z] ERROR: Get orders error: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":208,"state":1,"class":16,"message":"Invalid object name 'Orders'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":208,"lineNumber":2,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-01T16:10:37.323Z] INFO: ::1 - - [01/Jul/2025:16:10:37 +0000] "GET /api/orders HTTP/1.1" 500 95 "-" "curl/8.10.1"
[2025-07-01T16:11:19.725Z] ERROR: Error executing custom query: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":208,"state":1,"class":16,"message":"Invalid object name 'Orders'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":208,"lineNumber":2,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-01T16:11:19.726Z] ERROR: Error getting orders with filters: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":208,"state":1,"class":16,"message":"Invalid object name 'Orders'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":208,"lineNumber":2,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-01T16:11:19.726Z] ERROR: Get orders error: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":208,"state":1,"class":16,"message":"Invalid object name 'Orders'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":208,"lineNumber":2,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-01T16:11:19.727Z] INFO: ::1 - - [01/Jul/2025:16:11:19 +0000] "GET /api/orders HTTP/1.1" 500 95 "-" "curl/8.10.1"
[2025-07-01T16:11:48.126Z] INFO: Connecting to MSSQL database...
[2025-07-01T16:11:48.158Z] INFO: Database connected successfully
[2025-07-01T16:11:48.175Z] INFO: Database connection test successful
[2025-07-01T16:11:48.175Z] INFO: Database connected successfully
[2025-07-01T16:11:48.177Z] INFO: WebSocket service initialized
[2025-07-01T16:11:48.179Z] INFO: Server running on port 5000 in development mode
[2025-07-01T16:11:48.179Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-01T16:12:54.506Z] INFO: Order model database context: "master"
[2025-07-01T16:12:54.518Z] ERROR: Error executing custom query: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":208,"state":1,"class":16,"message":"Invalid object name 'Orders'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":208,"lineNumber":2,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-01T16:12:54.525Z] ERROR: Error getting orders with filters: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":208,"state":1,"class":16,"message":"Invalid object name 'Orders'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":208,"lineNumber":2,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-01T16:12:54.531Z] ERROR: Get orders error: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":208,"state":1,"class":16,"message":"Invalid object name 'Orders'.","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":208,"lineNumber":2,"state":1,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-01T16:12:54.553Z] INFO: ::1 - - [01/Jul/2025:16:12:54 +0000] "GET /api/orders HTTP/1.1" 500 95 "-" "curl/8.10.1"
[2025-07-01T16:13:49.751Z] INFO: Connecting to MSSQL database...
[2025-07-01T16:13:49.884Z] INFO: Database connected successfully
[2025-07-01T16:13:49.901Z] INFO: Database connection test successful
[2025-07-01T16:13:49.902Z] INFO: Database connected successfully
[2025-07-01T16:13:49.904Z] INFO: WebSocket service initialized
[2025-07-01T16:13:49.905Z] INFO: Server running on port 5000 in development mode
[2025-07-01T16:13:49.906Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-01T16:14:10.184Z] INFO: Order model database context: "OfficeEcommerce"
[2025-07-01T16:14:10.215Z] INFO: ::1 - - [01/Jul/2025:16:14:10 +0000] "GET /api/orders HTTP/1.1" 200 156 "-" "curl/8.10.1"
[2025-07-01T16:15:14.122Z] INFO: ::1 - - [01/Jul/2025:16:15:14 +0000] "GET /api/orders/recent HTTP/1.1" 400 53 "-" "curl/8.10.1"
[2025-07-01T16:15:18.889Z] INFO: ::1 - - [01/Jul/2025:16:15:18 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:15:22.081Z] INFO: ::1 - - [01/Jul/2025:16:15:22 +0000] "GET /api/orders/recent?limit=5 HTTP/1.1" 400 53 "-" "curl/8.10.1"
[2025-07-01T16:15:27.174Z] INFO: ::1 - - [01/Jul/2025:16:15:27 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:15:27.177Z] INFO: ::1 - - [01/Jul/2025:16:15:27 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:15:28.045Z] INFO: ::1 - - [01/Jul/2025:16:15:28 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:15:46.844Z] INFO: ::1 - - [01/Jul/2025:16:15:46 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:15:46.847Z] INFO: ::1 - - [01/Jul/2025:16:15:46 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:15:52.509Z] INFO: ::1 - - [01/Jul/2025:16:15:52 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:15:52.510Z] INFO: ::1 - - [01/Jul/2025:16:15:52 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:15:53.282Z] INFO: ::1 - - [01/Jul/2025:16:15:53 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:15:53.643Z] INFO: ::1 - - [01/Jul/2025:16:15:53 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:15:53.846Z] INFO: ::1 - - [01/Jul/2025:16:15:53 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:15:54.038Z] INFO: ::1 - - [01/Jul/2025:16:15:54 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:15:59.962Z] INFO: ::1 - - [01/Jul/2025:16:15:59 +0000] "GET /api/orders/analytics HTTP/1.1" 400 53 "-" "curl/8.10.1"
[2025-07-01T16:17:17.268Z] INFO: ::1 - - [01/Jul/2025:16:17:17 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:18:12.488Z] INFO: ::1 - - [01/Jul/2025:16:18:12 +0000] "GET /api/orders/analytics HTTP/1.1" 400 53 "-" "curl/8.10.1"
[2025-07-01T16:18:26.671Z] INFO: Connecting to MSSQL database...
[2025-07-01T16:18:26.744Z] INFO: Database connected successfully
[2025-07-01T16:18:26.762Z] INFO: Database connection test successful
[2025-07-01T16:18:26.763Z] INFO: Database connected successfully
[2025-07-01T16:18:26.766Z] INFO: WebSocket service initialized
[2025-07-01T16:18:26.769Z] INFO: Server running on port 5000 in development mode
[2025-07-01T16:18:26.769Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-01T16:18:47.225Z] INFO: ::1 - - [01/Jul/2025:16:18:47 +0000] "GET /api/orders/analytics HTTP/1.1" 200 388 "-" "curl/8.10.1"
[2025-07-01T16:18:56.158Z] INFO: ::1 - - [01/Jul/2025:16:18:56 +0000] "GET /api/orders HTTP/1.1" 200 156 "-" "curl/8.10.1"
[2025-07-01T16:20:45.930Z] INFO: ::1 - - [01/Jul/2025:16:20:45 +0000] "GET /orders?page=1&limit=10 HTTP/1.1" 404 45 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:21:20.926Z] INFO: ::1 - - [01/Jul/2025:16:21:20 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:21:39.931Z] INFO: ::1 - - [01/Jul/2025:16:21:39 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:21:52.934Z] INFO: ::1 - - [01/Jul/2025:16:21:52 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:22:06.925Z] INFO: ::1 - - [01/Jul/2025:16:22:06 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:22:25.424Z] INFO: ::1 - - [01/Jul/2025:16:22:25 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "-" "curl/8.10.1"
[2025-07-01T16:22:34.097Z] INFO: ::1 - - [01/Jul/2025:16:22:34 +0000] "GET /api/orders HTTP/1.1" 200 156 "-" "curl/8.10.1"
[2025-07-01T16:23:23.131Z] INFO: ::1 - - [01/Jul/2025:16:23:23 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:23:23.134Z] INFO: ::1 - - [01/Jul/2025:16:23:23 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:23:23.890Z] INFO: ::1 - - [01/Jul/2025:16:23:23 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:23:24.312Z] INFO: ::1 - - [01/Jul/2025:16:23:24 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:23:24.663Z] INFO: ::1 - - [01/Jul/2025:16:23:24 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:23:53.159Z] INFO: ::1 - - [01/Jul/2025:16:23:53 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:26:10.933Z] INFO: ::1 - - [01/Jul/2025:16:26:10 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:26:10.935Z] INFO: ::1 - - [01/Jul/2025:16:26:10 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:26:22.729Z] INFO: ::1 - - [01/Jul/2025:16:26:22 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-01T16:26:22.732Z] INFO: ::1 - - [01/Jul/2025:16:26:22 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:32:03.910Z] INFO: Connecting to MSSQL database...
[2025-07-02T02:32:04.002Z] INFO: Database connected successfully
[2025-07-02T02:32:04.038Z] INFO: Database connection test successful
[2025-07-02T02:32:04.040Z] INFO: Database connected successfully
[2025-07-02T02:32:04.044Z] INFO: WebSocket service initialized
[2025-07-02T02:32:04.047Z] INFO: Server running on port 5000 in development mode
[2025-07-02T02:32:04.048Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-02T02:32:55.466Z] INFO: ::1 - - [02/Jul/2025:02:32:55 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:32:55.470Z] INFO: ::1 - - [02/Jul/2025:02:32:55 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:33:07.974Z] INFO: ::1 - - [02/Jul/2025:02:33:07 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:33:07.978Z] INFO: ::1 - - [02/Jul/2025:02:33:07 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:34:02.201Z] INFO: ::1 - - [02/Jul/2025:02:34:02 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:34:02.204Z] INFO: ::1 - - [02/Jul/2025:02:34:02 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:34:19.697Z] INFO: ::1 - - [02/Jul/2025:02:34:19 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-02T02:34:27.687Z] INFO: ::1 - - [02/Jul/2025:02:34:27 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-02T02:34:27.750Z] INFO: ::1 - - [02/Jul/2025:02:34:27 +0000] "GET /api/orders HTTP/1.1" 200 156 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-02T02:34:43.550Z] INFO: ::1 - - [02/Jul/2025:02:34:43 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:34:43.552Z] INFO: ::1 - - [02/Jul/2025:02:34:43 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:36:57.218Z] INFO: ::1 - - [02/Jul/2025:02:36:57 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:36:57.282Z] INFO: User connected: 1 (Admin)
[2025-07-02T02:36:57.282Z] INFO: User 1 joined admin room
[2025-07-02T02:36:59.310Z] INFO: ::1 - - [02/Jul/2025:02:36:59 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 200 156 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:36:59.317Z] INFO: ::1 - - [02/Jul/2025:02:36:59 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:37:01.664Z] INFO: ::1 - - [02/Jul/2025:02:37:01 +0000] "GET /api/orders?page=1&limit=10&paymentStatus=Pending HTTP/1.1" 200 156 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:37:02.650Z] INFO: ::1 - - [02/Jul/2025:02:37:02 +0000] "GET /api/orders?page=1&limit=10&status=Pending&paymentStatus=Pending HTTP/1.1" 200 156 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:37:06.688Z] INFO: ::1 - - [02/Jul/2025:02:37:06 +0000] "GET /api/orders?page=1&limit=10&status=Pending&paymentStatus=Pending HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:37:08.243Z] INFO: ::1 - - [02/Jul/2025:02:37:08 +0000] "GET /api/orders?page=1&limit=10&status=Pending HTTP/1.1" 200 156 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:37:09.577Z] INFO: ::1 - - [02/Jul/2025:02:37:09 +0000] "GET /api/orders?page=1&limit=10&status=Pending&paymentStatus=Pending HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:37:15.914Z] INFO: ::1 - - [02/Jul/2025:02:37:15 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-02T02:37:15.919Z] INFO: ::1 - - [02/Jul/2025:02:37:15 +0000] "POST /api/orders HTTP/1.1" 400 639 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-02T02:37:22.487Z] INFO: User disconnected: 1
[2025-07-02T02:37:33.882Z] INFO: ::1 - - [02/Jul/2025:02:37:33 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-02T02:37:33.931Z] ERROR: Error creating order with inventory:
[2025-07-02T02:37:33.932Z] ERROR: Create order error:
[2025-07-02T02:37:33.933Z] INFO: ::1 - - [02/Jul/2025:02:37:33 +0000] "POST /api/orders HTTP/1.1" 400 161 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-02T02:37:58.189Z] INFO: ::1 - - [02/Jul/2025:02:37:58 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-02T02:37:58.200Z] INFO: ::1 - - [02/Jul/2025:02:37:58 +0000] "GET /api/products HTTP/1.1" 200 1317 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-02T02:38:37.006Z] INFO: User connected: 1 (Admin)
[2025-07-02T02:38:37.006Z] INFO: User 1 joined admin room
[2025-07-02T02:38:39.069Z] INFO: ::1 - - [02/Jul/2025:02:38:39 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:38:39.075Z] INFO: ::1 - - [02/Jul/2025:02:38:39 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:38:56.537Z] INFO: ::1 - - [02/Jul/2025:02:38:56 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-02T02:38:56.540Z] INFO: ::1 - - [02/Jul/2025:02:38:56 +0000] "GET /api/products HTTP/1.1" 200 1317 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-02T02:39:04.900Z] INFO: ::1 - - [02/Jul/2025:02:39:04 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-02T02:39:04.913Z] INFO: ::1 - - [02/Jul/2025:02:39:04 +0000] "GET /api/products HTTP/1.1" 200 1317 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-02T02:41:03.960Z] INFO: ::1 - - [02/Jul/2025:02:41:03 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-02T02:41:03.964Z] INFO: ::1 - - [02/Jul/2025:02:41:03 +0000] "GET /api/inventory HTTP/1.1" 200 1003 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-02T02:41:47.085Z] INFO: ::1 - - [02/Jul/2025:02:41:47 +0000] "POST /api/auth/login HTTP/1.1" 401 49 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:42:00.105Z] INFO: ::1 - - [02/Jul/2025:02:42:00 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:42:00.178Z] INFO: User connected: 1 (Admin)
[2025-07-02T02:42:00.179Z] INFO: User 1 joined admin room
[2025-07-02T02:42:02.083Z] INFO: ::1 - - [02/Jul/2025:02:42:02 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:42:02.090Z] INFO: ::1 - - [02/Jul/2025:02:42:02 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:42:02.863Z] INFO: ::1 - - [02/Jul/2025:02:42:02 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-02T02:42:02.871Z] INFO: ::1 - - [02/Jul/2025:02:42:02 +0000] "POST /api/orders HTTP/1.1" 400 176 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-02T02:42:04.287Z] INFO: ::1 - - [02/Jul/2025:02:42:04 +0000] "GET /api/orders?page=1&limit=10&paymentStatus=Pending HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:42:07.037Z] INFO: ::1 - - [02/Jul/2025:02:42:07 +0000] "GET /api/orders?page=1&limit=10&paymentStatus=Pending HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:43:00.301Z] INFO: ::1 - - [02/Jul/2025:02:43:00 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:43:00.306Z] INFO: ::1 - - [02/Jul/2025:02:43:00 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:43:03.604Z] INFO: ::1 - - [02/Jul/2025:02:43:03 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:43:03.609Z] INFO: ::1 - - [02/Jul/2025:02:43:03 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:43:31.755Z] INFO: User disconnected: 1
[2025-07-02T02:43:31.786Z] INFO: User connected: 1 (Admin)
[2025-07-02T02:43:31.786Z] INFO: User 1 joined admin room
[2025-07-02T02:43:33.309Z] INFO: User disconnected: 1
[2025-07-02T02:44:16.986Z] INFO: User connected: 1 (Admin)
[2025-07-02T02:44:16.987Z] INFO: User 1 joined admin room
[2025-07-02T02:44:53.231Z] INFO: User disconnected: 1
[2025-07-02T02:44:55.661Z] INFO: ::1 - - [02/Jul/2025:02:44:55 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:44:55.667Z] INFO: ::1 - - [02/Jul/2025:02:44:55 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:53:53.706Z] INFO: ::1 - - [02/Jul/2025:02:53:53 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:53:53.712Z] INFO: ::1 - - [02/Jul/2025:02:53:53 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:53:55.725Z] INFO: ::1 - - [02/Jul/2025:02:53:55 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:54:01.084Z] INFO: User disconnected: 1
[2025-07-02T02:54:01.109Z] INFO: User connected: 1 (Admin)
[2025-07-02T02:54:01.110Z] INFO: User 1 joined admin room
[2025-07-02T02:54:01.837Z] INFO: User disconnected: 1
[2025-07-02T02:54:03.339Z] INFO: User connected: 1 (Admin)
[2025-07-02T02:54:03.339Z] INFO: User 1 joined admin room
[2025-07-02T02:54:03.619Z] INFO: User disconnected: 1
[2025-07-02T02:58:32.932Z] INFO: User connected: 1 (Admin)
[2025-07-02T02:58:32.932Z] INFO: User 1 joined admin room
[2025-07-02T02:58:32.945Z] INFO: User connected: 1 (Admin)
[2025-07-02T02:58:32.946Z] INFO: User 1 joined admin room
[2025-07-02T02:58:48.933Z] INFO: User disconnected: 1
[2025-07-02T02:58:48.935Z] INFO: User disconnected: 1
[2025-07-02T02:58:48.963Z] INFO: User connected: 1 (Admin)
[2025-07-02T02:58:48.964Z] INFO: User 1 joined admin room
[2025-07-02T02:58:48.977Z] INFO: User connected: 1 (Admin)
[2025-07-02T02:58:48.978Z] INFO: User 1 joined admin room
[2025-07-02T02:59:08.926Z] INFO: User disconnected: 1
[2025-07-02T02:59:08.927Z] INFO: User disconnected: 1
[2025-07-02T02:59:08.936Z] INFO: User connected: 1 (Admin)
[2025-07-02T02:59:08.937Z] INFO: User 1 joined admin room
[2025-07-02T02:59:08.945Z] INFO: User connected: 1 (Admin)
[2025-07-02T02:59:08.946Z] INFO: User 1 joined admin room
[2025-07-02T02:59:38.968Z] INFO: User connected: 1 (Admin)
[2025-07-02T02:59:38.969Z] INFO: User 1 joined admin room
[2025-07-02T02:59:39.182Z] INFO: ::1 - - [02/Jul/2025:02:59:39 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
[2025-07-02T02:59:39.189Z] INFO: ::1 - - [02/Jul/2025:02:59:39 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
[2025-07-02T02:59:49.798Z] INFO: User connected: 1 (Admin)
[2025-07-02T02:59:49.801Z] INFO: User 1 joined admin room
[2025-07-02T02:59:49.822Z] INFO: ::1 - - [02/Jul/2025:02:59:49 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:59:49.839Z] INFO: ::1 - - [02/Jul/2025:02:59:49 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:59:58.742Z] INFO: ::1 - - [02/Jul/2025:02:59:58 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T02:59:58.749Z] INFO: ::1 - - [02/Jul/2025:02:59:58 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T03:00:19.556Z] INFO: User disconnected: 1
[2025-07-02T03:00:23.636Z] INFO: User disconnected: 1
[2025-07-02T03:00:23.676Z] INFO: User connected: 1 (Admin)
[2025-07-02T03:00:23.677Z] INFO: User 1 joined admin room
[2025-07-02T03:00:24.065Z] INFO: User disconnected: 1
[2025-07-02T03:00:28.766Z] INFO: ::1 - - [02/Jul/2025:03:00:28 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T03:00:28.771Z] INFO: ::1 - - [02/Jul/2025:03:00:28 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T03:00:39.019Z] INFO: ::1 - - [02/Jul/2025:03:00:39 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T03:00:39.026Z] INFO: ::1 - - [02/Jul/2025:03:00:39 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T03:00:53.579Z] INFO: User disconnected: 1
[2025-07-02T03:01:41.195Z] INFO: User disconnected: 1
[2025-07-02T03:01:55.810Z] INFO: ::1 - - [02/Jul/2025:03:01:55 +0000] "POST /api/auth/login HTTP/1.1" 200 543 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:17:48.281Z] INFO: Connecting to MSSQL database...
[2025-07-02T06:17:48.369Z] INFO: Database connected successfully
[2025-07-02T06:17:48.394Z] INFO: Database connection test successful
[2025-07-02T06:17:48.395Z] INFO: Database connected successfully
[2025-07-02T06:17:48.397Z] INFO: WebSocket service initialized
[2025-07-02T06:17:48.400Z] INFO: Server running on port 5000 in development mode
[2025-07-02T06:17:48.400Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-02T06:19:46.875Z] INFO: ::1 - - [02/Jul/2025:06:19:46 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:19:46.944Z] INFO: User connected: 1 (Admin)
[2025-07-02T06:19:46.945Z] INFO: User 1 joined admin room
[2025-07-02T06:19:51.208Z] INFO: User disconnected: 1
[2025-07-02T06:19:51.240Z] INFO: User connected: 1 (Admin)
[2025-07-02T06:19:51.241Z] INFO: User 1 joined admin room
[2025-07-02T06:19:58.244Z] INFO: User disconnected: 1
[2025-07-02T06:20:00.594Z] INFO: ::1 - - [02/Jul/2025:06:20:00 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:20:00.602Z] INFO: ::1 - - [02/Jul/2025:06:20:00 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:20:02.610Z] INFO: ::1 - - [02/Jul/2025:06:20:02 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:20:02.616Z] INFO: ::1 - - [02/Jul/2025:06:20:02 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:20:26.274Z] INFO: ::1 - - [02/Jul/2025:06:20:26 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:20:26.279Z] INFO: User connected: 1 (Admin)
[2025-07-02T06:20:26.280Z] INFO: User 1 joined admin room
[2025-07-02T06:20:26.287Z] INFO: ::1 - - [02/Jul/2025:06:20:26 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:20:32.038Z] INFO: ::1 - - [02/Jul/2025:06:20:32 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:20:32.045Z] INFO: ::1 - - [02/Jul/2025:06:20:32 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:20:35.949Z] INFO: ::1 - - [02/Jul/2025:06:20:35 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:20:45.174Z] INFO: User disconnected: 1
[2025-07-02T06:21:01.487Z] INFO: ::1 - - [02/Jul/2025:06:21:01 +0000] "POST /api/auth/login HTTP/1.1" 200 543 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:21:12.950Z] INFO: ::1 - - [02/Jul/2025:06:21:12 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:21:12.952Z] INFO: ::1 - - [02/Jul/2025:06:21:12 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:21:19.369Z] INFO: ::1 - - [02/Jul/2025:06:21:19 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:21:20.218Z] INFO: ::1 - - [02/Jul/2025:06:21:20 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:21:20.642Z] INFO: ::1 - - [02/Jul/2025:06:21:20 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:21:23.597Z] INFO: ::1 - - [02/Jul/2025:06:21:23 +0000] "POST /api/payments/create-link HTTP/1.1" 201 961 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:21:39.215Z] INFO: ::1 - - [02/Jul/2025:06:21:39 +0000] "GET /api/payments/status/ORDER_1751437283088/link_uo5dZQWRanKxmNZAEMutYkbK HTTP/1.1" 200 272 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:21:53.971Z] INFO: ::1 - - [02/Jul/2025:06:21:53 +0000] "GET /api/payments/status/ORDER_1751437283088/link_uo5dZQWRanKxmNZAEMutYkbK HTTP/1.1" 200 270 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:22:19.716Z] INFO: ::1 - - [02/Jul/2025:06:22:19 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:22:19.748Z] INFO: User connected: 1 (Admin)
[2025-07-02T06:22:19.749Z] INFO: User 1 joined admin room
[2025-07-02T06:22:21.914Z] INFO: ::1 - - [02/Jul/2025:06:22:21 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:22:21.920Z] INFO: ::1 - - [02/Jul/2025:06:22:21 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:22:24.814Z] INFO: ::1 - - [02/Jul/2025:06:22:24 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:22:25.261Z] INFO: ::1 - - [02/Jul/2025:06:22:25 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:22:25.521Z] INFO: ::1 - - [02/Jul/2025:06:22:25 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:32:28.202Z] INFO: User disconnected: 1
[2025-07-02T06:32:37.025Z] INFO: ::1 - - [02/Jul/2025:06:32:37 +0000] "POST /api/auth/login HTTP/1.1" 200 543 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:32:43.071Z] INFO: ::1 - - [02/Jul/2025:06:32:43 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:32:43.073Z] INFO: ::1 - - [02/Jul/2025:06:32:43 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:32:50.235Z] INFO: ::1 - - [02/Jul/2025:06:32:50 +0000] "POST /api/payments/create-link HTTP/1.1" 201 960 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:33:06.127Z] INFO: ::1 - - [02/Jul/2025:06:33:06 +0000] "GET /api/payments/status/ORDER_1751437969753/link_PCU1AwDCJ2TcoCDj5apg8fX6 HTTP/1.1" 200 272 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:33:20.952Z] INFO: ::1 - - [02/Jul/2025:06:33:20 +0000] "GET /api/payments/status/ORDER_1751437969753/link_PCU1AwDCJ2TcoCDj5apg8fX6 HTTP/1.1" 200 270 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:33:43.554Z] INFO: ::1 - - [02/Jul/2025:06:33:43 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:33:43.582Z] INFO: User connected: 1 (Admin)
[2025-07-02T06:33:43.583Z] INFO: User 1 joined admin room
[2025-07-02T06:33:45.485Z] INFO: ::1 - - [02/Jul/2025:06:33:45 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:33:45.491Z] INFO: ::1 - - [02/Jul/2025:06:33:45 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:33:49.195Z] INFO: ::1 - - [02/Jul/2025:06:33:49 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:33:49.837Z] INFO: ::1 - - [02/Jul/2025:06:33:49 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:33:50.241Z] INFO: ::1 - - [02/Jul/2025:06:33:50 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:35:05.775Z] INFO: ::1 - - [02/Jul/2025:06:35:05 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:35:05.781Z] INFO: ::1 - - [02/Jul/2025:06:35:05 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:35:18.079Z] INFO: ::1 - - [02/Jul/2025:06:35:18 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
[2025-07-02T06:35:30.089Z] INFO: ::1 - - [02/Jul/2025:06:35:30 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:35:30.096Z] INFO: ::1 - - [02/Jul/2025:06:35:30 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:35:42.294Z] INFO: User disconnected: 1
[2025-07-02T06:35:56.330Z] INFO: ::1 - - [02/Jul/2025:06:35:56 +0000] "POST /api/auth/login HTTP/1.1" 401 49 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:36:07.913Z] INFO: ::1 - - [02/Jul/2025:06:36:07 +0000] "POST /api/auth/login HTTP/1.1" 200 543 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:45:19.323Z] INFO: Connecting to MSSQL database...
[2025-07-02T06:45:19.392Z] INFO: Database connected successfully
[2025-07-02T06:45:19.409Z] INFO: Database connection test successful
[2025-07-02T06:45:19.409Z] INFO: Database connected successfully
[2025-07-02T06:45:19.411Z] INFO: WebSocket service initialized
[2025-07-02T06:48:13.408Z] INFO: ::1 - - [02/Jul/2025:06:48:13 +0000] "POST /api/auth/login HTTP/1.1" 401 49 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:48:17.426Z] INFO: ::1 - - [02/Jul/2025:06:48:17 +0000] "POST /api/auth/login HTTP/1.1" 200 543 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:48:26.778Z] INFO: ::1 - - [02/Jul/2025:06:48:26 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:48:26.780Z] INFO: ::1 - - [02/Jul/2025:06:48:26 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:48:39.480Z] INFO: ::1 - - [02/Jul/2025:06:48:39 +0000] "POST /api/orders HTTP/1.1" 400 184 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:48:47.249Z] INFO: ::1 - - [02/Jul/2025:06:48:47 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:48:48.418Z] INFO: ::1 - - [02/Jul/2025:06:48:48 +0000] "POST /api/orders HTTP/1.1" 400 184 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:50:00.935Z] INFO: ::1 - - [02/Jul/2025:06:50:00 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:50:13.943Z] INFO: ::1 - - [02/Jul/2025:06:50:13 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:50:46.936Z] INFO: ::1 - - [02/Jul/2025:06:50:46 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:51:08.712Z] INFO: ::1 - - [02/Jul/2025:06:51:08 +0000] "POST /api/orders HTTP/1.1" 400 168 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:55:24.424Z] INFO: Connecting to MSSQL database...
[2025-07-02T06:55:24.505Z] INFO: Database connected successfully
[2025-07-02T06:55:24.527Z] INFO: Database connection test successful
[2025-07-02T06:55:24.527Z] INFO: Database connected successfully
[2025-07-02T06:55:24.530Z] INFO: WebSocket service initialized
[2025-07-02T06:56:56.921Z] INFO: ::1 - - [02/Jul/2025:06:56:56 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:57:23.564Z] INFO: ::1 - - [02/Jul/2025:06:57:23 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:57:23.568Z] INFO: ::1 - - [02/Jul/2025:06:57:23 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:57:33.025Z] INFO: ::1 - - [02/Jul/2025:06:57:33 +0000] "POST /api/orders HTTP/1.1" 400 168 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:57:34.431Z] INFO: ::1 - - [02/Jul/2025:06:57:34 +0000] "POST /api/orders HTTP/1.1" 400 168 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:57:35.150Z] INFO: ::1 - - [02/Jul/2025:06:57:35 +0000] "POST /api/orders HTTP/1.1" 400 168 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:57:39.264Z] INFO: ::1 - - [02/Jul/2025:06:57:39 +0000] "POST /api/orders HTTP/1.1" 400 168 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:57:39.605Z] INFO: ::1 - - [02/Jul/2025:06:57:39 +0000] "POST /api/orders HTTP/1.1" 400 168 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T06:58:07.947Z] INFO: ::1 - - [02/Jul/2025:06:58:07 +0000] "POST /api/orders HTTP/1.1" 400 168 "http://localhost:3000/" "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
[2025-07-02T06:58:36.945Z] INFO: ::1 - - [02/Jul/2025:06:58:36 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:00:32.831Z] INFO: Connecting to MSSQL database...
[2025-07-02T07:00:32.904Z] INFO: Database connected successfully
[2025-07-02T07:00:32.926Z] INFO: Database connection test successful
[2025-07-02T07:00:32.927Z] INFO: Database connected successfully
[2025-07-02T07:00:32.929Z] INFO: WebSocket service initialized
[2025-07-02T07:00:32.931Z] INFO: Server running on port 5000 in development mode
[2025-07-02T07:00:32.932Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-02T07:02:31.666Z] INFO: Connecting to MSSQL database...
[2025-07-02T07:02:31.738Z] INFO: Database connected successfully
[2025-07-02T07:02:31.759Z] INFO: Database connection test successful
[2025-07-02T07:02:31.760Z] INFO: Database connected successfully
[2025-07-02T07:02:31.763Z] INFO: WebSocket service initialized
[2025-07-02T07:02:31.765Z] INFO: Server running on port 5000 in development mode
[2025-07-02T07:02:31.765Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-02T07:04:13.936Z] ERROR: Error executing custom query: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:04:13.936Z] ERROR: Error getting inventory status: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:04:13.937Z] ERROR: Error checking inventory availability: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:04:13.939Z] ERROR: Error creating order with inventory: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:04:13.940Z] ERROR: Create order error: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:04:13.944Z] INFO: ::1 - - [02/Jul/2025:07:04:13 +0000] "POST /api/orders HTTP/1.1" 500 124 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:04:15.035Z] ERROR: Error executing custom query: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:04:15.036Z] ERROR: Error getting inventory status: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:04:15.036Z] ERROR: Error checking inventory availability: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:04:15.038Z] ERROR: Error creating order with inventory: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:04:15.039Z] ERROR: Create order error: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:04:15.040Z] INFO: ::1 - - [02/Jul/2025:07:04:15 +0000] "POST /api/orders HTTP/1.1" 500 124 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:05:42.856Z] INFO: ::1 - - [02/Jul/2025:07:05:42 +0000] "POST /api/auth/login HTTP/1.1" 200 543 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:05:58.698Z] INFO: ::1 - - [02/Jul/2025:07:05:58 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:05:58.703Z] INFO: ::1 - - [02/Jul/2025:07:05:58 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:06:04.483Z] INFO: ::1 - - [02/Jul/2025:07:06:04 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 119 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:06:06.952Z] ERROR: Error executing custom query: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:06:06.953Z] ERROR: Error getting inventory status: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:06:06.954Z] ERROR: Error checking inventory availability: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:06:06.955Z] ERROR: Error creating order with inventory: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:06:06.955Z] ERROR: Create order error: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:06:06.957Z] INFO: ::1 - - [02/Jul/2025:07:06:06 +0000] "POST /api/orders HTTP/1.1" 500 124 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:09:34.693Z] INFO: Connecting to MSSQL database...
[2025-07-02T07:09:34.771Z] INFO: Database connected successfully
[2025-07-02T07:09:34.790Z] INFO: Database connection test successful
[2025-07-02T07:09:34.791Z] INFO: Database connected successfully
[2025-07-02T07:09:34.793Z] INFO: WebSocket service initialized
[2025-07-02T07:09:34.795Z] INFO: Server running on port 5000 in development mode
[2025-07-02T07:09:34.795Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-02T07:10:29.204Z] ERROR: Error executing custom query: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:10:29.204Z] ERROR: Error getting inventory status: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:10:29.205Z] ERROR: Error checking inventory availability: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:10:29.207Z] ERROR: Error creating order with inventory: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:10:29.207Z] ERROR: Create order error: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:10:29.210Z] INFO: ::1 - - [02/Jul/2025:07:10:29 +0000] "POST /api/orders HTTP/1.1" 500 124 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:10:38.914Z] INFO: ::1 - - [02/Jul/2025:07:10:38 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:10:38.935Z] INFO: ::1 - - [02/Jul/2025:07:10:38 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:10:43.667Z] ERROR: Error executing custom query: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:10:43.668Z] ERROR: Error getting inventory status: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:10:43.669Z] ERROR: Error checking inventory availability: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:10:43.670Z] ERROR: Error creating order with inventory: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:10:43.671Z] ERROR: Create order error: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:10:43.672Z] INFO: ::1 - - [02/Jul/2025:07:10:43 +0000] "POST /api/orders HTTP/1.1" 500 124 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:10:45.245Z] ERROR: Error executing custom query: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:10:45.246Z] ERROR: Error getting inventory status: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:10:45.246Z] ERROR: Error checking inventory availability: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:10:45.249Z] ERROR: Error creating order with inventory: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:10:45.249Z] ERROR: Create order error: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":206,"state":2,"class":16,"message":"Operand type clash: uniqueidentifier is incompatible with int","serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","lineNumber":2}},"name":"RequestError","number":206,"lineNumber":2,"state":2,"class":16,"serverName":"DESKTOP-F4OI6BT\\SQLEXPRESS","procName":"","precedingErrors":[]}
[2025-07-02T07:10:45.251Z] INFO: ::1 - - [02/Jul/2025:07:10:45 +0000] "POST /api/orders HTTP/1.1" 500 124 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:11:42.922Z] INFO: ::1 - - [02/Jul/2025:07:11:42 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:12:25.636Z] INFO: ::1 - - [02/Jul/2025:07:12:25 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:12:25.638Z] INFO: ::1 - - [02/Jul/2025:07:12:25 +0000] "POST /api/payments/calculate-fees HTTP/1.1" 200 120 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:12:29.882Z] INFO: ::1 - - [02/Jul/2025:07:12:29 +0000] "POST /api/payments/create-link HTTP/1.1" 201 960 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:12:45.140Z] INFO: ::1 - - [02/Jul/2025:07:12:45 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 200 272 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:13:00.162Z] INFO: ::1 - - [02/Jul/2025:07:13:00 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:13:15.115Z] INFO: ::1 - - [02/Jul/2025:07:13:15 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:13:30.246Z] INFO: ::1 - - [02/Jul/2025:07:13:30 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:13:45.188Z] INFO: ::1 - - [02/Jul/2025:07:13:45 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:14:00.134Z] INFO: ::1 - - [02/Jul/2025:07:14:00 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:14:15.157Z] INFO: ::1 - - [02/Jul/2025:07:14:15 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:14:30.192Z] INFO: ::1 - - [02/Jul/2025:07:14:30 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:14:45.192Z] INFO: ::1 - - [02/Jul/2025:07:14:45 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:15:00.135Z] INFO: ::1 - - [02/Jul/2025:07:15:00 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:15:52.071Z] INFO: ::1 - - [02/Jul/2025:07:15:52 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:16:00.116Z] INFO: ::1 - - [02/Jul/2025:07:16:00 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:16:15.215Z] INFO: ::1 - - [02/Jul/2025:07:16:15 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:16:30.138Z] INFO: ::1 - - [02/Jul/2025:07:16:30 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:16:45.203Z] INFO: ::1 - - [02/Jul/2025:07:16:45 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:17:14.290Z] INFO: ::1 - - [02/Jul/2025:07:17:14 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:18:14.360Z] INFO: ::1 - - [02/Jul/2025:07:18:14 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:18:15.088Z] INFO: ::1 - - [02/Jul/2025:07:18:15 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:18:30.201Z] INFO: ::1 - - [02/Jul/2025:07:18:30 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:18:45.174Z] INFO: ::1 - - [02/Jul/2025:07:18:45 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:19:00.243Z] INFO: ::1 - - [02/Jul/2025:07:19:00 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:19:15.249Z] INFO: ::1 - - [02/Jul/2025:07:19:15 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:20:07.331Z] INFO: ::1 - - [02/Jul/2025:07:20:07 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:20:15.309Z] INFO: ::1 - - [02/Jul/2025:07:20:15 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:20:30.118Z] INFO: ::1 - - [02/Jul/2025:07:20:30 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:20:45.137Z] INFO: ::1 - - [02/Jul/2025:07:20:45 +0000] "GET /api/payments/status/ORDER_1751440349462/link_wLeZtxNQ4YTSXrnAevMCLLNK HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:48:08.214Z] INFO: Connecting to MSSQL database...
[2025-07-02T07:48:08.283Z] INFO: Database connected successfully
[2025-07-02T07:48:08.299Z] INFO: Database connection test successful
[2025-07-02T07:48:08.299Z] INFO: Database connected successfully
[2025-07-02T07:48:08.303Z] INFO: WebSocket service initialized
[2025-07-02T07:48:08.305Z] INFO: Server running on port 5000 in development mode
[2025-07-02T07:48:08.305Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-02T07:51:16.188Z] INFO: ::1 - - [02/Jul/2025:07:51:16 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:51:16.244Z] INFO: User connected: 1 (Admin)
[2025-07-02T07:51:16.244Z] INFO: User 1 joined admin room
[2025-07-02T07:51:18.613Z] INFO: ::1 - - [02/Jul/2025:07:51:18 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:51:18.621Z] INFO: ::1 - - [02/Jul/2025:07:51:18 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T07:51:23.710Z] INFO: User disconnected: 1
[2025-07-02T08:50:07.290Z] INFO: Connecting to MSSQL database...
[2025-07-02T08:50:07.359Z] INFO: Database connected successfully
[2025-07-02T08:50:07.383Z] INFO: Database connection test successful
[2025-07-02T08:50:07.383Z] INFO: Database connected successfully
[2025-07-02T08:50:07.386Z] INFO: WebSocket service initialized
[2025-07-02T08:50:07.389Z] INFO: Server running on port 5000 in development mode
[2025-07-02T08:50:07.389Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-02T09:21:54.642Z] INFO: Connecting to MSSQL database...
[2025-07-02T09:21:54.753Z] INFO: Database connected successfully
[2025-07-02T09:21:54.772Z] INFO: Database connection test successful
[2025-07-02T09:21:54.773Z] INFO: Database connected successfully
[2025-07-02T09:21:54.775Z] INFO: WebSocket service initialized
[2025-07-02T09:21:54.777Z] INFO: Server running on port 5000 in development mode
[2025-07-02T09:21:54.777Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-02T10:27:20.608Z] INFO: ::1 - - [02/Jul/2025:10:27:20 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T10:27:20.651Z] INFO: User connected: 1 (Admin)
[2025-07-02T10:27:20.652Z] INFO: User 1 joined admin room
[2025-07-02T10:27:46.779Z] INFO: User disconnected: 1
[2025-07-02T10:27:46.805Z] INFO: User connected: 1 (Admin)
[2025-07-02T10:27:46.805Z] INFO: User 1 joined admin room
[2025-07-02T10:27:47.431Z] INFO: User disconnected: 1
[2025-07-02T10:27:49.649Z] INFO: ::1 - - [02/Jul/2025:10:27:49 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T10:27:49.656Z] INFO: ::1 - - [02/Jul/2025:10:27:49 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T10:27:50.753Z] INFO: User connected: 1 (Admin)
[2025-07-02T10:27:50.753Z] INFO: User 1 joined admin room
[2025-07-02T10:27:56.024Z] INFO: User disconnected: 1
[2025-07-02T10:27:56.822Z] INFO: User connected: 1 (Admin)
[2025-07-02T10:27:56.823Z] INFO: User 1 joined admin room
[2025-07-02T10:33:25.897Z] INFO: User disconnected: 1
[2025-07-02T10:33:26.504Z] INFO: ::1 - - [02/Jul/2025:10:33:26 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T10:33:26.511Z] INFO: ::1 - - [02/Jul/2025:10:33:26 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T10:37:15.697Z] INFO: User connected: 1 (Admin)
[2025-07-02T10:37:15.698Z] INFO: User 1 joined admin room
[2025-07-02T10:37:17.515Z] INFO: User disconnected: 1
[2025-07-02T10:43:11.988Z] INFO: User connected: 1 (Admin)
[2025-07-02T10:43:11.989Z] INFO: User 1 joined admin room
[2025-07-02T10:44:58.925Z] INFO: User disconnected: 1
[2025-07-02T10:44:58.947Z] INFO: User connected: 1 (Admin)
[2025-07-02T10:44:58.948Z] INFO: User 1 joined admin room
[2025-07-02T10:45:11.918Z] INFO: User disconnected: 1
[2025-07-02T10:45:11.934Z] INFO: User connected: 1 (Admin)
[2025-07-02T10:45:11.935Z] INFO: User 1 joined admin room
[2025-07-02T10:45:23.854Z] INFO: User disconnected: 1
[2025-07-02T10:45:23.876Z] INFO: User connected: 1 (Admin)
[2025-07-02T10:45:23.877Z] INFO: User 1 joined admin room
[2025-07-02T10:45:23.878Z] INFO: User 1 subscribed to inventory updates
[2025-07-02T10:45:23.879Z] INFO: User 1 subscribed to order updates
[2025-07-02T10:45:23.879Z] INFO: User 1 subscribed to dashboard updates
[2025-07-02T10:52:16.424Z] INFO: User disconnected: 1
[2025-07-02T10:52:16.899Z] INFO: User connected: 1 (Admin)
[2025-07-02T10:52:16.900Z] INFO: User 1 joined admin room
[2025-07-02T10:52:16.903Z] INFO: User 1 subscribed to inventory updates
[2025-07-02T10:52:16.904Z] INFO: User 1 subscribed to order updates
[2025-07-02T10:52:16.904Z] INFO: User 1 subscribed to dashboard updates
[2025-07-02T10:52:26.336Z] INFO: User disconnected: 1
[2025-07-02T10:52:34.001Z] INFO: ::1 - - [02/Jul/2025:10:52:34 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T10:52:34.008Z] INFO: ::1 - - [02/Jul/2025:10:52:34 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T10:53:18.153Z] INFO: User connected: 1 (Admin)
[2025-07-02T10:53:18.154Z] INFO: User 1 joined admin room
[2025-07-02T10:53:18.162Z] INFO: User 1 subscribed to inventory updates
[2025-07-02T10:53:18.163Z] INFO: User 1 subscribed to order updates
[2025-07-02T10:53:18.165Z] INFO: User 1 subscribed to dashboard updates
[2025-07-02T10:53:23.832Z] INFO: User disconnected: 1
[2025-07-02T10:53:23.855Z] INFO: User connected: 1 (Admin)
[2025-07-02T10:53:23.856Z] INFO: User 1 joined admin room
[2025-07-02T10:53:23.859Z] INFO: User 1 subscribed to inventory updates
[2025-07-02T10:53:23.860Z] INFO: User 1 subscribed to order updates
[2025-07-02T10:53:23.861Z] INFO: User 1 subscribed to dashboard updates
[2025-07-02T10:53:24.759Z] INFO: User disconnected: 1
[2025-07-02T10:53:24.843Z] INFO: ::1 - - [02/Jul/2025:10:53:24 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T10:53:24.850Z] INFO: ::1 - - [02/Jul/2025:10:53:24 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T10:53:41.929Z] INFO: User connected: 1 (Admin)
[2025-07-02T10:53:41.929Z] INFO: User 1 joined admin room
[2025-07-02T10:54:43.197Z] INFO: User disconnected: 1
[2025-07-02T10:54:43.227Z] INFO: User connected: 1 (Admin)
[2025-07-02T10:54:43.228Z] INFO: User 1 joined admin room
[2025-07-02T10:54:45.948Z] INFO: User disconnected: 1
[2025-07-02T11:36:07.259Z] INFO: Connecting to MSSQL database...
[2025-07-02T11:36:07.434Z] INFO: Database connected successfully
[2025-07-02T11:36:07.461Z] INFO: Database connection test successful
[2025-07-02T11:36:07.462Z] INFO: Database connected successfully
[2025-07-02T11:36:07.465Z] INFO: WebSocket service initialized
[2025-07-02T11:36:07.467Z] INFO: Server running on port 5000 in development mode
[2025-07-02T11:36:07.467Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-02T11:48:20.240Z] INFO: Connecting to MSSQL database...
[2025-07-02T11:48:20.354Z] INFO: Database connected successfully
[2025-07-02T11:48:20.383Z] INFO: Database connection test successful
[2025-07-02T11:48:20.383Z] INFO: Database connected successfully
[2025-07-02T11:48:20.385Z] INFO: WebSocket service initialized
[2025-07-02T11:48:20.388Z] INFO: Server running on port 5000 in development mode
[2025-07-02T11:48:20.389Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-02T11:50:13.758Z] INFO: ::1 - - [02/Jul/2025:11:50:13 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-02T11:50:33.494Z] INFO: ::1 - - [02/Jul/2025:11:50:33 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T11:50:33.542Z] INFO: User connected: 1 (Admin)
[2025-07-02T11:50:33.543Z] INFO: User 1 joined admin room
[2025-07-02T11:50:35.806Z] INFO: User disconnected: 1
[2025-07-02T11:50:35.839Z] INFO: User connected: 1 (Admin)
[2025-07-02T11:50:35.840Z] INFO: User 1 joined admin room
[2025-07-02T11:50:46.983Z] INFO: User disconnected: 1
[2025-07-02T11:50:51.995Z] INFO: User connected: 1 (Admin)
[2025-07-02T11:50:51.996Z] INFO: User 1 joined admin room
[2025-07-02T11:51:58.327Z] INFO: ::1 - - [02/Jul/2025:11:51:58 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T11:51:58.347Z] INFO: ::1 - - [02/Jul/2025:11:51:58 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T11:53:39.219Z] INFO: User disconnected: 1
[2025-07-02T11:58:01.687Z] INFO: User connected: 1 (Admin)
[2025-07-02T11:58:01.688Z] INFO: User 1 joined admin room
[2025-07-02T11:58:04.244Z] INFO: ::1 - - [02/Jul/2025:11:58:04 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T11:58:04.251Z] INFO: ::1 - - [02/Jul/2025:11:58:04 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T11:58:05.387Z] INFO: User disconnected: 1
[2025-07-02T11:58:05.429Z] INFO: User connected: 1 (Admin)
[2025-07-02T11:58:05.430Z] INFO: User 1 joined admin room
[2025-07-02T11:58:05.971Z] INFO: User disconnected: 1
[2025-07-02T12:01:06.116Z] INFO: ::1 - - [02/Jul/2025:12:01:06 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:01:06.125Z] INFO: ::1 - - [02/Jul/2025:12:01:06 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:01:18.341Z] INFO: ::1 - - [02/Jul/2025:12:01:18 +0000] "POST /api/auth/login HTTP/1.1" 401 49 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:01:22.127Z] INFO: ::1 - - [02/Jul/2025:12:01:22 +0000] "POST /api/auth/login HTTP/1.1" 200 543 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:01:27.388Z] INFO: ::1 - - [02/Jul/2025:12:01:27 +0000] "POST /api/auth/login HTTP/1.1" 200 543 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:01:44.494Z] INFO: ::1 - - [02/Jul/2025:12:01:44 +0000] "POST /api/auth/login HTTP/1.1" 200 543 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:01:52.713Z] INFO: ::1 - - [02/Jul/2025:12:01:52 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:02:15.520Z] INFO: ::1 - - [02/Jul/2025:12:02:15 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:05:08.746Z] INFO: ::1 - - [02/Jul/2025:12:05:08 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:05:08.823Z] INFO: User connected: 1 (Admin)
[2025-07-02T12:05:08.824Z] INFO: User 1 joined admin room
[2025-07-02T12:05:13.862Z] INFO: User disconnected: 1
[2025-07-02T12:05:13.895Z] INFO: User connected: 1 (Admin)
[2025-07-02T12:05:13.895Z] INFO: User 1 joined admin room
[2025-07-02T12:05:14.838Z] INFO: User disconnected: 1
[2025-07-02T12:09:55.325Z] INFO: User connected: 1 (Admin)
[2025-07-02T12:09:55.327Z] INFO: User 1 joined admin room
[2025-07-02T12:09:55.592Z] INFO: User disconnected: 1
[2025-07-02T12:09:55.616Z] INFO: User connected: 1 (Admin)
[2025-07-02T12:09:55.617Z] INFO: User 1 joined admin room
[2025-07-02T12:10:17.583Z] INFO: User disconnected: 1
[2025-07-02T12:10:31.497Z] INFO: User connected: 1 (Admin)
[2025-07-02T12:10:31.498Z] INFO: User 1 joined admin room
[2025-07-02T12:10:31.923Z] INFO: User disconnected: 1
[2025-07-02T12:10:31.943Z] INFO: User connected: 1 (Admin)
[2025-07-02T12:10:31.944Z] INFO: User 1 joined admin room
[2025-07-02T12:12:14.273Z] INFO: User disconnected: 1
[2025-07-02T12:13:12.261Z] INFO: ::1 - - [02/Jul/2025:12:13:12 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:13:12.299Z] INFO: User connected: 1 (Admin)
[2025-07-02T12:13:12.299Z] INFO: User 1 joined admin room
[2025-07-02T12:13:18.657Z] INFO: ::1 - - [02/Jul/2025:12:13:18 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:13:18.663Z] INFO: ::1 - - [02/Jul/2025:12:13:18 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:14:49.733Z] INFO: User disconnected: 1
[2025-07-02T12:14:55.908Z] INFO: User connected: 1 (Admin)
[2025-07-02T12:14:55.909Z] INFO: User 1 joined admin room
[2025-07-02T12:21:13.451Z] INFO: User disconnected: 1
[2025-07-02T12:22:10.242Z] INFO: ::1 - - [02/Jul/2025:12:22:10 +0000] "POST /api/auth/login HTTP/1.1" 400 149 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:22:15.383Z] INFO: ::1 - - [02/Jul/2025:12:22:15 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:22:15.417Z] INFO: User connected: 1 (Admin)
[2025-07-02T12:22:15.418Z] INFO: User 1 joined admin room
[2025-07-02T12:26:44.531Z] INFO: User disconnected: 1
[2025-07-02T12:26:44.562Z] INFO: User connected: 1 (Admin)
[2025-07-02T12:26:44.563Z] INFO: User 1 joined admin room
[2025-07-02T12:26:45.025Z] INFO: User disconnected: 1
[2025-07-02T12:26:45.044Z] INFO: ::1 - - [02/Jul/2025:12:26:45 +0000] "GET /api/products?page=1&limit=20&sortBy=ProductName&sortDirection=ASC HTTP/1.1" 200 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:26:45.046Z] INFO: ::1 - - [02/Jul/2025:12:26:45 +0000] "GET /api/products?page=1&limit=20&sortBy=ProductName&sortDirection=ASC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:30:55.137Z] INFO: Connecting to MSSQL database...
[2025-07-02T12:30:55.221Z] INFO: Database connected successfully
[2025-07-02T12:30:55.251Z] INFO: Database connection test successful
[2025-07-02T12:30:55.252Z] INFO: Database connected successfully
[2025-07-02T12:30:55.254Z] INFO: WebSocket service initialized
[2025-07-02T12:31:16.313Z] INFO: Connecting to MSSQL database...
[2025-07-02T12:31:16.382Z] INFO: Database connected successfully
[2025-07-02T12:31:16.401Z] INFO: Database connection test successful
[2025-07-02T12:31:16.402Z] INFO: Database connected successfully
[2025-07-02T12:31:16.405Z] INFO: WebSocket service initialized
[2025-07-02T12:31:16.407Z] INFO: Server running on port 5000 in development mode
[2025-07-02T12:31:16.408Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-02T12:32:00.946Z] INFO: ::1 - - [02/Jul/2025:12:32:00 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:32:01.008Z] INFO: User connected: 1 (Admin)
[2025-07-02T12:32:01.009Z] INFO: User 1 joined admin room
[2025-07-02T12:32:02.790Z] INFO: ::1 - - [02/Jul/2025:12:32:02 +0000] "GET /api/products/categories HTTP/1.1" 404 47 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:32:02.797Z] INFO: ::1 - - [02/Jul/2025:12:32:02 +0000] "GET /api/products/categories HTTP/1.1" 404 47 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:32:02.800Z] INFO: ::1 - - [02/Jul/2025:12:32:02 +0000] "GET /api/products?page=1&limit=20&sortBy=ProductName&sortDirection=ASC HTTP/1.1" 200 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:32:02.804Z] INFO: ::1 - - [02/Jul/2025:12:32:02 +0000] "GET /api/products?page=1&limit=20&sortBy=ProductName&sortDirection=ASC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:32:02.832Z] INFO: User disconnected: 1
[2025-07-02T12:33:31.394Z] INFO: Connecting to MSSQL database...
[2025-07-02T12:33:31.493Z] INFO: Database connected successfully
[2025-07-02T12:33:31.512Z] INFO: Database connection test successful
[2025-07-02T12:33:31.512Z] INFO: Database connected successfully
[2025-07-02T12:33:31.515Z] INFO: WebSocket service initialized
[2025-07-02T12:33:31.516Z] INFO: Server running on port 5000 in development mode
[2025-07-02T12:33:31.517Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-02T12:33:46.069Z] INFO: ::1 - - [02/Jul/2025:12:33:46 +0000] "GET /api/products/categories HTTP/1.1" 200 51 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-02T12:34:35.953Z] INFO: ::1 - - [02/Jul/2025:12:34:35 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:34:36.009Z] INFO: User connected: 1 (Admin)
[2025-07-02T12:34:36.016Z] INFO: User 1 joined admin room
[2025-07-02T12:34:41.744Z] INFO: ::1 - - [02/Jul/2025:12:34:41 +0000] "GET /api/products/categories HTTP/1.1" 200 51 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:34:41.746Z] INFO: ::1 - - [02/Jul/2025:12:34:41 +0000] "GET /api/products/categories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:34:41.748Z] INFO: ::1 - - [02/Jul/2025:12:34:41 +0000] "GET /api/products?page=1&limit=20&sortBy=ProductName&sortDirection=ASC HTTP/1.1" 200 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:34:41.751Z] INFO: ::1 - - [02/Jul/2025:12:34:41 +0000] "GET /api/products?page=1&limit=20&sortBy=ProductName&sortDirection=ASC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:34:41.769Z] INFO: User disconnected: 1
[2025-07-02T12:35:23.946Z] INFO: ::1 - - [02/Jul/2025:12:35:23 +0000] "GET /api/products?page=1&limit=20&sortBy=ProductName&sortDirection=ASC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:35:23.948Z] INFO: ::1 - - [02/Jul/2025:12:35:23 +0000] "GET /api/products?page=1&limit=20&sortBy=ProductName&sortDirection=ASC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:35:23.950Z] INFO: ::1 - - [02/Jul/2025:12:35:23 +0000] "GET /api/products/categories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:35:23.952Z] INFO: User connected: 1 (Admin)
[2025-07-02T12:35:23.953Z] INFO: User 1 joined admin room
[2025-07-02T12:35:23.955Z] INFO: ::1 - - [02/Jul/2025:12:35:23 +0000] "GET /api/products/categories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:35:40.932Z] INFO: ::1 - - [02/Jul/2025:12:35:40 +0000] "GET /api/products?page=1&limit=20&sortBy=ProductName&sortDirection=ASC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:35:40.934Z] INFO: ::1 - - [02/Jul/2025:12:35:40 +0000] "GET /api/products/categories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:35:56.423Z] INFO: ::1 - - [02/Jul/2025:12:35:56 +0000] "GET /api/products?page=1&limit=20&sortBy=ProductName&sortDirection=ASC HTTP/1.1" 200 1317 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-02T12:36:04.861Z] INFO: ::1 - - [02/Jul/2025:12:36:04 +0000] "POST /api/products HTTP/1.1" 201 429 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-02T12:36:10.706Z] INFO: ::1 - - [02/Jul/2025:12:36:10 +0000] "POST /api/products/PROD004/models HTTP/1.1" 201 136 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-02T12:36:57.826Z] INFO: ::1 - - [02/Jul/2025:12:36:57 +0000] "GET /api/products/undefined HTTP/1.1" 404 47 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:36:58.972Z] INFO: ::1 - - [02/Jul/2025:12:36:58 +0000] "GET /api/products/undefined HTTP/1.1" 404 47 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:37:01.196Z] INFO: User disconnected: 1
[2025-07-02T12:37:01.699Z] INFO: User connected: 1 (Admin)
[2025-07-02T12:37:01.701Z] INFO: User 1 joined admin room
[2025-07-02T12:37:01.705Z] INFO: ::1 - - [02/Jul/2025:12:37:01 +0000] "GET /api/products/categories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:37:01.707Z] INFO: ::1 - - [02/Jul/2025:12:37:01 +0000] "GET /api/products/categories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:37:01.709Z] INFO: ::1 - - [02/Jul/2025:12:37:01 +0000] "GET /api/products?page=1&limit=20&sortBy=ProductName&sortDirection=ASC HTTP/1.1" 200 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:37:01.711Z] INFO: ::1 - - [02/Jul/2025:12:37:01 +0000] "GET /api/products?page=1&limit=20&sortBy=ProductName&sortDirection=ASC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:37:50.679Z] INFO: User disconnected: 1
[2025-07-02T12:38:17.402Z] INFO: ::1 - - [02/Jul/2025:12:38:17 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:38:17.443Z] INFO: User connected: 1 (Admin)
[2025-07-02T12:38:17.444Z] INFO: User 1 joined admin room
[2025-07-02T12:38:19.687Z] INFO: User disconnected: 1
[2025-07-02T12:38:19.719Z] INFO: User connected: 1 (Admin)
[2025-07-02T12:38:19.719Z] INFO: User 1 joined admin room
[2025-07-02T12:38:20.074Z] INFO: User disconnected: 1
[2025-07-02T12:38:20.092Z] INFO: ::1 - - [02/Jul/2025:12:38:20 +0000] "GET /api/products?page=1&limit=20&sortBy=ProductName&sortDirection=ASC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:38:20.094Z] INFO: ::1 - - [02/Jul/2025:12:38:20 +0000] "GET /api/products/categories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:38:20.096Z] INFO: ::1 - - [02/Jul/2025:12:38:20 +0000] "GET /api/products?page=1&limit=20&sortBy=ProductName&sortDirection=ASC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:38:20.098Z] INFO: ::1 - - [02/Jul/2025:12:38:20 +0000] "GET /api/products/categories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:38:23.688Z] INFO: ::1 - - [02/Jul/2025:12:38:23 +0000] "GET /api/products?page=1&limit=10&sortBy=ProductName&sortDirection=ASC HTTP/1.1" 200 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:38:57.948Z] INFO: ::1 - - [02/Jul/2025:12:38:57 +0000] "GET /api/products?page=1&limit=20&sortBy=ProductName&sortDirection=ASC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:38:57.950Z] INFO: ::1 - - [02/Jul/2025:12:38:57 +0000] "GET /api/products?page=1&limit=20&sortBy=ProductName&sortDirection=ASC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:38:57.951Z] INFO: ::1 - - [02/Jul/2025:12:38:57 +0000] "GET /api/products/categories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:38:57.954Z] INFO: User connected: 1 (Admin)
[2025-07-02T12:38:57.954Z] INFO: User 1 joined admin room
[2025-07-02T12:38:57.956Z] INFO: ::1 - - [02/Jul/2025:12:38:57 +0000] "GET /api/products/categories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:42:06.061Z] INFO: ::1 - - [02/Jul/2025:12:42:06 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:42:06.070Z] INFO: ::1 - - [02/Jul/2025:12:42:06 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:42:07.898Z] INFO: ::1 - - [02/Jul/2025:12:42:07 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:42:07.905Z] INFO: ::1 - - [02/Jul/2025:12:42:07 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:42:23.806Z] INFO: ::1 - - [02/Jul/2025:12:42:23 +0000] "GET /api/products?page=1&limit=20&sortBy=ProductName&sortDirection=ASC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:42:23.808Z] INFO: ::1 - - [02/Jul/2025:12:42:23 +0000] "GET /api/products?page=1&limit=20&sortBy=ProductName&sortDirection=ASC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:42:23.810Z] INFO: ::1 - - [02/Jul/2025:12:42:23 +0000] "GET /api/products/categories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:42:23.811Z] INFO: ::1 - - [02/Jul/2025:12:42:23 +0000] "GET /api/products/categories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:44:30.226Z] INFO: User disconnected: 1
[2025-07-02T12:44:30.259Z] INFO: User connected: 1 (Admin)
[2025-07-02T12:44:30.260Z] INFO: User 1 joined admin room
[2025-07-02T12:44:31.735Z] INFO: User disconnected: 1
[2025-07-02T12:53:25.926Z] INFO: User connected: 1 (Admin)
[2025-07-02T12:53:25.927Z] INFO: User 1 joined admin room
[2025-07-02T12:53:33.917Z] INFO: User disconnected: 1
[2025-07-02T12:53:33.922Z] INFO: User connected: 1 (Admin)
[2025-07-02T12:53:33.922Z] INFO: User 1 joined admin room
[2025-07-02T12:53:52.547Z] INFO: ::1 - - [02/Jul/2025:12:53:52 +0000] "GET /api/products/categories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:53:52.548Z] INFO: ::1 - - [02/Jul/2025:12:53:52 +0000] "GET /api/products/categories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:53:52.549Z] INFO: ::1 - - [02/Jul/2025:12:53:52 +0000] "GET /api/products?page=1&limit=12&sortBy=name&sortDirection=ASC HTTP/1.1" 200 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:53:52.550Z] INFO: ::1 - - [02/Jul/2025:12:53:52 +0000] "GET /api/products?page=1&limit=12&sortBy=name&sortDirection=ASC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:56:25.039Z] INFO: ::1 - - [02/Jul/2025:12:56:25 +0000] "PUT /api/products/PROD001 HTTP/1.1" 200 507 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:56:25.046Z] INFO: ::1 - - [02/Jul/2025:12:56:25 +0000] "POST /api/products/PROD001/models HTTP/1.1" 201 136 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:56:25.055Z] INFO: ::1 - - [02/Jul/2025:12:56:25 +0000] "POST /api/products/PROD001/images HTTP/1.1" 201 135 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:56:25.061Z] INFO: ::1 - - [02/Jul/2025:12:56:25 +0000] "GET /api/products?page=1&limit=12&sortBy=name&sortDirection=ASC HTTP/1.1" 200 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:56:58.345Z] INFO: User disconnected: 1
[2025-07-02T12:57:00.111Z] INFO: Connecting to MSSQL database...
[2025-07-02T12:57:00.189Z] INFO: Database connected successfully
[2025-07-02T12:57:00.208Z] INFO: Database connection test successful
[2025-07-02T12:57:00.209Z] INFO: Database connected successfully
[2025-07-02T12:57:00.211Z] INFO: WebSocket service initialized
[2025-07-02T12:57:07.023Z] INFO: ::1 - - [02/Jul/2025:12:57:07 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:57:07.069Z] INFO: User connected: 1 (Admin)
[2025-07-02T12:57:07.069Z] INFO: User 1 joined admin room
[2025-07-02T12:57:09.426Z] INFO: User disconnected: 1
[2025-07-02T12:57:09.457Z] INFO: User connected: 1 (Admin)
[2025-07-02T12:57:09.458Z] INFO: User 1 joined admin room
[2025-07-02T12:57:09.986Z] INFO: User disconnected: 1
[2025-07-02T12:57:10.018Z] INFO: ::1 - - [02/Jul/2025:12:57:10 +0000] "GET /api/products?page=1&limit=12&sortBy=name&sortDirection=ASC HTTP/1.1" 304 - "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:57:10.020Z] INFO: ::1 - - [02/Jul/2025:12:57:10 +0000] "GET /api/products?page=1&limit=12&sortBy=name&sortDirection=ASC HTTP/1.1" 304 - "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:57:10.024Z] INFO: ::1 - - [02/Jul/2025:12:57:10 +0000] "GET /api/products/categories HTTP/1.1" 304 - "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:57:10.028Z] INFO: ::1 - - [02/Jul/2025:12:57:10 +0000] "GET /api/products/categories HTTP/1.1" 304 - "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:57:24.274Z] INFO: ::1 - - [02/Jul/2025:12:57:24 +0000] "GET /api/products?page=1&limit=12&category=Chairs&sortBy=name&sortDirection=ASC HTTP/1.1" 200 925 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:57:50.693Z] INFO: ::1 - - [02/Jul/2025:12:57:50 +0000] "PUT /api/products/PROD004 HTTP/1.1" 200 451 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:57:50.699Z] INFO: ::1 - - [02/Jul/2025:12:57:50 +0000] "POST /api/products/PROD004/models HTTP/1.1" 201 136 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:57:50.709Z] INFO: ::1 - - [02/Jul/2025:12:57:50 +0000] "POST /api/products/PROD004/images HTTP/1.1" 201 135 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:57:50.715Z] INFO: ::1 - - [02/Jul/2025:12:57:50 +0000] "GET /api/products?page=1&limit=12&category=Chairs&sortBy=name&sortDirection=ASC HTTP/1.1" 200 947 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:59:56.079Z] INFO: ::1 - - [02/Jul/2025:12:59:56 +0000] "PUT /api/products/PROD004 HTTP/1.1" 200 451 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T12:59:56.085Z] INFO: ::1 - - [02/Jul/2025:12:59:56 +0000] "GET /api/products?page=1&limit=12&category=Chairs&sortBy=name&sortDirection=ASC HTTP/1.1" 200 947 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T13:00:36.242Z] INFO: ::1 - - [02/Jul/2025:13:00:36 +0000] "PUT /api/products/PROD004 HTTP/1.1" 200 451 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T13:00:36.248Z] INFO: ::1 - - [02/Jul/2025:13:00:36 +0000] "POST /api/products/PROD004/models HTTP/1.1" 201 136 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T13:00:36.264Z] INFO: ::1 - - [02/Jul/2025:13:00:36 +0000] "POST /api/products/PROD004/images HTTP/1.1" 201 135 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T13:00:36.272Z] INFO: ::1 - - [02/Jul/2025:13:00:36 +0000] "GET /api/products?page=1&limit=12&category=Chairs&sortBy=name&sortDirection=ASC HTTP/1.1" 200 947 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T13:00:50.101Z] INFO: Connecting to MSSQL database...
[2025-07-02T13:00:50.167Z] INFO: Database connected successfully
[2025-07-02T13:00:50.183Z] INFO: Database connection test successful
[2025-07-02T13:00:50.183Z] INFO: Database connected successfully
[2025-07-02T13:00:50.186Z] INFO: WebSocket service initialized
[2025-07-02T13:00:50.187Z] INFO: Server running on port 5000 in development mode
[2025-07-02T13:00:50.188Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-02T13:00:54.892Z] INFO: ::1 - - [02/Jul/2025:13:00:54 +0000] "PUT /api/products/PROD004 HTTP/1.1" 404 47 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T13:01:15.951Z] INFO: Product created event emitted to subscribers
[2025-07-02T13:01:15.953Z] INFO: ::1 - - [02/Jul/2025:13:01:15 +0000] "POST /api/products HTTP/1.1" 201 454 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4484"
[2025-07-02T13:01:59.563Z] INFO: Product updated event emitted to subscribers
[2025-07-02T13:01:59.565Z] INFO: ::1 - - [02/Jul/2025:13:01:59 +0000] "PUT /api/products/PROD004 HTTP/1.1" 200 451 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T13:01:59.571Z] INFO: ::1 - - [02/Jul/2025:13:01:59 +0000] "POST /api/products/PROD004/models HTTP/1.1" 201 136 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T13:01:59.582Z] INFO: ::1 - - [02/Jul/2025:13:01:59 +0000] "POST /api/products/PROD004/images HTTP/1.1" 201 135 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T13:01:59.591Z] INFO: ::1 - - [02/Jul/2025:13:01:59 +0000] "GET /api/products?page=1&limit=12&category=Chairs&sortBy=name&sortDirection=ASC HTTP/1.1" 200 925 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-02T14:14:12.502Z] INFO: Connecting to MSSQL database...
[2025-07-02T14:14:27.518Z] ERROR: Database connection failed: {"code":"ETIMEOUT","originalError":{"code":"ETIMEOUT"},"name":"ConnectionError"}
[2025-07-02T14:14:27.520Z] WARN: Database connection failed, starting server without database: "Failed to connect to DESKTOP-F4OI6BT\\SQLEXPRESS in 15000ms"
[2025-07-02T14:14:27.521Z] INFO: Server will use mock data until database is available
[2025-07-02T14:14:27.529Z] INFO: WebSocket service initialized
[2025-07-02T14:14:27.535Z] INFO: Server running on port 5000 in development mode
[2025-07-02T14:14:27.536Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-02T14:16:16.697Z] INFO: Connecting to MSSQL database...
[2025-07-02T14:16:31.721Z] ERROR: Database connection failed: {"code":"ETIMEOUT","originalError":{"code":"ETIMEOUT"},"name":"ConnectionError"}
[2025-07-02T14:16:31.726Z] WARN: Database connection failed, starting server without database: "Failed to connect to DESKTOP-F4OI6BT\\SQLEXPRESS in 15000ms"
[2025-07-02T14:16:31.728Z] INFO: Server will use mock data until database is available
[2025-07-02T14:16:31.750Z] INFO: WebSocket service initialized
[2025-07-02T14:16:31.760Z] INFO: Server running on port 5000 in development mode
[2025-07-02T14:16:31.761Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-09T01:05:05.519Z] INFO: Connecting to MSSQL database...
[2025-07-09T01:05:07.832Z] ERROR: Database connection failed: {"code":"EINSTLOOKUP","originalError":{"code":"EINSTLOOKUP"},"name":"ConnectionError"}
[2025-07-09T01:05:07.835Z] WARN: Database connection failed, starting server without database: "getaddrinfo ENOTFOUND desktop-f4oi6bt"
[2025-07-09T01:05:07.836Z] INFO: Server will use mock data until database is available
[2025-07-09T01:05:07.845Z] INFO: WebSocket service initialized
[2025-07-09T01:05:07.869Z] INFO: Server running on port 5000 in development mode
[2025-07-09T01:05:07.870Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-09T01:06:01.206Z] INFO: ::1 - - [09/Jul/2025:01:06:01 +0000] "GET /api/health HTTP/1.1" 200 212 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.3803"
[2025-07-09T01:26:58.458Z] INFO: Connecting to MSSQL database...
[2025-07-09T01:27:13.807Z] ERROR: Database connection failed: {"code":"ETIMEOUT","originalError":{"code":"ETIMEOUT"},"name":"ConnectionError"}
[2025-07-09T01:27:14.470Z] ERROR: Database connection failed: {"code":"ETIMEOUT","originalError":{"code":"ETIMEOUT"},"name":"ConnectionError"}
[2025-07-09T01:28:12.500Z] INFO: Testing database connection...
[2025-07-09T01:28:27.542Z] ERROR: Database connection test failed: {"code":"ETIMEOUT","originalError":{"code":"ETIMEOUT"},"name":"ConnectionError"}
[2025-07-09T01:37:32.542Z] INFO: Testing database connection...
[2025-07-09T01:37:47.560Z] ERROR: Database connection test failed: {"code":"ETIMEOUT","originalError":{"code":"ETIMEOUT"},"name":"ConnectionError"}
[2025-07-09T01:38:02.275Z] INFO: Connecting to MSSQL database...
[2025-07-09T01:39:36.907Z] INFO: Testing database connection...
[2025-07-09T01:40:17.521Z] INFO: Testing database connection...
[2025-07-09T01:40:32.540Z] ERROR: Database connection test failed: {"code":"ETIMEOUT","originalError":{"code":"ETIMEOUT"},"name":"ConnectionError"}
[2025-07-09T02:14:36.747Z] INFO: Connecting to MSSQL database...
[2025-07-09T02:14:39.040Z] ERROR: Database connection failed: {"code":"EINSTLOOKUP","originalError":{"code":"EINSTLOOKUP"},"name":"ConnectionError"}
[2025-07-09T02:14:39.041Z] WARN: Database connection failed, starting server without database: "getaddrinfo ENOTFOUND desktop-f4oi6bt"
[2025-07-09T02:14:39.042Z] INFO: Server will use mock data until database is available
[2025-07-09T02:14:39.048Z] INFO: WebSocket service initialized
[2025-07-09T02:14:39.060Z] INFO: Server running on port 5000 in development mode
[2025-07-09T02:14:39.062Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-09T03:02:45.010Z] INFO: Connecting to MSSQL database...
[2025-07-09T03:02:47.318Z] ERROR: Database connection failed: {"code":"EINSTLOOKUP","originalError":{"code":"EINSTLOOKUP"},"name":"ConnectionError"}
[2025-07-09T03:02:47.319Z] WARN: Database connection failed, starting server without database: "getaddrinfo ENOTFOUND desktop-f4oi6bt"
[2025-07-09T03:02:47.320Z] INFO: Server will use mock data until database is available
[2025-07-09T03:02:47.324Z] INFO: WebSocket service initialized
[2025-07-09T03:02:47.331Z] INFO: Server running on port 5000 in development mode
[2025-07-09T03:02:47.334Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-09T03:03:37.764Z] INFO: Connecting to MSSQL database...
[2025-07-09T03:03:52.787Z] ERROR: Database connection failed: {"code":"ETIMEOUT","originalError":{"code":"ETIMEOUT"},"name":"ConnectionError"}
[2025-07-09T03:03:52.788Z] WARN: Database connection failed, starting server without database: "Failed to connect to DESKTOP-DPNS718\\SQLEXPRESS in 15000ms"
[2025-07-09T03:03:52.789Z] INFO: Server will use mock data until database is available
[2025-07-09T03:03:52.796Z] INFO: WebSocket service initialized
[2025-07-09T03:03:52.802Z] INFO: Server running on port 5000 in development mode
[2025-07-09T03:03:52.803Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-09T03:05:13.718Z] INFO: Connecting to MSSQL database...
[2025-07-09T03:05:28.738Z] ERROR: Database connection failed: {"code":"ETIMEOUT","originalError":{"code":"ETIMEOUT"},"name":"ConnectionError"}
[2025-07-09T03:05:28.741Z] WARN: Database connection failed, starting server without database: "Failed to connect to DESKTOP-DPNS718\\SQLEXPRESS in 15000ms"
[2025-07-09T03:05:28.744Z] INFO: Server will use mock data until database is available
[2025-07-09T03:05:28.762Z] INFO: WebSocket service initialized
[2025-07-09T03:05:28.783Z] INFO: Server running on port 5000 in development mode
[2025-07-09T03:05:28.786Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-09T03:05:59.249Z] INFO: Connecting to MSSQL database...
[2025-07-09T03:06:14.264Z] ERROR: Database connection failed: {"code":"ETIMEOUT","originalError":{"code":"ETIMEOUT"},"name":"ConnectionError"}
[2025-07-09T03:06:14.267Z] WARN: Database connection failed, starting server without database: "Failed to connect to localhost\\SQLEXPRESS in 15000ms"
[2025-07-09T03:06:14.269Z] INFO: Server will use mock data until database is available
[2025-07-09T03:06:14.286Z] INFO: WebSocket service initialized
[2025-07-09T03:06:14.301Z] INFO: Server running on port 5000 in development mode
[2025-07-09T03:06:14.304Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-09T03:07:09.775Z] INFO: Connecting to MSSQL database...
[2025-07-09T03:07:24.798Z] ERROR: Database connection failed: {"code":"ETIMEOUT","originalError":{"code":"ETIMEOUT"},"name":"ConnectionError"}
[2025-07-09T03:07:24.800Z] WARN: Database connection failed, starting server without database: "Failed to connect to localhost\\SQLEXPRESS in 15000ms"
[2025-07-09T03:07:24.807Z] INFO: Server will use mock data until database is available
[2025-07-09T03:07:24.821Z] INFO: WebSocket service initialized
[2025-07-09T03:07:24.831Z] INFO: Server running on port 5000 in development mode
[2025-07-09T03:07:24.833Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-09T03:09:40.127Z] INFO: Connecting to MSSQL database...
[2025-07-09T03:09:40.154Z] ERROR: Database connection failed: {"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}
[2025-07-09T03:09:40.155Z] WARN: Database connection failed, starting server without database: "Failed to connect to localhost:1433 - Could not connect (sequence)"
[2025-07-09T03:09:40.157Z] INFO: Server will use mock data until database is available
[2025-07-09T03:09:40.164Z] INFO: WebSocket service initialized
[2025-07-09T03:09:40.170Z] INFO: Server running on port 5000 in development mode
[2025-07-09T03:09:40.170Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-09T03:10:50.782Z] INFO: Connecting to MSSQL database...
[2025-07-09T03:10:50.793Z] ERROR: Database connection failed:
[2025-07-09T03:10:50.794Z] WARN: Database connection failed, starting server without database: "The \"config.options.port\" property must be of type number."
[2025-07-09T03:10:50.794Z] INFO: Server will use mock data until database is available
[2025-07-09T03:10:50.800Z] INFO: WebSocket service initialized
[2025-07-09T03:10:50.804Z] INFO: Server running on port 5000 in development mode
[2025-07-09T03:10:50.805Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-09T03:11:31.143Z] INFO: Connecting to MSSQL database...
[2025-07-09T03:11:31.197Z] ERROR: Database connection failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-07-09T03:11:31.198Z] WARN: Database connection failed, starting server without database: "Login failed for user ''."
[2025-07-09T03:11:31.199Z] INFO: Server will use mock data until database is available
[2025-07-09T03:11:31.204Z] INFO: WebSocket service initialized
[2025-07-09T03:11:31.208Z] INFO: Server running on port 5000 in development mode
[2025-07-09T03:11:31.209Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-09T03:13:03.647Z] INFO: Connecting to MSSQL database...
[2025-07-09T03:13:03.698Z] ERROR: Database connection failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-07-09T03:13:03.699Z] WARN: Database connection failed, starting server without database: "Login failed for user 'DesignXcel'."
[2025-07-09T03:13:03.699Z] INFO: Server will use mock data until database is available
[2025-07-09T03:13:03.703Z] INFO: WebSocket service initialized
[2025-07-09T03:13:03.708Z] INFO: Server running on port 5000 in development mode
[2025-07-09T03:13:03.712Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-09T03:17:05.152Z] INFO: Connecting to MSSQL database...
[2025-07-09T03:17:20.199Z] ERROR: Database connection failed: {"code":"ETIMEOUT","originalError":{"code":"ETIMEOUT"},"name":"ConnectionError"}
[2025-07-09T03:17:20.205Z] WARN: Database connection failed, starting server without database: "Failed to connect to localhost\\\\SQLEXPRESS in 15000ms"
[2025-07-09T03:17:20.209Z] INFO: Server will use mock data until database is available
[2025-07-09T03:17:20.242Z] INFO: WebSocket service initialized
[2025-07-09T03:17:20.250Z] INFO: Server running on port 5000 in development mode
[2025-07-09T03:17:20.255Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-09T03:25:33.716Z] INFO: Connecting to MSSQL database...
[2025-07-09T03:25:48.746Z] ERROR: Database connection failed: {"code":"ETIMEOUT","originalError":{"code":"ETIMEOUT"},"name":"ConnectionError"}
[2025-07-09T03:25:48.747Z] WARN: Database connection failed, starting server without database: "Failed to connect to localhost\\SQLEXPRESS in 15000ms"
[2025-07-09T03:25:48.748Z] INFO: Server will use mock data until database is available
[2025-07-09T03:25:48.752Z] INFO: WebSocket service initialized
[2025-07-09T03:25:48.759Z] INFO: Server running on port 5000 in development mode
[2025-07-09T03:25:48.761Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-09T03:40:30.246Z] INFO: Starting database migration...
[2025-07-09T03:40:30.250Z] INFO: Connecting to MSSQL database...
[2025-07-09T03:40:45.271Z] ERROR: Database connection failed: {"code":"ETIMEOUT","originalError":{"code":"ETIMEOUT"},"name":"ConnectionError"}
[2025-07-09T03:40:45.272Z] ERROR: Migration failed: {"code":"ETIMEOUT","originalError":{"code":"ETIMEOUT"},"name":"ConnectionError"}
[2025-07-09T03:44:01.259Z] INFO: Starting database migration...
[2025-07-09T03:44:01.265Z] INFO: Connecting to MSSQL database...
[2025-07-09T03:44:01.403Z] ERROR: Database connection failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-07-09T03:44:01.405Z] ERROR: Migration failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-07-09T03:44:17.893Z] INFO: Connecting to MSSQL database...
[2025-07-09T03:44:18.026Z] ERROR: Database connection failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-07-09T03:44:18.028Z] ERROR: Database connection failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-07-09T03:50:51.534Z] INFO: Connecting to MSSQL database...
[2025-07-09T03:50:51.603Z] ERROR: Database connection failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-07-09T03:50:51.604Z] WARN: Database connection failed, starting server without database: "Login failed for user ''."
[2025-07-09T03:50:51.605Z] INFO: Server will use mock data until database is available
[2025-07-09T03:50:51.610Z] INFO: WebSocket service initialized
[2025-07-09T03:50:51.614Z] INFO: Server running on port 5000 in development mode
[2025-07-09T03:50:51.615Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-09T03:51:26.109Z] INFO: Connecting to MSSQL database...
[2025-07-09T03:51:26.186Z] ERROR: Database connection failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-07-09T03:51:26.186Z] ERROR: Database connection failed: {"code":"ELOGIN","originalError":{"code":"ELOGIN"},"name":"ConnectionError"}
[2025-07-09T04:17:17.789Z] INFO: Connecting to MSSQL database...
[2025-07-09T04:17:18.060Z] INFO: Database connected successfully
[2025-07-09T04:17:18.099Z] INFO: Database connection test successful
[2025-07-09T04:17:18.111Z] INFO: Database connection test successful
[2025-07-09T04:17:18.113Z] INFO: Database connection closed
[2025-07-09T04:17:30.414Z] INFO: Connecting to MSSQL database...
[2025-07-09T04:17:30.482Z] INFO: Database connected successfully
[2025-07-09T04:17:30.516Z] INFO: Database connection test successful
[2025-07-09T04:17:30.517Z] INFO: Database connected successfully
[2025-07-09T04:17:30.522Z] INFO: WebSocket service initialized
[2025-07-09T04:18:26.905Z] INFO: Connecting to MSSQL database...
[2025-07-09T04:18:26.974Z] INFO: Database connected successfully
[2025-07-09T04:18:27.019Z] INFO: Database connection test successful
[2025-07-09T04:18:27.020Z] INFO: Database connected successfully
[2025-07-09T04:18:27.025Z] INFO: WebSocket service initialized
[2025-07-09T04:18:27.030Z] INFO: Server running on port 5001 in development mode
[2025-07-09T04:18:27.031Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-09T04:20:31.752Z] INFO: ::1 - - [09/Jul/2025:04:20:31 +0000] "POST /api/auth/login HTTP/1.1" 401 49 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:20:39.566Z] INFO: ::1 - - [09/Jul/2025:04:20:39 +0000] "POST /api/auth/login HTTP/1.1" 401 49 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:20:55.443Z] INFO: ::1 - - [09/Jul/2025:04:20:55 +0000] "POST /api/auth/login HTTP/1.1" 401 49 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:20:56.249Z] INFO: ::1 - - [09/Jul/2025:04:20:56 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:20:57.294Z] INFO: ::1 - - [09/Jul/2025:04:20:57 +0000] "GET /favicon.ico HTTP/1.1" 404 45 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:26:30.860Z] INFO: Connecting to MSSQL database...
[2025-07-09T04:26:31.153Z] INFO: Database connected successfully
[2025-07-09T04:26:31.220Z] INFO: Database connection test successful
[2025-07-09T04:26:31.221Z] INFO: Database connected successfully
[2025-07-09T04:26:31.228Z] INFO: WebSocket service initialized
[2025-07-09T04:26:31.234Z] INFO: Server running on port 5001 in development mode
[2025-07-09T04:26:31.235Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-09T04:27:29.560Z] INFO: ::1 - - [09/Jul/2025:04:27:29 +0000] "POST /api/auth/login HTTP/1.1" 401 49 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:27:54.833Z] INFO: ::1 - - [09/Jul/2025:04:27:54 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.3803"
[2025-07-09T04:28:12.747Z] INFO: ::1 - - [09/Jul/2025:04:28:12 +0000] "POST /api/auth/login HTTP/1.1" 200 526 "-" "axios/1.10.0"
[2025-07-09T04:33:06.911Z] INFO: Connecting to MSSQL database...
[2025-07-09T04:33:07.300Z] INFO: Database connected successfully
[2025-07-09T04:33:07.349Z] INFO: Database connection test successful
[2025-07-09T04:33:07.350Z] INFO: Database connected successfully
[2025-07-09T04:33:07.358Z] INFO: WebSocket service initialized
[2025-07-09T04:33:07.362Z] INFO: Server running on port 5001 in development mode
[2025-07-09T04:33:07.363Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-09T04:33:37.935Z] INFO: ::1 - - [09/Jul/2025:04:33:37 +0000] "POST /api/auth/login HTTP/1.1" 401 84 "-" "axios/1.10.0"
[2025-07-09T04:34:59.696Z] INFO: ::1 - - [09/Jul/2025:04:34:59 +0000] "POST /api/auth/login HTTP/1.1" 401 84 "-" "axios/1.10.0"
[2025-07-09T04:35:32.828Z] ERROR: Query execution failed:
[2025-07-09T04:36:04.287Z] INFO: Connecting to MSSQL database...
[2025-07-09T04:36:04.444Z] INFO: Database connected successfully
[2025-07-09T04:36:04.508Z] INFO: Database connection test successful
[2025-07-09T04:38:04.420Z] INFO: Connecting to MSSQL database...
[2025-07-09T04:38:04.526Z] INFO: Database connected successfully
[2025-07-09T04:38:04.581Z] INFO: Database connection test successful
[2025-07-09T04:38:22.368Z] INFO: ::1 - - [09/Jul/2025:04:38:22 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "-" "axios/1.10.0"
[2025-07-09T04:39:16.703Z] INFO: ::1 - - [09/Jul/2025:04:39:16 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:39:56.425Z] INFO: ::1 - - [09/Jul/2025:04:39:56 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:40:30.118Z] INFO: ::1 - - [09/Jul/2025:04:40:30 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:44:46.611Z] INFO: ::1 - - [09/Jul/2025:04:44:46 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "-" "axios/1.10.0"
[2025-07-09T04:47:33.431Z] INFO: ::1 - - [09/Jul/2025:04:47:33 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:47:42.544Z] INFO: ::1 - - [09/Jul/2025:04:47:42 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:50:01.803Z] INFO: ::1 - - [09/Jul/2025:04:50:01 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:50:02.074Z] INFO: User connected: undefined (Admin)
[2025-07-09T04:50:02.077Z] INFO: User undefined joined admin room
[2025-07-09T04:50:02.085Z] INFO: User undefined subscribed to inventory updates
[2025-07-09T04:50:02.091Z] INFO: User undefined subscribed to orders updates
[2025-07-09T04:50:02.093Z] INFO: User undefined subscribed to dashboard updates
[2025-07-09T04:50:04.709Z] INFO: User disconnected: undefined
[2025-07-09T04:50:04.772Z] INFO: User connected: undefined (Admin)
[2025-07-09T04:50:04.773Z] INFO: User undefined joined admin room
[2025-07-09T04:50:04.776Z] INFO: User undefined subscribed to inventory updates
[2025-07-09T04:50:04.778Z] INFO: User undefined subscribed to orders updates
[2025-07-09T04:50:04.779Z] INFO: User undefined subscribed to dashboard updates
[2025-07-09T04:50:05.565Z] INFO: User disconnected: undefined
[2025-07-09T04:50:05.621Z] ERROR: ProductService.getProducts error:
[2025-07-09T04:50:05.623Z] ERROR: ProductController.getProducts error:
[2025-07-09T04:50:05.625Z] INFO: ::1 - - [09/Jul/2025:04:50:05 +0000] "GET /api/products?page=1&limit=12&sortBy=name&sortDirection=ASC HTTP/1.1" 500 121 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:50:05.646Z] INFO: ::1 - - [09/Jul/2025:04:50:05 +0000] "GET /api/products/categories HTTP/1.1" 500 42 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:50:05.650Z] ERROR: ProductService.getProducts error:
[2025-07-09T04:50:05.656Z] ERROR: ProductController.getProducts error:
[2025-07-09T04:50:05.658Z] INFO: ::1 - - [09/Jul/2025:04:50:05 +0000] "GET /api/products?page=1&limit=12&sortBy=name&sortDirection=ASC HTTP/1.1" 500 121 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:50:05.693Z] INFO: ::1 - - [09/Jul/2025:04:50:05 +0000] "GET /api/products/categories HTTP/1.1" 500 42 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:50:14.664Z] ERROR: ProductService.getProducts error:
[2025-07-09T04:50:14.682Z] ERROR: ProductController.getProducts error:
[2025-07-09T04:50:14.687Z] INFO: ::1 - - [09/Jul/2025:04:50:14 +0000] "GET /api/products?page=1&limit=12&sortBy=name&sortDirection=ASC HTTP/1.1" 500 121 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:50:14.750Z] INFO: ::1 - - [09/Jul/2025:04:50:14 +0000] "GET /api/products/categories HTTP/1.1" 500 42 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:50:14.758Z] INFO: User connected: undefined (Admin)
[2025-07-09T04:50:14.763Z] INFO: User undefined joined admin room
[2025-07-09T04:50:14.771Z] ERROR: ProductService.getProducts error:
[2025-07-09T04:50:14.776Z] ERROR: ProductController.getProducts error:
[2025-07-09T04:50:14.781Z] INFO: ::1 - - [09/Jul/2025:04:50:14 +0000] "GET /api/products?page=1&limit=12&sortBy=name&sortDirection=ASC HTTP/1.1" 500 121 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:50:14.819Z] INFO: ::1 - - [09/Jul/2025:04:50:14 +0000] "GET /api/products/categories HTTP/1.1" 500 42 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:50:14.829Z] INFO: User undefined subscribed to inventory updates
[2025-07-09T04:50:14.830Z] INFO: User undefined subscribed to orders updates
[2025-07-09T04:50:14.831Z] INFO: User undefined subscribed to dashboard updates
[2025-07-09T04:50:14.845Z] INFO: User disconnected: undefined
[2025-07-09T04:53:29.298Z] ERROR: ProductService.getProducts error:
[2025-07-09T04:53:29.309Z] ERROR: ProductController.getProducts error:
[2025-07-09T04:53:29.312Z] INFO: ::1 - - [09/Jul/2025:04:53:29 +0000] "GET /api/products?page=1&limit=12&sortBy=name&sortDirection=ASC HTTP/1.1" 500 121 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:53:29.328Z] ERROR: ProductService.getProducts error:
[2025-07-09T04:53:29.329Z] ERROR: ProductController.getProducts error:
[2025-07-09T04:53:29.337Z] INFO: ::1 - - [09/Jul/2025:04:53:29 +0000] "GET /api/products?page=1&limit=12&sortBy=name&sortDirection=ASC HTTP/1.1" 500 121 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:53:29.356Z] INFO: ::1 - - [09/Jul/2025:04:53:29 +0000] "GET /api/products/categories HTTP/1.1" 500 42 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:53:29.373Z] INFO: ::1 - - [09/Jul/2025:04:53:29 +0000] "GET /api/products/categories HTTP/1.1" 500 42 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:53:29.407Z] INFO: User connected: undefined (Admin)
[2025-07-09T04:53:29.408Z] INFO: User undefined joined admin room
[2025-07-09T04:53:29.455Z] INFO: User undefined subscribed to inventory updates
[2025-07-09T04:53:29.456Z] INFO: User undefined subscribed to orders updates
[2025-07-09T04:53:29.457Z] INFO: User undefined subscribed to dashboard updates
[2025-07-09T04:53:57.836Z] ERROR: ProductService.getProducts error:
[2025-07-09T04:53:57.837Z] ERROR: ProductController.getProducts error:
[2025-07-09T04:53:57.842Z] INFO: ::1 - - [09/Jul/2025:04:53:57 +0000] "GET /api/products?page=1&limit=12&category=Tables&sortBy=name&sortDirection=ASC HTTP/1.1" 500 121 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:53:59.867Z] ERROR: ProductService.getProducts error:
[2025-07-09T04:53:59.877Z] ERROR: ProductController.getProducts error:
[2025-07-09T04:53:59.880Z] INFO: ::1 - - [09/Jul/2025:04:53:59 +0000] "GET /api/products?page=1&limit=12&sortBy=name&sortDirection=ASC HTTP/1.1" 500 121 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:54:06.314Z] ERROR: OrderService.getOrders error:
[2025-07-09T04:54:06.317Z] ERROR: OrderController.getOrders error:
[2025-07-09T04:54:06.321Z] INFO: ::1 - - [09/Jul/2025:04:54:06 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:54:06.328Z] ERROR: OrderService.getOrders error:
[2025-07-09T04:54:06.329Z] ERROR: OrderController.getOrders error:
[2025-07-09T04:54:06.331Z] INFO: ::1 - - [09/Jul/2025:04:54:06 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:54:07.349Z] ERROR: OrderService.getOrders error:
[2025-07-09T04:54:07.350Z] ERROR: OrderController.getOrders error:
[2025-07-09T04:54:07.353Z] INFO: ::1 - - [09/Jul/2025:04:54:07 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:54:11.235Z] INFO: User disconnected: undefined
[2025-07-09T04:54:11.297Z] INFO: User connected: undefined (Admin)
[2025-07-09T04:54:11.304Z] INFO: User undefined joined admin room
[2025-07-09T04:54:11.307Z] INFO: User undefined subscribed to inventory updates
[2025-07-09T04:54:11.313Z] INFO: User undefined subscribed to orders updates
[2025-07-09T04:54:11.319Z] INFO: User undefined subscribed to dashboard updates
[2025-07-09T04:54:13.553Z] INFO: User disconnected: undefined
[2025-07-09T04:54:13.603Z] ERROR: ProductService.getProducts error:
[2025-07-09T04:54:13.604Z] ERROR: ProductController.getProducts error:
[2025-07-09T04:54:13.607Z] INFO: ::1 - - [09/Jul/2025:04:54:13 +0000] "GET /api/products?page=1&limit=12&sortBy=name&sortDirection=ASC HTTP/1.1" 500 121 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:54:13.625Z] INFO: ::1 - - [09/Jul/2025:04:54:13 +0000] "GET /api/products/categories HTTP/1.1" 500 42 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:54:13.636Z] ERROR: ProductService.getProducts error:
[2025-07-09T04:54:13.638Z] ERROR: ProductController.getProducts error:
[2025-07-09T04:54:13.640Z] INFO: ::1 - - [09/Jul/2025:04:54:13 +0000] "GET /api/products?page=1&limit=12&sortBy=name&sortDirection=ASC HTTP/1.1" 500 121 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:54:13.672Z] INFO: ::1 - - [09/Jul/2025:04:54:13 +0000] "GET /api/products/categories HTTP/1.1" 500 42 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:54:25.441Z] ERROR: OrderService.getOrders error:
[2025-07-09T04:54:25.443Z] ERROR: OrderController.getOrders error:
[2025-07-09T04:54:25.445Z] INFO: ::1 - - [09/Jul/2025:04:54:25 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:54:25.461Z] ERROR: OrderService.getOrders error:
[2025-07-09T04:54:25.462Z] ERROR: OrderController.getOrders error:
[2025-07-09T04:54:25.463Z] INFO: ::1 - - [09/Jul/2025:04:54:25 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:54:36.087Z] ERROR: ProductService.getProducts error:
[2025-07-09T04:54:36.168Z] ERROR: ProductController.getProducts error:
[2025-07-09T04:54:36.202Z] INFO: ::1 - - [09/Jul/2025:04:54:36 +0000] "GET /api/products?page=1&limit=12&sortBy=name&sortDirection=ASC HTTP/1.1" 500 121 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:54:36.234Z] INFO: ::1 - - [09/Jul/2025:04:54:36 +0000] "GET /api/products/categories HTTP/1.1" 500 42 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:54:36.254Z] ERROR: ProductService.getProducts error:
[2025-07-09T04:54:36.268Z] ERROR: ProductController.getProducts error:
[2025-07-09T04:54:36.338Z] INFO: ::1 - - [09/Jul/2025:04:54:36 +0000] "GET /api/products?page=1&limit=12&sortBy=name&sortDirection=ASC HTTP/1.1" 500 121 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-09T04:54:36.356Z] INFO: ::1 - - [09/Jul/2025:04:54:36 +0000] "GET /api/products/categories HTTP/1.1" 500 42 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T02:30:43.841Z] INFO: Connecting to MSSQL database...
[2025-07-15T02:30:44.371Z] INFO: Database connected successfully
[2025-07-15T02:30:44.460Z] INFO: Database connection test successful
[2025-07-15T02:30:44.462Z] INFO: Database connected successfully
[2025-07-15T02:30:44.470Z] INFO: WebSocket service initialized
[2025-07-15T02:30:44.482Z] INFO: Server running on port 5001 in development mode
[2025-07-15T02:30:44.493Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T02:30:55.259Z] INFO: Connecting to MSSQL database...
[2025-07-15T02:30:55.328Z] INFO: Database connected successfully
[2025-07-15T02:30:55.364Z] INFO: Database connection test successful
[2025-07-15T02:30:55.364Z] INFO: Database connected successfully
[2025-07-15T02:30:55.369Z] INFO: WebSocket service initialized
[2025-07-15T02:30:55.374Z] INFO: Server running on port 5001 in development mode
[2025-07-15T02:30:55.376Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T02:32:26.670Z] INFO: ::1 - - [15/Jul/2025:02:32:26 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T02:32:26.853Z] INFO: User connected: undefined (Admin)
[2025-07-15T02:32:26.855Z] INFO: User undefined joined admin room
[2025-07-15T02:32:26.864Z] INFO: User undefined subscribed to inventory updates
[2025-07-15T02:32:26.896Z] INFO: User undefined subscribed to orders updates
[2025-07-15T02:32:26.901Z] INFO: User undefined subscribed to dashboard updates
[2025-07-15T02:33:27.339Z] INFO: Connecting to MSSQL database...
[2025-07-15T02:33:27.416Z] INFO: Database connected successfully
[2025-07-15T02:33:27.453Z] INFO: Database connection test successful
[2025-07-15T02:33:27.454Z] INFO: Database connected successfully
[2025-07-15T02:33:27.462Z] INFO: WebSocket service initialized
[2025-07-15T02:33:27.466Z] INFO: Server running on port 5001 in development mode
[2025-07-15T02:33:27.466Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T02:33:55.038Z] INFO: Connecting to MSSQL database...
[2025-07-15T02:33:55.109Z] INFO: Database connected successfully
[2025-07-15T02:33:55.152Z] INFO: Database connection test successful
[2025-07-15T02:33:55.154Z] INFO: Database connected successfully
[2025-07-15T02:33:55.162Z] INFO: WebSocket service initialized
[2025-07-15T02:33:55.167Z] INFO: Server running on port 5001 in development mode
[2025-07-15T02:33:55.168Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T02:38:26.590Z] INFO: Connecting to MSSQL database...
[2025-07-15T02:38:26.654Z] INFO: Database connected successfully
[2025-07-15T02:38:26.683Z] INFO: Database connection test successful
[2025-07-15T02:38:26.684Z] INFO: Database connected successfully
[2025-07-15T02:38:26.689Z] INFO: WebSocket service initialized
[2025-07-15T02:38:26.692Z] INFO: Server running on port 5001 in development mode
[2025-07-15T02:38:26.693Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T02:38:59.080Z] INFO: Connecting to MSSQL database...
[2025-07-15T02:38:59.263Z] INFO: Database connected successfully
[2025-07-15T02:38:59.307Z] INFO: Database connection test successful
[2025-07-15T02:38:59.309Z] INFO: Database connected successfully
[2025-07-15T02:38:59.313Z] INFO: WebSocket service initialized
[2025-07-15T02:38:59.317Z] INFO: Server running on port 5001 in development mode
[2025-07-15T02:38:59.318Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T02:39:07.702Z] INFO: Connecting to MSSQL database...
[2025-07-15T02:39:07.819Z] INFO: Database connected successfully
[2025-07-15T02:39:07.866Z] INFO: Database connection test successful
[2025-07-15T02:39:07.867Z] INFO: Database connected successfully
[2025-07-15T02:39:07.872Z] INFO: WebSocket service initialized
[2025-07-15T02:39:07.876Z] INFO: Server running on port 5001 in development mode
[2025-07-15T02:39:07.877Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T02:39:18.894Z] INFO: Connecting to MSSQL database...
[2025-07-15T02:39:18.951Z] INFO: Database connected successfully
[2025-07-15T02:39:18.986Z] INFO: Database connection test successful
[2025-07-15T02:39:18.987Z] INFO: Database connected successfully
[2025-07-15T02:39:18.993Z] INFO: WebSocket service initialized
[2025-07-15T02:39:18.997Z] INFO: Server running on port 5001 in development mode
[2025-07-15T02:39:18.997Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T02:39:44.767Z] INFO: Connecting to MSSQL database...
[2025-07-15T02:39:44.822Z] INFO: Database connected successfully
[2025-07-15T02:39:44.852Z] INFO: Database connection test successful
[2025-07-15T02:39:44.852Z] INFO: Database connected successfully
[2025-07-15T02:39:44.856Z] INFO: WebSocket service initialized
[2025-07-15T02:39:44.861Z] INFO: Server running on port 5001 in development mode
[2025-07-15T02:39:44.863Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T02:40:27.249Z] INFO: Connecting to MSSQL database...
[2025-07-15T02:40:27.349Z] INFO: Database connected successfully
[2025-07-15T02:40:27.396Z] INFO: Database connection test successful
[2025-07-15T02:40:27.397Z] INFO: Database connected successfully
[2025-07-15T02:40:27.404Z] INFO: WebSocket service initialized
[2025-07-15T02:40:27.408Z] INFO: Server running on port 5001 in development mode
[2025-07-15T02:40:27.409Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T02:42:35.905Z] INFO: Connecting to MSSQL database...
[2025-07-15T02:42:36.027Z] INFO: Database connected successfully
[2025-07-15T02:42:36.261Z] INFO: Database connection test successful
[2025-07-15T02:48:11.144Z] INFO: Connecting to MSSQL database...
[2025-07-15T02:48:11.231Z] INFO: Database connected successfully
[2025-07-15T02:48:11.280Z] INFO: Database connection test successful
[2025-07-15T02:48:11.281Z] INFO: Database connected successfully
[2025-07-15T02:48:11.287Z] INFO: WebSocket service initialized
[2025-07-15T02:48:11.292Z] INFO: Server running on port 5001 in development mode
[2025-07-15T02:48:11.294Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T02:48:28.277Z] INFO: Connecting to MSSQL database...
[2025-07-15T02:48:28.372Z] INFO: Database connected successfully
[2025-07-15T02:48:28.421Z] INFO: Database connection test successful
[2025-07-15T02:48:28.422Z] INFO: Database connected successfully
[2025-07-15T02:48:28.430Z] INFO: WebSocket service initialized
[2025-07-15T02:48:28.435Z] INFO: Server running on port 5001 in development mode
[2025-07-15T02:48:28.436Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T02:48:40.209Z] INFO: Connecting to MSSQL database...
[2025-07-15T02:48:40.320Z] INFO: Database connected successfully
[2025-07-15T02:48:40.380Z] INFO: Database connection test successful
[2025-07-15T02:48:40.381Z] INFO: Database connected successfully
[2025-07-15T02:48:40.391Z] INFO: WebSocket service initialized
[2025-07-15T02:48:40.396Z] INFO: Server running on port 5001 in development mode
[2025-07-15T02:48:40.397Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T02:48:54.775Z] INFO: Connecting to MSSQL database...
[2025-07-15T02:48:54.864Z] INFO: Database connected successfully
[2025-07-15T02:48:54.911Z] INFO: Database connection test successful
[2025-07-15T02:48:54.912Z] INFO: Database connected successfully
[2025-07-15T02:48:54.918Z] INFO: WebSocket service initialized
[2025-07-15T02:48:54.923Z] INFO: Server running on port 5001 in development mode
[2025-07-15T02:48:54.924Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T02:52:08.046Z] INFO: Connecting to MSSQL database...
[2025-07-15T02:52:08.112Z] INFO: Database connected successfully
[2025-07-15T02:52:08.160Z] INFO: Database connection test successful
[2025-07-15T02:52:08.161Z] INFO: Database connected successfully
[2025-07-15T02:52:08.169Z] INFO: WebSocket service initialized
[2025-07-15T02:52:08.174Z] INFO: Server running on port 5001 in development mode
[2025-07-15T02:52:08.175Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T02:54:37.378Z] INFO: Connecting to MSSQL database...
[2025-07-15T02:54:37.439Z] INFO: Database connected successfully
[2025-07-15T02:54:37.471Z] INFO: Database connection test successful
[2025-07-15T02:54:37.472Z] INFO: Database connected successfully
[2025-07-15T02:54:37.480Z] INFO: WebSocket service initialized
[2025-07-15T02:54:37.483Z] INFO: Server running on port 5001 in development mode
[2025-07-15T02:54:37.484Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T02:55:56.717Z] INFO: Connecting to MSSQL database...
[2025-07-15T02:55:56.942Z] INFO: Database connected successfully
[2025-07-15T02:55:57.019Z] INFO: Database connection test successful
[2025-07-15T02:55:57.021Z] INFO: Database connected successfully
[2025-07-15T02:55:57.035Z] INFO: WebSocket service initialized
[2025-07-15T02:55:57.043Z] INFO: Server running on http://127.0.0.1:5001 in development mode
[2025-07-15T02:55:57.047Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T02:56:44.560Z] INFO: Connecting to MSSQL database...
[2025-07-15T02:56:44.792Z] INFO: Database connected successfully
[2025-07-15T02:56:44.865Z] INFO: Database connection test successful
[2025-07-15T02:56:44.866Z] INFO: Database connected successfully
[2025-07-15T02:56:44.873Z] INFO: WebSocket service initialized
[2025-07-15T02:56:44.878Z] INFO: Server running on http://127.0.0.1:5002 in development mode
[2025-07-15T02:56:44.879Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T02:57:57.133Z] INFO: Connecting to MSSQL database...
[2025-07-15T02:57:57.204Z] INFO: Database connected successfully
[2025-07-15T02:57:57.238Z] INFO: Database connection test successful
[2025-07-15T02:57:57.239Z] INFO: Database connected successfully
[2025-07-15T02:57:57.244Z] INFO: WebSocket service initialized
[2025-07-15T02:57:57.245Z] INFO: WebSocket service initialized
[2025-07-15T02:57:57.250Z] INFO: Server running on http://127.0.0.1:5002 in development mode
[2025-07-15T02:57:57.251Z] INFO: Server is ready to accept connections
[2025-07-15T03:02:33.684Z] INFO: Connecting to MSSQL database...
[2025-07-15T03:02:33.804Z] INFO: Database connected successfully
[2025-07-15T03:02:33.872Z] INFO: Database connection test successful
[2025-07-15T03:02:33.873Z] INFO: Database connected successfully
[2025-07-15T03:02:33.879Z] INFO: WebSocket service initialized
[2025-07-15T03:02:33.881Z] INFO: WebSocket service initialized
[2025-07-15T03:02:33.885Z] INFO: Server running on http://localhost:5002 in development mode
[2025-07-15T03:02:33.886Z] INFO: Server is ready to accept connections
[2025-07-15T03:29:48.379Z] INFO: Connecting to MSSQL database...
[2025-07-15T03:29:48.477Z] INFO: Database connected successfully
[2025-07-15T03:29:48.540Z] INFO: Database connection test successful
[2025-07-15T03:29:48.541Z] INFO: Database connected successfully
[2025-07-15T03:29:48.550Z] INFO: WebSocket service initialized
[2025-07-15T03:29:48.551Z] INFO: WebSocket service initialized
[2025-07-15T03:29:48.555Z] INFO: Server running on http://localhost:8000 in development mode
[2025-07-15T03:29:48.556Z] INFO: Server is ready to accept connections
[2025-07-15T03:53:45.678Z] INFO: Connecting to MSSQL database...
[2025-07-15T03:53:45.816Z] INFO: Database connected successfully
[2025-07-15T03:53:45.911Z] INFO: Database connection test successful
[2025-07-15T03:53:45.912Z] INFO: Database connected successfully
[2025-07-15T03:53:45.920Z] INFO: WebSocket service initialized
[2025-07-15T03:53:45.922Z] INFO: WebSocket service initialized
[2025-07-15T03:53:45.941Z] INFO: Server running on http://localhost:8000 in development mode
[2025-07-15T03:53:45.950Z] INFO: Server is ready to accept connections
[2025-07-15T03:56:25.249Z] INFO: SIGINT received, shutting down gracefully
[2025-07-15T03:57:24.112Z] INFO: Connecting to MSSQL database...
[2025-07-15T03:57:24.221Z] INFO: Database connected successfully
[2025-07-15T03:57:24.270Z] INFO: Database connection test successful
[2025-07-15T03:57:24.272Z] INFO: Database connected successfully
[2025-07-15T03:57:24.280Z] INFO: WebSocket service initialized
[2025-07-15T03:57:24.281Z] INFO: WebSocket service initialized
[2025-07-15T03:57:24.285Z] INFO: Server running on http://localhost:8000 in development mode
[2025-07-15T03:57:24.286Z] INFO: Server is ready to accept connections
[2025-07-15T04:14:31.927Z] INFO: Connecting to MSSQL database...
[2025-07-15T04:14:32.050Z] INFO: Database connected successfully
[2025-07-15T04:14:32.137Z] INFO: Database connection test successful
[2025-07-15T04:14:32.138Z] INFO: Database connected successfully
[2025-07-15T04:14:32.147Z] INFO: WebSocket service initialized
[2025-07-15T04:14:32.148Z] INFO: WebSocket service initialized
[2025-07-15T04:14:32.156Z] INFO: Server running on http://localhost:8000 in development mode
[2025-07-15T04:14:32.159Z] INFO: Server is ready to accept connections
[2025-07-15T04:16:04.917Z] INFO: Connecting to MSSQL database...
[2025-07-15T04:16:05.380Z] INFO: Database connected successfully
[2025-07-15T04:16:05.457Z] INFO: Database connection test successful
[2025-07-15T04:18:14.781Z] INFO: Connecting to MSSQL database...
[2025-07-15T04:18:14.965Z] INFO: Database connected successfully
[2025-07-15T04:18:15.187Z] INFO: Database connection test successful
[2025-07-15T04:18:15.188Z] INFO: Database connected successfully
[2025-07-15T04:18:15.198Z] INFO: WebSocket service initialized
[2025-07-15T04:18:15.200Z] INFO: WebSocket service initialized
[2025-07-15T04:18:15.208Z] INFO: Server running on http://localhost:8000 in development mode
[2025-07-15T04:18:15.209Z] INFO: Server is ready to accept connections
[2025-07-15T04:18:19.346Z] INFO: SIGINT received, shutting down gracefully
[2025-07-15T04:20:27.354Z] INFO: Connecting to MSSQL database...
[2025-07-15T04:20:27.450Z] INFO: Database connected successfully
[2025-07-15T04:20:27.520Z] INFO: Database connection test successful
[2025-07-15T04:20:27.521Z] INFO: Database connected successfully
[2025-07-15T04:20:27.530Z] INFO: WebSocket service initialized
[2025-07-15T04:20:27.531Z] INFO: WebSocket service initialized
[2025-07-15T04:20:27.536Z] INFO: Server running on http://localhost:8000 in development mode
[2025-07-15T04:20:27.537Z] INFO: Server is ready to accept connections
[2025-07-15T04:20:38.422Z] INFO: ::1 - - [15/Jul/2025:04:20:38 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:20:38.435Z] INFO: ::1 - - [15/Jul/2025:04:20:38 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:20:38.454Z] INFO: ::1 - - [15/Jul/2025:04:20:38 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:20:38.464Z] INFO: ::1 - - [15/Jul/2025:04:20:38 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:20:38.524Z] INFO: ::1 - - [15/Jul/2025:04:20:38 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:20:38.674Z] INFO: ::1 - - [15/Jul/2025:04:20:38 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:20:39.786Z] INFO: ::1 - - [15/Jul/2025:04:20:39 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 401 51 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:20:39.806Z] INFO: ::1 - - [15/Jul/2025:04:20:39 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 401 51 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:20:39.833Z] INFO: ::1 - - [15/Jul/2025:04:20:39 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 401 51 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:20:39.853Z] INFO: ::1 - - [15/Jul/2025:04:20:39 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 401 51 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:20:39.893Z] INFO: ::1 - - [15/Jul/2025:04:20:39 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 401 51 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:20:39.911Z] INFO: ::1 - - [15/Jul/2025:04:20:39 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 401 51 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:20:40.508Z] INFO: ::1 - - [15/Jul/2025:04:20:40 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 401 51 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:20:40.562Z] INFO: ::1 - - [15/Jul/2025:04:20:40 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 401 51 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:20:40.578Z] INFO: ::1 - - [15/Jul/2025:04:20:40 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 401 51 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:20:40.590Z] INFO: ::1 - - [15/Jul/2025:04:20:40 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 401 51 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:20:40.598Z] INFO: ::1 - - [15/Jul/2025:04:20:40 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 401 51 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:20:40.608Z] INFO: ::1 - - [15/Jul/2025:04:20:40 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 401 51 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:23:42.031Z] INFO: Connecting to database...
[2025-07-15T04:23:42.039Z] INFO: Connecting to MSSQL database...
[2025-07-15T04:23:42.304Z] INFO: Database connected successfully
[2025-07-15T04:23:42.415Z] INFO: Database connection test successful
[2025-07-15T04:23:42.417Z] INFO: Database connected successfully
[2025-07-15T04:23:42.426Z] INFO: Activity Logs Server running on http://localhost:8000
[2025-07-15T04:23:42.430Z] INFO: Server is ready to accept connections
[2025-07-15T04:24:14.795Z] INFO: Connecting to MSSQL database...
[2025-07-15T04:24:14.948Z] INFO: Database connected successfully
[2025-07-15T04:24:15.057Z] INFO: Database connection test successful
[2025-07-15T04:26:24.579Z] INFO: Connecting to MSSQL database...
[2025-07-15T04:26:24.762Z] INFO: Database connected successfully
[2025-07-15T04:26:24.832Z] INFO: Database connection test successful
[2025-07-15T04:40:06.077Z] INFO: Connecting to MSSQL database...
[2025-07-15T04:40:06.597Z] INFO: Database connected successfully
[2025-07-15T04:40:06.748Z] INFO: Database connection test successful
[2025-07-15T04:40:06.751Z] INFO: Database connected successfully
[2025-07-15T04:40:06.759Z] INFO: WebSocket service initialized
[2025-07-15T04:40:06.792Z] INFO: WebSocket service initialized
[2025-07-15T04:40:06.799Z] INFO: Server running on http://localhost:8000 in development mode
[2025-07-15T04:40:06.900Z] INFO: Server is ready to accept connections
[2025-07-15T04:48:02.880Z] INFO: Connecting to MSSQL database...
[2025-07-15T04:48:03.084Z] INFO: Database connected successfully
[2025-07-15T04:48:03.159Z] INFO: Database connection test successful
[2025-07-15T04:48:03.160Z] INFO: Database connected successfully
[2025-07-15T04:48:03.170Z] INFO: WebSocket service initialized
[2025-07-15T04:48:03.171Z] INFO: WebSocket service initialized
[2025-07-15T04:48:03.180Z] INFO: Server running on http://localhost:8000 in development mode
[2025-07-15T04:48:03.182Z] INFO: Server is ready to accept connections
[2025-07-15T04:48:28.008Z] INFO: Connecting to MSSQL database...
[2025-07-15T04:48:28.134Z] INFO: Database connected successfully
[2025-07-15T04:48:28.212Z] INFO: Database connection test successful
[2025-07-15T04:48:28.213Z] INFO: Database connected successfully
[2025-07-15T04:48:28.224Z] INFO: WebSocket service initialized
[2025-07-15T04:48:28.225Z] INFO: WebSocket service initialized
[2025-07-15T04:48:28.232Z] INFO: Server running on http://localhost:8000 in development mode
[2025-07-15T04:48:28.237Z] INFO: Server is ready to accept connections
[2025-07-15T04:48:57.074Z] INFO: Connecting to MSSQL database...
[2025-07-15T04:48:57.203Z] INFO: Database connected successfully
[2025-07-15T04:48:57.267Z] INFO: Database connection test successful
[2025-07-15T04:48:57.268Z] INFO: Database connected successfully
[2025-07-15T04:48:57.276Z] INFO: WebSocket service initialized
[2025-07-15T04:48:57.277Z] INFO: WebSocket service initialized
[2025-07-15T04:48:57.281Z] ERROR: Server error: {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::","port":8000}
[2025-07-15T04:48:57.282Z] ERROR: Port 8000 is already in use
[2025-07-15T04:50:39.968Z] INFO: ::1 - - [15/Jul/2025:04:50:39 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093"
[2025-07-15T04:50:55.170Z] INFO: ::1 - - [15/Jul/2025:04:50:55 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:50:55.236Z] INFO: ::1 - - [15/Jul/2025:04:50:55 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:50:55.267Z] INFO: ::1 - - [15/Jul/2025:04:50:55 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:50:55.308Z] INFO: ::1 - - [15/Jul/2025:04:50:55 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:50:55.366Z] INFO: ::1 - - [15/Jul/2025:04:50:55 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:50:55.400Z] INFO: ::1 - - [15/Jul/2025:04:50:55 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:50:55.781Z] INFO: ::1 - - [15/Jul/2025:04:50:55 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 401 51 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:50:55.793Z] INFO: ::1 - - [15/Jul/2025:04:50:55 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 401 51 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:50:55.827Z] INFO: ::1 - - [15/Jul/2025:04:50:55 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 401 51 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:50:55.847Z] INFO: ::1 - - [15/Jul/2025:04:50:55 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 401 51 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:50:55.861Z] INFO: ::1 - - [15/Jul/2025:04:50:55 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 401 51 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:50:55.894Z] INFO: ::1 - - [15/Jul/2025:04:50:55 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 401 51 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:50:56.523Z] INFO: ::1 - - [15/Jul/2025:04:50:56 +0000] "GET /health HTTP/1.1" 200 103 "-" "axios/1.10.0"
[2025-07-15T04:50:56.556Z] INFO: ::1 - - [15/Jul/2025:04:50:56 +0000] "GET /api/health HTTP/1.1" 200 213 "-" "axios/1.10.0"
[2025-07-15T04:50:58.367Z] INFO: ::1 - - [15/Jul/2025:04:50:58 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "-" "axios/1.10.0"
[2025-07-15T04:51:16.753Z] INFO: ::1 - - [15/Jul/2025:04:51:16 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 401 51 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093"
[2025-07-15T04:51:58.417Z] INFO: ::1 - - [15/Jul/2025:04:51:58 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 403 54 "-" "axios/1.10.0"
[2025-07-15T04:51:58.446Z] INFO: ::1 - - [15/Jul/2025:04:51:58 +0000] "GET /api/admin/activity-logs HTTP/1.1" 403 54 "-" "axios/1.10.0"
[2025-07-15T04:53:52.103Z] INFO: ::1 - - [15/Jul/2025:04:53:52 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "-" "axios/1.10.0"
[2025-07-15T04:53:52.144Z] INFO: ::1 - - [15/Jul/2025:04:53:52 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 200 145 "-" "axios/1.10.0"
[2025-07-15T04:53:52.605Z] INFO: ::1 - - [15/Jul/2025:04:53:52 +0000] "GET /api/admin/activity-logs HTTP/1.1" 200 - "-" "axios/1.10.0"
[2025-07-15T04:53:54.099Z] INFO: ::1 - - [15/Jul/2025:04:53:54 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:53:55.736Z] INFO: ::1 - - [15/Jul/2025:04:53:55 +0000] "GET /favicon.ico HTTP/1.1" 404 45 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:53:58.977Z] INFO: ::1 - - [15/Jul/2025:04:53:58 +0000] "GET /api/health HTTP/1.1" 200 213 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:55:04.951Z] INFO: ::1 - - [15/Jul/2025:04:55:04 +0000] "GET /health HTTP/1.1" 200 103 "-" "axios/1.10.0"
[2025-07-15T04:55:04.989Z] INFO: ::1 - - [15/Jul/2025:04:55:04 +0000] "GET /api/health HTTP/1.1" 200 213 "-" "axios/1.10.0"
[2025-07-15T04:55:07.347Z] INFO: ::1 - - [15/Jul/2025:04:55:07 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "-" "axios/1.10.0"
[2025-07-15T04:56:21.304Z] INFO: ::1 - - [15/Jul/2025:04:56:21 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:25.322Z] INFO: ::1 - - [15/Jul/2025:04:56:25 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 200 145 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:25.336Z] INFO: ::1 - - [15/Jul/2025:04:56:25 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 200 140 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:25.359Z] INFO: ::1 - - [15/Jul/2025:04:56:25 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:25.374Z] INFO: ::1 - - [15/Jul/2025:04:56:25 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:25.446Z] INFO: ::1 - - [15/Jul/2025:04:56:25 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 200 414 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:25.470Z] INFO: ::1 - - [15/Jul/2025:04:56:25 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:26.142Z] INFO: ::1 - - [15/Jul/2025:04:56:26 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:26.158Z] INFO: ::1 - - [15/Jul/2025:04:56:26 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:26.174Z] INFO: ::1 - - [15/Jul/2025:04:56:26 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:26.196Z] INFO: ::1 - - [15/Jul/2025:04:56:26 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:26.276Z] INFO: ::1 - - [15/Jul/2025:04:56:26 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:26.382Z] INFO: ::1 - - [15/Jul/2025:04:56:26 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:26.519Z] INFO: ::1 - - [15/Jul/2025:04:56:26 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:26.533Z] INFO: ::1 - - [15/Jul/2025:04:56:26 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:26.557Z] INFO: ::1 - - [15/Jul/2025:04:56:26 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:26.573Z] INFO: ::1 - - [15/Jul/2025:04:56:26 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:26.586Z] INFO: ::1 - - [15/Jul/2025:04:56:26 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:26.666Z] INFO: ::1 - - [15/Jul/2025:04:56:26 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:28.107Z] INFO: ::1 - - [15/Jul/2025:04:56:28 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:28.126Z] INFO: ::1 - - [15/Jul/2025:04:56:28 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:28.139Z] INFO: ::1 - - [15/Jul/2025:04:56:28 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:28.160Z] INFO: ::1 - - [15/Jul/2025:04:56:28 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:28.179Z] INFO: ::1 - - [15/Jul/2025:04:56:28 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:28.200Z] INFO: ::1 - - [15/Jul/2025:04:56:28 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:31.005Z] INFO: ::1 - - [15/Jul/2025:04:56:31 +0000] "GET /api/products/categories HTTP/1.1" 500 42 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:31.163Z] ERROR: ProductService.getProducts error:
[2025-07-15T04:56:31.213Z] ERROR: ProductController.getProducts error:
[2025-07-15T04:56:31.315Z] INFO: ::1 - - [15/Jul/2025:04:56:31 +0000] "GET /api/products?page=1&limit=12&sortBy=name&sortDirection=ASC HTTP/1.1" 500 121 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:31.414Z] INFO: ::1 - - [15/Jul/2025:04:56:31 +0000] "GET /api/products/categories HTTP/1.1" 500 42 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:31.440Z] ERROR: ProductService.getProducts error:
[2025-07-15T04:56:31.444Z] ERROR: ProductController.getProducts error:
[2025-07-15T04:56:31.462Z] INFO: ::1 - - [15/Jul/2025:04:56:31 +0000] "GET /api/products?page=1&limit=12&sortBy=name&sortDirection=ASC HTTP/1.1" 500 121 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:31.691Z] INFO: ::1 - - [15/Jul/2025:04:56:31 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:31.724Z] INFO: ::1 - - [15/Jul/2025:04:56:31 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:31.759Z] INFO: ::1 - - [15/Jul/2025:04:56:31 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:31.775Z] INFO: ::1 - - [15/Jul/2025:04:56:31 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:31.804Z] INFO: ::1 - - [15/Jul/2025:04:56:31 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:56:31.836Z] INFO: ::1 - - [15/Jul/2025:04:56:31 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:57:28.603Z] INFO: ::1 - - [15/Jul/2025:04:57:28 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:57:28.617Z] INFO: ::1 - - [15/Jul/2025:04:57:28 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:57:28.663Z] INFO: ::1 - - [15/Jul/2025:04:57:28 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:57:29.155Z] INFO: ::1 - - [15/Jul/2025:04:57:29 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:57:31.121Z] INFO: ::1 - - [15/Jul/2025:04:57:31 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:57:31.199Z] INFO: ::1 - - [15/Jul/2025:04:57:31 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:59:05.449Z] INFO: ::1 - - [15/Jul/2025:04:59:05 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T04:59:07.621Z] INFO: ::1 - - [15/Jul/2025:04:59:07 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:03:22.612Z] INFO: Connecting to MSSQL database...
[2025-07-15T05:03:24.025Z] INFO: Database connected successfully
[2025-07-15T05:03:24.320Z] INFO: Database connection test successful
[2025-07-15T05:03:24.370Z] INFO: Database connected successfully
[2025-07-15T05:03:24.472Z] INFO: WebSocket service initialized
[2025-07-15T05:03:24.524Z] INFO: WebSocket service initialized
[2025-07-15T05:03:24.587Z] INFO: Server running on http://localhost:8000 in development mode
[2025-07-15T05:03:24.610Z] INFO: Server is ready to accept connections
[2025-07-15T05:03:25.247Z] INFO: ::1 - - [15/Jul/2025:05:03:25 +0000] "GET /health HTTP/1.1" 200 102 "-" "axios/1.10.0"
[2025-07-15T05:03:25.322Z] INFO: ::1 - - [15/Jul/2025:05:03:25 +0000] "GET /api/health HTTP/1.1" 200 211 "-" "axios/1.10.0"
[2025-07-15T05:03:27.795Z] INFO: ::1 - - [15/Jul/2025:05:03:27 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "-" "axios/1.10.0"
[2025-07-15T05:04:21.283Z] INFO: Connecting to MSSQL database...
[2025-07-15T05:04:21.525Z] INFO: Database connected successfully
[2025-07-15T05:04:21.594Z] INFO: Database connection test successful
[2025-07-15T05:04:21.595Z] INFO: Database connected successfully
[2025-07-15T05:04:21.605Z] INFO: WebSocket service initialized
[2025-07-15T05:04:21.613Z] INFO: WebSocket service initialized
[2025-07-15T05:04:21.622Z] INFO: Server running on http://localhost:8000 in development mode
[2025-07-15T05:04:21.623Z] INFO: Server is ready to accept connections
[2025-07-15T05:04:32.818Z] INFO: ::1 - - [15/Jul/2025:05:04:32 +0000] "GET /api/cors-test HTTP/1.1" 200 234 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:04:37.331Z] INFO: ::1 - - [15/Jul/2025:05:04:37 +0000] "GET /health HTTP/1.1" 200 102 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:28.221Z] INFO: ::1 - - [15/Jul/2025:05:05:28 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:30.527Z] INFO: ::1 - - [15/Jul/2025:05:05:30 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 200 145 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:30.539Z] INFO: ::1 - - [15/Jul/2025:05:05:30 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 200 140 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:30.553Z] INFO: ::1 - - [15/Jul/2025:05:05:30 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:30.561Z] INFO: ::1 - - [15/Jul/2025:05:05:30 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:30.589Z] INFO: ::1 - - [15/Jul/2025:05:05:30 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 200 414 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:30.604Z] INFO: ::1 - - [15/Jul/2025:05:05:30 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:38.146Z] INFO: ::1 - - [15/Jul/2025:05:05:38 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:38.159Z] INFO: ::1 - - [15/Jul/2025:05:05:38 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:38.176Z] INFO: ::1 - - [15/Jul/2025:05:05:38 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:38.189Z] INFO: ::1 - - [15/Jul/2025:05:05:38 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:38.197Z] INFO: ::1 - - [15/Jul/2025:05:05:38 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:38.212Z] INFO: ::1 - - [15/Jul/2025:05:05:38 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:39.575Z] INFO: ::1 - - [15/Jul/2025:05:05:39 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:39.586Z] INFO: ::1 - - [15/Jul/2025:05:05:39 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:39.597Z] INFO: ::1 - - [15/Jul/2025:05:05:39 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:39.616Z] INFO: ::1 - - [15/Jul/2025:05:05:39 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:39.625Z] INFO: ::1 - - [15/Jul/2025:05:05:39 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:39.639Z] INFO: ::1 - - [15/Jul/2025:05:05:39 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:51.849Z] INFO: ::1 - - [15/Jul/2025:05:05:51 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:51.858Z] INFO: ::1 - - [15/Jul/2025:05:05:51 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:51.873Z] INFO: ::1 - - [15/Jul/2025:05:05:51 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:51.883Z] INFO: ::1 - - [15/Jul/2025:05:05:51 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:51.896Z] INFO: ::1 - - [15/Jul/2025:05:05:51 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:05:51.916Z] INFO: ::1 - - [15/Jul/2025:05:05:51 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:06.131Z] INFO: ::1 - - [15/Jul/2025:05:06:06 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:06.148Z] INFO: ::1 - - [15/Jul/2025:05:06:06 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:06.181Z] INFO: ::1 - - [15/Jul/2025:05:06:06 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:06.193Z] INFO: ::1 - - [15/Jul/2025:05:06:06 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:06.262Z] INFO: ::1 - - [15/Jul/2025:05:06:06 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:06.284Z] INFO: ::1 - - [15/Jul/2025:05:06:06 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:06.822Z] INFO: ::1 - - [15/Jul/2025:05:06:06 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:06.843Z] INFO: ::1 - - [15/Jul/2025:05:06:06 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:06.863Z] INFO: ::1 - - [15/Jul/2025:05:06:06 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:06.893Z] INFO: ::1 - - [15/Jul/2025:05:06:06 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:06.915Z] INFO: ::1 - - [15/Jul/2025:05:06:06 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:06.931Z] INFO: ::1 - - [15/Jul/2025:05:06:06 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:07.901Z] INFO: ::1 - - [15/Jul/2025:05:06:07 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:07.928Z] INFO: ::1 - - [15/Jul/2025:05:06:07 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:07.968Z] INFO: ::1 - - [15/Jul/2025:05:06:07 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:07.982Z] INFO: ::1 - - [15/Jul/2025:05:06:07 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:07.994Z] INFO: ::1 - - [15/Jul/2025:05:06:07 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:08.017Z] INFO: ::1 - - [15/Jul/2025:05:06:08 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:08.522Z] INFO: ::1 - - [15/Jul/2025:05:06:08 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:08.541Z] INFO: ::1 - - [15/Jul/2025:05:06:08 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:08.552Z] INFO: ::1 - - [15/Jul/2025:05:06:08 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:08.571Z] INFO: ::1 - - [15/Jul/2025:05:06:08 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:08.588Z] INFO: ::1 - - [15/Jul/2025:05:06:08 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:08.629Z] INFO: ::1 - - [15/Jul/2025:05:06:08 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:08.963Z] INFO: ::1 - - [15/Jul/2025:05:06:08 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:08.971Z] INFO: ::1 - - [15/Jul/2025:05:06:08 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:08.993Z] INFO: ::1 - - [15/Jul/2025:05:06:08 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:09.102Z] INFO: ::1 - - [15/Jul/2025:05:06:09 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:09.201Z] INFO: ::1 - - [15/Jul/2025:05:06:09 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:09.233Z] INFO: ::1 - - [15/Jul/2025:05:06:09 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:16.655Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:06:16.656Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:06:16.659Z] INFO: ::1 - - [15/Jul/2025:05:06:16 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:16.675Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:06:16.677Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:06:16.681Z] INFO: ::1 - - [15/Jul/2025:05:06:16 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:21.606Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:06:21.609Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:06:21.613Z] INFO: ::1 - - [15/Jul/2025:05:06:21 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:21.635Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:06:21.639Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:06:21.643Z] INFO: ::1 - - [15/Jul/2025:05:06:21 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:22.761Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:06:22.762Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:06:22.766Z] INFO: ::1 - - [15/Jul/2025:05:06:22 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:46.190Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:06:46.191Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:06:46.193Z] INFO: ::1 - - [15/Jul/2025:05:06:46 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:52.888Z] ERROR: ProductService.getProducts error:
[2025-07-15T05:06:52.891Z] ERROR: ProductController.getProducts error:
[2025-07-15T05:06:52.894Z] INFO: ::1 - - [15/Jul/2025:05:06:52 +0000] "GET /api/products?page=1&limit=12&sortBy=name&sortDirection=ASC HTTP/1.1" 500 121 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:52.943Z] INFO: ::1 - - [15/Jul/2025:05:06:52 +0000] "GET /api/products/categories HTTP/1.1" 500 42 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:52.960Z] ERROR: ProductService.getProducts error:
[2025-07-15T05:06:52.964Z] ERROR: ProductController.getProducts error:
[2025-07-15T05:06:52.984Z] INFO: ::1 - - [15/Jul/2025:05:06:52 +0000] "GET /api/products?page=1&limit=12&sortBy=name&sortDirection=ASC HTTP/1.1" 500 121 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:06:53.040Z] INFO: ::1 - - [15/Jul/2025:05:06:53 +0000] "GET /api/products/categories HTTP/1.1" 500 42 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:08:57.397Z] INFO: Connecting to MSSQL database...
[2025-07-15T05:08:57.492Z] INFO: Database connected successfully
[2025-07-15T05:08:57.543Z] INFO: Database connection test successful
[2025-07-15T05:08:57.544Z] INFO: Database connected successfully
[2025-07-15T05:08:57.552Z] INFO: WebSocket service initialized
[2025-07-15T05:08:57.553Z] INFO: WebSocket service initialized
[2025-07-15T05:08:57.561Z] INFO: Server running on http://localhost:8000 in development mode
[2025-07-15T05:08:57.562Z] INFO: Server is ready to accept connections
[2025-07-15T05:09:18.889Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:09:18.891Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:09:18.906Z] INFO: ::1 - - [15/Jul/2025:05:09:18 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:09:19.702Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:09:19.707Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:09:19.710Z] INFO: ::1 - - [15/Jul/2025:05:09:19 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:09:20.414Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:09:20.418Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:09:20.424Z] INFO: ::1 - - [15/Jul/2025:05:09:20 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:09:21.019Z] INFO: ::1 - - [15/Jul/2025:05:09:21 +0000] "GET /health HTTP/1.1" 200 102 "-" "axios/1.10.0"
[2025-07-15T05:09:21.054Z] INFO: ::1 - - [15/Jul/2025:05:09:21 +0000] "GET /api/health HTTP/1.1" 200 212 "-" "axios/1.10.0"
[2025-07-15T05:09:22.395Z] INFO: ::1 - - [15/Jul/2025:05:09:22 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "-" "axios/1.10.0"
[2025-07-15T05:10:08.744Z] INFO: ::1 - - [15/Jul/2025:05:10:08 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "-" "axios/1.10.0"
[2025-07-15T05:10:08.789Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:10:08.790Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:10:08.793Z] INFO: ::1 - - [15/Jul/2025:05:10:08 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "-" "axios/1.10.0"
[2025-07-15T05:10:42.097Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:10:42.100Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:10:42.114Z] INFO: ::1 - - [15/Jul/2025:05:10:42 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:11:08.205Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:11:08.207Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:11:08.211Z] INFO: ::1 - - [15/Jul/2025:05:11:08 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:12:48.540Z] INFO: Connecting to MSSQL database...
[2025-07-15T05:12:48.767Z] INFO: Database connected successfully
[2025-07-15T05:12:48.849Z] INFO: Database connection test successful
[2025-07-15T05:12:48.851Z] INFO: Database connected successfully
[2025-07-15T05:12:48.860Z] INFO: WebSocket service initialized
[2025-07-15T05:12:48.868Z] INFO: WebSocket service initialized
[2025-07-15T05:12:48.876Z] INFO: Server running on http://localhost:8000 in development mode
[2025-07-15T05:12:48.878Z] INFO: Server is ready to accept connections
[2025-07-15T05:12:48.998Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:12:49.001Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:12:49.015Z] INFO: ::ffff:127.0.0.1 - - [15/Jul/2025:05:12:49 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:12:49.039Z] ERROR: ProductService.getProducts error:
[2025-07-15T05:12:49.041Z] ERROR: ProductController.getProducts error:
[2025-07-15T05:12:49.053Z] INFO: ::ffff:127.0.0.1 - - [15/Jul/2025:05:12:49 +0000] "GET /api/products?page=1&limit=12&sortBy=name&sortDirection=ASC HTTP/1.1" 500 121 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:12:49.087Z] INFO: ::ffff:127.0.0.1 - - [15/Jul/2025:05:12:49 +0000] "GET /api/products/categories HTTP/1.1" 500 42 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:12:49.099Z] ERROR: ProductService.getProducts error:
[2025-07-15T05:12:49.105Z] ERROR: ProductController.getProducts error:
[2025-07-15T05:12:49.109Z] INFO: ::ffff:127.0.0.1 - - [15/Jul/2025:05:12:49 +0000] "GET /api/products?page=1&limit=12&sortBy=name&sortDirection=ASC HTTP/1.1" 500 121 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:12:49.133Z] INFO: ::ffff:127.0.0.1 - - [15/Jul/2025:05:12:49 +0000] "GET /api/products/categories HTTP/1.1" 500 42 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:12:49.978Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:12:49.981Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:12:49.985Z] INFO: ::ffff:127.0.0.1 - - [15/Jul/2025:05:12:49 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:12:49.994Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:12:49.996Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:12:50.002Z] INFO: ::ffff:127.0.0.1 - - [15/Jul/2025:05:12:50 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:12:53.032Z] INFO: ::1 - - [15/Jul/2025:05:12:53 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "-" "axios/1.10.0"
[2025-07-15T05:12:53.066Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:12:53.067Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:12:53.074Z] INFO: ::1 - - [15/Jul/2025:05:12:53 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "-" "axios/1.10.0"
[2025-07-15T05:12:53.527Z] INFO: ::ffff:127.0.0.1 - - [15/Jul/2025:05:12:53 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:12:53.541Z] INFO: ::ffff:127.0.0.1 - - [15/Jul/2025:05:12:53 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:12:53.573Z] INFO: ::ffff:127.0.0.1 - - [15/Jul/2025:05:12:53 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:12:53.615Z] INFO: ::ffff:127.0.0.1 - - [15/Jul/2025:05:12:53 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:12:53.723Z] INFO: ::ffff:127.0.0.1 - - [15/Jul/2025:05:12:53 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 200 416 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:12:53.762Z] INFO: ::ffff:127.0.0.1 - - [15/Jul/2025:05:12:53 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:13:37.821Z] INFO: Connecting to MSSQL database...
[2025-07-15T05:13:38.012Z] INFO: Database connected successfully
[2025-07-15T05:13:38.071Z] INFO: Database connection test successful
[2025-07-15T05:13:38.189Z] ERROR: Error executing custom query: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'TotalPrice'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":12}},"name":"RequestError","number":207,"lineNumber":12,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","precedingErrors":[{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'CustomerEmail'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":17}},"name":"RequestError","number":207,"lineNumber":17,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'CustomerName'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":17}},"name":"RequestError","number":207,"lineNumber":17,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'CustomerPhone'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":17}},"name":"RequestError","number":207,"lineNumber":17,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'PaymentReference'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":17}},"name":"RequestError","number":207,"lineNumber":17,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'DiscountAmount'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":17}},"name":"RequestError","number":207,"lineNumber":17,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'Currency'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":17}},"name":"RequestError","number":207,"lineNumber":17,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'CustomerEmail'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":3}},"name":"RequestError","number":207,"lineNumber":3,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'CustomerName'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":3}},"name":"RequestError","number":207,"lineNumber":3,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'CustomerPhone'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":3}},"name":"RequestError","number":207,"lineNumber":3,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'PaymentReference'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":5}},"name":"RequestError","number":207,"lineNumber":5,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'DiscountAmount'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":5}},"name":"RequestError","number":207,"lineNumber":5,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'Currency'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":6}},"name":"RequestError","number":207,"lineNumber":6,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'CustomerName'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":10}},"name":"RequestError","number":207,"lineNumber":10,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""}]}
[2025-07-15T05:13:38.221Z] ERROR: Error getting orders with pagination: {"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'TotalPrice'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":12}},"name":"RequestError","number":207,"lineNumber":12,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","precedingErrors":[{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'CustomerEmail'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":17}},"name":"RequestError","number":207,"lineNumber":17,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'CustomerName'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":17}},"name":"RequestError","number":207,"lineNumber":17,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'CustomerPhone'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":17}},"name":"RequestError","number":207,"lineNumber":17,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'PaymentReference'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":17}},"name":"RequestError","number":207,"lineNumber":17,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'DiscountAmount'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":17}},"name":"RequestError","number":207,"lineNumber":17,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'Currency'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":17}},"name":"RequestError","number":207,"lineNumber":17,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'CustomerEmail'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":3}},"name":"RequestError","number":207,"lineNumber":3,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'CustomerName'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":3}},"name":"RequestError","number":207,"lineNumber":3,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'CustomerPhone'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":3}},"name":"RequestError","number":207,"lineNumber":3,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'PaymentReference'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":5}},"name":"RequestError","number":207,"lineNumber":5,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'DiscountAmount'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":5}},"name":"RequestError","number":207,"lineNumber":5,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'Currency'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":6}},"name":"RequestError","number":207,"lineNumber":6,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""},{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":207,"state":1,"class":16,"message":"Invalid column name 'CustomerName'.","serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":"","lineNumber":10}},"name":"RequestError","number":207,"lineNumber":10,"state":1,"class":16,"serverName":"DESKTOP-DPNS718\\SQLEXPRESS","procName":""}]}
[2025-07-15T05:14:24.567Z] INFO: ::1 - - [15/Jul/2025:05:14:24 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:14:24.579Z] INFO: ::1 - - [15/Jul/2025:05:14:24 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:14:24.598Z] INFO: ::1 - - [15/Jul/2025:05:14:24 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:14:24.616Z] INFO: ::1 - - [15/Jul/2025:05:14:24 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:14:24.712Z] INFO: ::1 - - [15/Jul/2025:05:14:24 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:14:24.736Z] INFO: ::1 - - [15/Jul/2025:05:14:24 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:14:26.256Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:14:26.260Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:14:26.266Z] INFO: ::1 - - [15/Jul/2025:05:14:26 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:14:26.279Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:14:26.280Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:14:26.283Z] INFO: ::1 - - [15/Jul/2025:05:14:26 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:14:27.831Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:14:27.832Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:14:27.836Z] INFO: ::1 - - [15/Jul/2025:05:14:27 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:14:28.432Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:14:28.434Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:14:28.440Z] INFO: ::1 - - [15/Jul/2025:05:14:28 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:14:29.110Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:14:29.124Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:14:29.127Z] INFO: ::1 - - [15/Jul/2025:05:14:29 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:14:37.697Z] INFO: Connecting to MSSQL database...
[2025-07-15T05:14:37.802Z] INFO: Database connected successfully
[2025-07-15T05:14:37.850Z] INFO: Database connection test successful
[2025-07-15T05:14:42.225Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:14:42.230Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:14:42.235Z] INFO: ::1 - - [15/Jul/2025:05:14:42 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:14:42.262Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:14:42.294Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:14:42.298Z] INFO: ::1 - - [15/Jul/2025:05:14:42 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:14:43.714Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:14:43.716Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:14:43.718Z] INFO: ::1 - - [15/Jul/2025:05:14:43 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:14:44.288Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:14:44.294Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:14:44.297Z] INFO: ::1 - - [15/Jul/2025:05:14:44 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:16:27.836Z] INFO: Connecting to MSSQL database...
[2025-07-15T05:16:28.003Z] INFO: Database connected successfully
[2025-07-15T05:16:28.069Z] INFO: Database connection test successful
[2025-07-15T05:16:28.072Z] INFO: Database connected successfully
[2025-07-15T05:16:28.082Z] INFO: WebSocket service initialized
[2025-07-15T05:16:28.083Z] INFO: WebSocket service initialized
[2025-07-15T05:16:28.089Z] INFO: Server running on http://localhost:8000 in development mode
[2025-07-15T05:16:28.098Z] INFO: Server is ready to accept connections
[2025-07-15T05:16:32.153Z] INFO: Connecting to MSSQL database...
[2025-07-15T05:16:32.448Z] INFO: Database connected successfully
[2025-07-15T05:16:32.546Z] INFO: Database connection test successful
[2025-07-15T05:16:42.139Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:16:42.140Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:16:42.157Z] INFO: ::1 - - [15/Jul/2025:05:16:42 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:16:42.614Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:16:42.615Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:16:42.619Z] INFO: ::1 - - [15/Jul/2025:05:16:42 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:16:59.260Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:17:00.075Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:17:00.838Z] INFO: ::1 - - [15/Jul/2025:05:17:00 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:17:09.974Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:17:10.277Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:17:10.863Z] INFO: ::1 - - [15/Jul/2025:05:17:10 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:17:12.249Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:17:12.990Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:17:13.556Z] INFO: ::1 - - [15/Jul/2025:05:17:13 +0000] "GET /api/orders?page=1&limit=10&status=Pending HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:17:15.873Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:17:16.441Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:17:17.101Z] INFO: ::1 - - [15/Jul/2025:05:17:17 +0000] "GET /api/orders?page=1&limit=10&status=Pending&paymentStatus=Pending HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:17:18.183Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:17:18.782Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:17:19.349Z] INFO: ::1 - - [15/Jul/2025:05:17:19 +0000] "GET /api/orders?page=1&limit=10&status=Pending&paymentStatus=Failed HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:17:48.061Z] INFO: ::1 - - [15/Jul/2025:05:17:48 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "-" "axios/1.10.0"
[2025-07-15T05:17:48.106Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:17:48.157Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:17:48.169Z] INFO: ::1 - - [15/Jul/2025:05:17:48 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "-" "axios/1.10.0"
[2025-07-15T05:17:55.039Z] INFO: ::1 - - [15/Jul/2025:05:17:55 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 401 51 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:18:40.600Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:18:40.601Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:18:40.603Z] INFO: ::1 - - [15/Jul/2025:05:18:40 +0000] "GET /api/orders?page=1&limit=10&status=Pending&paymentStatus=Failed HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:18:41.212Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:18:41.213Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:18:41.215Z] INFO: ::1 - - [15/Jul/2025:05:18:41 +0000] "GET /api/orders?page=1&limit=10&status=Pending&paymentStatus=Failed HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:18:46.520Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:18:46.521Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:18:46.524Z] INFO: ::1 - - [15/Jul/2025:05:18:46 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:18:46.534Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:18:46.535Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:18:46.537Z] INFO: ::1 - - [15/Jul/2025:05:18:46 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:18:47.764Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:18:47.765Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:18:47.768Z] INFO: ::1 - - [15/Jul/2025:05:18:47 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:18:48.157Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:18:48.158Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:18:48.159Z] INFO: ::1 - - [15/Jul/2025:05:18:48 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:18:48.342Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:18:48.342Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:18:48.344Z] INFO: ::1 - - [15/Jul/2025:05:18:48 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:18:50.241Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:18:50.242Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:18:50.243Z] INFO: ::1 - - [15/Jul/2025:05:18:50 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:19:00.067Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:19:00.072Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:19:00.075Z] INFO: ::1 - - [15/Jul/2025:05:19:00 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:19:47.309Z] INFO: ::1 - - [15/Jul/2025:05:19:47 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "-" "axios/1.10.0"
[2025-07-15T05:19:47.330Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:19:47.331Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:19:47.335Z] INFO: ::1 - - [15/Jul/2025:05:19:47 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "-" "axios/1.10.0"
[2025-07-15T05:20:11.683Z] INFO: Connecting to MSSQL database...
[2025-07-15T05:20:11.793Z] INFO: Database connected successfully
[2025-07-15T05:20:11.879Z] INFO: Database connection test successful
[2025-07-15T05:20:11.880Z] INFO: Database connected successfully
[2025-07-15T05:20:11.887Z] INFO: WebSocket service initialized
[2025-07-15T05:20:11.894Z] INFO: WebSocket service initialized
[2025-07-15T05:20:11.900Z] INFO: Server running on http://localhost:8000 in development mode
[2025-07-15T05:20:11.902Z] INFO: Server is ready to accept connections
[2025-07-15T05:20:13.047Z] INFO: ::1 - - [15/Jul/2025:05:20:13 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "-" "axios/1.10.0"
[2025-07-15T05:20:13.068Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:20:13.075Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:20:13.077Z] INFO: ::1 - - [15/Jul/2025:05:20:13 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "-" "axios/1.10.0"
[2025-07-15T05:20:48.344Z] INFO: Connecting to MSSQL database...
[2025-07-15T05:20:48.436Z] INFO: Database connected successfully
[2025-07-15T05:20:48.468Z] INFO: Database connection test successful
[2025-07-15T05:22:05.320Z] INFO: Connecting to MSSQL database...
[2025-07-15T05:22:05.439Z] INFO: Database connected successfully
[2025-07-15T05:22:05.485Z] INFO: Database connection test successful
[2025-07-15T05:22:05.486Z] INFO: Database connected successfully
[2025-07-15T05:22:05.490Z] INFO: WebSocket service initialized
[2025-07-15T05:22:05.491Z] INFO: WebSocket service initialized
[2025-07-15T05:22:05.496Z] INFO: Server running on http://localhost:8000 in development mode
[2025-07-15T05:22:05.497Z] INFO: Server is ready to accept connections
[2025-07-15T05:22:12.502Z] INFO: ::1 - - [15/Jul/2025:05:22:12 +0000] "POST /api/auth/login HTTP/1.1" 200 624 "-" "axios/1.10.0"
[2025-07-15T05:22:12.539Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:22:12.542Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:22:12.544Z] INFO: ::1 - - [15/Jul/2025:05:22:12 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "-" "axios/1.10.0"
[2025-07-15T05:22:49.727Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:22:50.114Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:22:50.696Z] INFO: ::1 - - [15/Jul/2025:05:22:50 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:27:11.419Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:27:11.420Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:27:11.422Z] INFO: ::1 - - [15/Jul/2025:05:27:11 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:32:03.612Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:32:04.073Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:32:04.744Z] INFO: ::1 - - [15/Jul/2025:05:32:04 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:32:13.771Z] ERROR: OrderService.getOrders error:
[2025-07-15T05:32:14.185Z] ERROR: OrderController.getOrders error:
[2025-07-15T05:32:14.634Z] INFO: ::1 - - [15/Jul/2025:05:32:14 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 500 115 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T05:37:06.506Z] INFO: Connecting to MSSQL database...
[2025-07-15T05:37:06.565Z] INFO: Database connected successfully
[2025-07-15T05:37:06.606Z] INFO: Database connection test successful
[2025-07-15T05:37:06.607Z] INFO: Database connected successfully
[2025-07-15T05:37:06.612Z] INFO: WebSocket service initialized
[2025-07-15T05:37:06.613Z] INFO: WebSocket service initialized
[2025-07-15T05:37:06.617Z] INFO: Server running on http://localhost:8000 in development mode
[2025-07-15T05:37:06.618Z] INFO: Server is ready to accept connections
[2025-07-15T06:13:42.540Z] INFO: ::1 - - [15/Jul/2025:06:13:42 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T06:15:46.805Z] INFO: Connecting to MSSQL database...
[2025-07-15T06:15:46.867Z] INFO: Database connected successfully
[2025-07-15T06:15:46.929Z] INFO: Database connection test successful
[2025-07-15T06:15:46.930Z] INFO: Database connected successfully
[2025-07-15T06:15:46.936Z] INFO: WebSocket service initialized
[2025-07-15T06:15:46.937Z] INFO: WebSocket service initialized
[2025-07-15T06:15:46.939Z] ERROR: Server error: {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::","port":8000}
[2025-07-15T06:15:46.940Z] ERROR: Port 8000 is already in use
[2025-07-15T06:16:08.678Z] INFO: ::1 - - [15/Jul/2025:06:16:08 +0000] "POST /api/auth/login HTTP/1.1" 500 42 "-" "axios/1.10.0"
[2025-07-15T06:16:26.024Z] INFO: ::1 - - [15/Jul/2025:06:16:26 +0000] "POST /api/auth/login HTTP/1.1" 500 42 "-" "axios/1.10.0"
[2025-07-15T06:16:52.857Z] INFO: ::1 - - [15/Jul/2025:06:16:52 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 401 51 "-" "axios/1.10.0"
[2025-07-15T06:17:02.397Z] INFO: ::1 - - [15/Jul/2025:06:17:02 +0000] "GET /health HTTP/1.1" 200 104 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T06:17:20.536Z] INFO: Connecting to MSSQL database...
[2025-07-15T06:17:20.595Z] INFO: Database connected successfully
[2025-07-15T06:17:20.625Z] INFO: Database connection test successful
[2025-07-15T06:17:20.625Z] INFO: Database connected successfully
[2025-07-15T06:17:20.630Z] INFO: WebSocket service initialized
[2025-07-15T06:17:20.630Z] INFO: WebSocket service initialized
[2025-07-15T06:17:20.633Z] ERROR: Server error: {"code":"EADDRINUSE","errno":-4091,"syscall":"listen","address":"::","port":8000}
[2025-07-15T06:17:20.634Z] ERROR: Port 8000 is already in use
[2025-07-15T06:21:51.414Z] INFO: ::1 - - [15/Jul/2025:06:21:51 +0000] "POST /api/auth/login HTTP/1.1" 500 42 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T06:21:53.562Z] INFO: ::1 - - [15/Jul/2025:06:21:53 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T06:21:53.575Z] INFO: ::1 - - [15/Jul/2025:06:21:53 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T06:21:53.591Z] INFO: ::1 - - [15/Jul/2025:06:21:53 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T06:21:53.605Z] INFO: ::1 - - [15/Jul/2025:06:21:53 +0000] "GET /api/admin/activity-logs/actions HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T06:21:53.659Z] INFO: ::1 - - [15/Jul/2025:06:21:53 +0000] "GET /api/admin/activity-logs/stats HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T06:21:53.664Z] INFO: ::1 - - [15/Jul/2025:06:21:53 +0000] "GET /api/admin/activity-logs/entity-types HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T06:22:23.067Z] INFO: ::1 - - [15/Jul/2025:06:22:23 +0000] "POST /api/auth/login HTTP/1.1" 500 42 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T06:22:26.096Z] INFO: ::1 - - [15/Jul/2025:06:22:26 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T06:22:26.099Z] INFO: ::1 - - [15/Jul/2025:06:22:26 +0000] "GET /api/orders?page=1&limit=10 HTTP/1.1" 403 54 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T06:29:17.185Z] INFO: Connecting to MSSQL database...
[2025-07-15T06:29:17.269Z] INFO: Database connected successfully
[2025-07-15T06:29:17.348Z] INFO: Database connection test successful
[2025-07-15T06:29:17.349Z] INFO: Database connected successfully
[2025-07-15T06:29:17.356Z] INFO: WebSocket service initialized
[2025-07-15T06:29:17.362Z] INFO: Server running on port 5001 in development mode
[2025-07-15T06:29:17.363Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T06:30:11.854Z] INFO: SIGINT received, shutting down gracefully
[2025-07-15T06:30:21.256Z] INFO: Connecting to MSSQL database...
[2025-07-15T06:30:21.331Z] INFO: Database connected successfully
[2025-07-15T06:30:21.366Z] INFO: Database connection test successful
[2025-07-15T06:30:21.367Z] INFO: Database connected successfully
[2025-07-15T06:30:21.373Z] INFO: WebSocket service initialized
[2025-07-15T06:30:21.377Z] INFO: Server running on port 8000 in development mode
[2025-07-15T06:30:21.378Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T06:38:45.915Z] INFO: Connecting to MSSQL database...
[2025-07-15T06:38:46.136Z] INFO: Database connected successfully
[2025-07-15T06:38:46.193Z] INFO: Database connection test successful
[2025-07-15T06:38:46.194Z] INFO: Database connected successfully
[2025-07-15T06:38:46.200Z] INFO: WebSocket service initialized
[2025-07-15T06:38:46.204Z] INFO: Server running on port 8000 in development mode
[2025-07-15T06:38:46.207Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T06:52:08.458Z] INFO: Connecting to MSSQL database...
[2025-07-15T06:52:08.542Z] INFO: Database connected successfully
[2025-07-15T06:52:08.598Z] INFO: Database connection test successful
[2025-07-15T06:52:08.599Z] INFO: Database connected successfully
[2025-07-15T06:52:08.607Z] INFO: WebSocket service initialized
[2025-07-15T06:52:08.611Z] INFO: Server running on port 8000 in development mode
[2025-07-15T06:52:08.612Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T06:56:23.066Z] INFO: Connecting to MSSQL database...
[2025-07-15T06:56:23.128Z] INFO: Database connected successfully
[2025-07-15T06:56:23.159Z] INFO: Database connection test successful
[2025-07-15T06:56:23.160Z] INFO: Database connected successfully
[2025-07-15T06:56:23.170Z] INFO: WebSocket service initialized
[2025-07-15T06:56:23.174Z] INFO: Server running on port 8000 in development mode
[2025-07-15T06:56:23.175Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T07:33:52.774Z] INFO: Connecting to MSSQL database...
[2025-07-15T07:33:52.851Z] INFO: Database connected successfully
[2025-07-15T07:33:52.937Z] INFO: Database connection test successful
[2025-07-15T07:33:52.941Z] INFO: Database connected successfully
[2025-07-15T07:33:52.949Z] INFO: WebSocket service initialized
[2025-07-15T07:33:52.953Z] INFO: Server running on port 5001 in development mode
[2025-07-15T07:33:52.974Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T07:39:26.643Z] INFO: ::1 - - [15/Jul/2025:07:39:26 +0000] "POST /api/auth/login HTTP/1.1" 500 42 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T07:39:28.439Z] INFO: ::1 - - [15/Jul/2025:07:39:28 +0000] "POST /api/auth/login HTTP/1.1" 500 42 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T07:41:50.061Z] INFO: Connecting to MSSQL database...
[2025-07-15T07:41:50.125Z] INFO: Database connected successfully
[2025-07-15T07:41:50.432Z] INFO: Database connection test successful
[2025-07-15T07:41:50.478Z] INFO: Database connected successfully
[2025-07-15T07:41:50.486Z] INFO: WebSocket service initialized
[2025-07-15T07:41:50.499Z] INFO: Server running on port 5001 in development mode
[2025-07-15T07:41:50.505Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T07:43:20.450Z] INFO: ::1 - - [15/Jul/2025:07:43:20 +0000] "POST /api/auth/login HTTP/1.1" 500 42 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T07:43:53.324Z] INFO: Connecting to MSSQL database...
[2025-07-15T07:43:53.385Z] INFO: Database connected successfully
[2025-07-15T07:43:53.417Z] INFO: Database connection test successful
[2025-07-15T07:43:53.417Z] INFO: Database connected successfully
[2025-07-15T07:43:53.422Z] INFO: WebSocket service initialized
[2025-07-15T07:43:53.425Z] INFO: Server running on port 5001 in development mode
[2025-07-15T07:43:53.426Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T07:44:39.407Z] INFO: Connecting to MSSQL database...
[2025-07-15T07:44:39.467Z] INFO: Database connected successfully
[2025-07-15T07:44:39.509Z] INFO: Database connection test successful
[2025-07-15T07:44:39.511Z] INFO: Database connected successfully
[2025-07-15T07:44:39.519Z] INFO: WebSocket service initialized
[2025-07-15T07:46:25.153Z] INFO: Connecting to MSSQL database...
[2025-07-15T07:46:25.211Z] INFO: Database connected successfully
[2025-07-15T07:46:25.275Z] INFO: Database connection test successful
[2025-07-15T07:46:25.276Z] INFO: Database connected successfully
[2025-07-15T07:46:25.282Z] INFO: WebSocket service initialized
[2025-07-15T07:46:25.286Z] INFO: Server running on port 5001 in development mode
[2025-07-15T07:46:25.287Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T08:37:59.564Z] INFO: Connecting to MSSQL database...
[2025-07-15T08:37:59.670Z] INFO: Database connected successfully
[2025-07-15T08:37:59.796Z] INFO: Database connection test successful
[2025-07-15T08:37:59.797Z] INFO: Database connected successfully
[2025-07-15T08:37:59.802Z] INFO: WebSocket service initialized
[2025-07-15T08:37:59.812Z] INFO: Server running on port 5001 in development mode
[2025-07-15T08:37:59.813Z] INFO: WebSocket service initialized and ready for real-time updates
[2025-07-15T08:39:56.660Z] INFO: ::1 - - [15/Jul/2025:08:39:56 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093"
[2025-07-15T08:44:34.066Z] INFO: ::1 - - [15/Jul/2025:08:44:34 +0000] "POST /api/auth/login HTTP/1.1" 500 42 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-15T08:44:53.914Z] INFO: ::1 - - [15/Jul/2025:08:44:53 +0000] "POST /api/auth/login HTTP/1.1" 500 42 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
