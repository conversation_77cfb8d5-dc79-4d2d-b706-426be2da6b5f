// Simple test script to check backend startup
console.log('Testing backend startup...');

try {
    // Test basic Node.js functionality
    console.log('✅ Node.js is working');
    
    // Test require functionality
    const express = require('express');
    console.log('✅ Express is available');
    
    // Test dotenv
    require('dotenv').config();
    console.log('✅ Environment variables loaded');
    
    // Test database config
    const { connectDB } = require('./backend/config/database');
    console.log('✅ Database config loaded');
    
    // Try to start a simple server
    const app = express();
    const PORT = process.env.PORT || 5001;
    
    app.get('/health', (req, res) => {
        res.json({ status: 'OK', message: 'Backend is running' });
    });
    
    const server = app.listen(PORT, () => {
        console.log(`✅ Test server started on port ${PORT}`);
        console.log(`🌐 Test URL: http://localhost:${PORT}/health`);
        
        // Test database connection
        connectDB()
            .then(() => {
                console.log('✅ Database connection successful');
            })
            .catch((error) => {
                console.log('⚠️  Database connection failed:', error.message);
                console.log('   This is expected if SQL Server is not running');
            });
    });
    
    server.on('error', (error) => {
        console.error('❌ Server error:', error);
    });
    
} catch (error) {
    console.error('❌ Backend test failed:', error);
    process.exit(1);
}
