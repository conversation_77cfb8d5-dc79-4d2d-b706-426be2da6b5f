import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useLanguage } from '../../contexts/LanguageContext';
import CartIcon from '../cart/CartIcon';
import Logo from './Logo';
import CurrencyLanguageSelector from './CurrencyLanguageSelector';
import SearchInput from '../search/SearchInput';
import '../../styles/components.css';

const Header = () => {
    const { user, logout, isAuthenticated } = useAuth();
    const { t } = useLanguage();
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const navigate = useNavigate();

    // Scroll-based navigation state
    const [isNavHidden, setIsNavHidden] = useState(false);
    const [isNavFloating, setIsNavFloating] = useState(false);
    const lastScrollY = useRef(0);
    const scrollThreshold = 100;

    const handleLogout = useCallback(() => {
        // Add confirmation dialog for better UX
        if (window.confirm('Are you sure you want to logout?')) {
            logout();
            navigate('/');
            setIsMenuOpen(false);
        }
    }, [logout, navigate]);

    // Scroll behavior for navigation bar
    useEffect(() => {
        const handleScroll = () => {
            const currentScrollY = window.scrollY;

            // Only activate behavior after scrolling past threshold
            if (currentScrollY < scrollThreshold) {
                setIsNavHidden(false);
                setIsNavFloating(false);
                lastScrollY.current = currentScrollY;
                return;
            }

            // Determine scroll direction
            const scrollingDown = currentScrollY > lastScrollY.current;
            const scrollingUp = currentScrollY < lastScrollY.current;

            // Hide navigation when scrolling down, show when scrolling up
            if (scrollingDown && currentScrollY > scrollThreshold) {
                setIsNavHidden(true);
                setIsNavFloating(true);
            } else if (scrollingUp) {
                setIsNavHidden(false);
                setIsNavFloating(true);
            }

            lastScrollY.current = currentScrollY;
        };

        // Throttle scroll events for better performance
        let ticking = false;
        const throttledHandleScroll = () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    handleScroll();
                    ticking = false;
                });
                ticking = true;
            }
        };

        window.addEventListener('scroll', throttledHandleScroll, { passive: true });

        return () => {
            window.removeEventListener('scroll', throttledHandleScroll);
        };
    }, []);

    const toggleMenu = useCallback(() => {
        setIsMenuOpen(prev => !prev);
    }, []);

    const closeMenu = useCallback(() => {
        setIsMenuOpen(false);
    }, []);

    // Memoize navigation links to prevent unnecessary re-renders
    const navigationLinks = useMemo(() => [
        { to: '/', label: t('home') },
        { to: '/products', label: t('products') },
        { to: '/configurator', label: t('customFurniture') },
        { to: '/gallery', label: t('gallery') },
        { to: '/about', label: t('about') },
        { to: '/contact', label: t('contact') },
        { to: '/payment', label: t('payments') }
    ], [t]);

    // Memoize user actions to prevent re-renders
    const userActions = useMemo(() => {
        if (isAuthenticated) {
            return (
                <div className="user-menu">
                    <Link to="/account" className="action-btn user-btn">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z" stroke="#F0B21B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                    </Link>
                    <span className="user-greeting">Hello, {user?.firstName}</span>
                    <button onClick={handleLogout} className="logout-btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9M16 17L21 12M21 12L16 7M21 12H9" stroke="#F0B21B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                    </button>
                </div>
            );
        } else {
            return (
                <Link to="/login" className="action-btn login-btn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15M10 17L15 12M15 12L10 7M15 12H3" stroke="#F0B21B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                </Link>
            );
        }
    }, [isAuthenticated, user?.firstName, handleLogout]);

    return (
        <header className="header">
            {/* Special Offer Banner */}
            <div className="special-offer-banner">
                <div className="container">
                    <div className="offer-content">
                        <span className="offer-icon">⚡</span>
                        <span className="offer-text">{t('specialOffer')}</span>
                        <span className="offer-details">{t('offerText')}</span>
                        <button className="offer-shop-btn">{t('shopNow')}</button>
                        <button className="offer-close" onClick={() => document.querySelector('.special-offer-banner').style.display = 'none'}>×</button>
                    </div>
                </div>
            </div>

            {/* Top Header Info */}
            <div className="top-header">
                <div className="container">
                    <div className="top-header-content">
                        <div className="contact-info">
                            <span className="contact-item">
                                <svg
                                    width="14"
                                    height="14"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                    style={{ marginRight: '0.5rem' }}
                                >
                                    <path
                                        d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z"
                                        stroke="#F0B21B"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                    <polyline
                                        points="22,6 12,13 2,6"
                                        stroke="#F0B21B"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                </svg>
                                <EMAIL>
                            </span>
                            <span className="contact-item">
                                <svg
                                    width="14"
                                    height="14"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                    style={{ marginRight: '0.5rem' }}
                                >
                                    <path
                                        d="M22 16.92V19.92C22.0011 20.1985 21.9441 20.4742 21.8325 20.7293C21.7209 20.9845 21.5573 21.2136 21.3521 21.4019C21.1468 21.5901 20.9046 21.7335 20.6407 21.8227C20.3769 21.9119 20.0974 21.9451 19.82 21.92C16.7428 21.5856 13.787 20.5341 11.19 18.85C8.77382 17.3147 6.72533 15.2662 5.18999 12.85C3.49997 10.2412 2.44824 7.27099 2.11999 4.18C2.095 3.90347 2.12787 3.62476 2.21649 3.36162C2.30512 3.09849 2.44756 2.85669 2.63476 2.65162C2.82196 2.44655 3.0498 2.28271 3.30379 2.17052C3.55777 2.05833 3.83233 2.00026 4.10999 2H7.10999C7.59531 1.99522 8.06579 2.16708 8.43376 2.48353C8.80173 2.79999 9.04207 3.23945 9.10999 3.72C9.23662 4.68007 9.47144 5.62273 9.80999 6.53C9.94454 6.88792 9.97366 7.27691 9.8939 7.65088C9.81415 8.02485 9.62886 8.36811 9.35999 8.64L8.08999 9.91C9.51355 12.4135 11.5865 14.4864 14.09 15.91L15.36 14.64C15.6319 14.3711 15.9751 14.1858 16.3491 14.1061C16.7231 14.0263 17.1121 14.0555 17.47 14.19C18.3773 14.5286 19.3199 14.7634 20.28 14.89C20.7658 14.9585 21.2094 15.2032 21.5265 15.5775C21.8437 15.9518 22.0122 16.4296 22 16.92Z"
                                        stroke="#F0B21B"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                </svg>
                                (02) 413-6682
                            </span>
                        </div>
                        <div className="location-info">
                            #1 Binakla Street Cor Biak na Bato Brgy, Manresa, Quezon City
                        </div>
                        <CurrencyLanguageSelector />
                    </div>
                </div>
            </div>

            {/* Unified Header Container - Main Header + Navigation */}
            <div className={`unified-header-container ${isNavHidden ? 'unified-hidden' : ''} ${isNavFloating ? 'unified-floating' : ''}`}>
                {/* Main Header */}
                <div className="main-header">
                <div className="container">
                    <div className="main-header-content">
                        {/* Search */}
                        <div className="header-search">
                            <SearchInput
                                className="header-search-input"
                                placeholder={t('searchProducts')}
                            />
                        </div>

                        {/* Centered Logo */}
                        <div className="header-logo">
                            <Link to="/" className="logo">
                                <Logo size="default" />
                            </Link>
                            <div className="logo-tagline">EXCELLENCE IN DESIGN</div>
                        </div>

                        {/* User Actions */}
                        <div className="header-actions">
                            {/* Shopping Cart */}
                            <CartIcon />

                            {/* Contact/Message */}
                            <Link to="/contact" className="action-btn contact-btn">
                                <svg
                                    width="20"
                                    height="20"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z"
                                        stroke="#F0B21B"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                    <polyline
                                        points="22,6 12,13 2,6"
                                        stroke="#F0B21B"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                </svg>
                            </Link>

                            {/* User Authentication */}
                            {userActions}

                            {/* Mobile Menu Toggle */}
                            <button
                                className="mobile-menu-toggle"
                                onClick={toggleMenu}
                                aria-label="Toggle menu"
                            >
                                <span></span>
                                <span></span>
                                <span></span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

                {/* Navigation Bar */}
                <div className="navigation-bar">
                    <div className="container">
                        <nav className="main-navigation">
                            {navigationLinks.map(({ to, label }) => (
                                <Link key={to} to={to} className="nav-link">{label}</Link>
                            ))}
                        </nav>
                    </div>
                </div>
            </div>

            {/* Mobile Navigation */}
            <nav className={`mobile-nav ${isMenuOpen ? 'open' : ''}`}>
                {navigationLinks.map(({ to, label }) => (
                    <Link
                        key={to}
                        to={to}
                        className="mobile-nav-link"
                        onClick={closeMenu}
                    >
                        {label}
                    </Link>
                ))}

                {!isAuthenticated && (
                    <Link
                        to="/login"
                        className="mobile-nav-link login"
                        onClick={() => setIsMenuOpen(false)}
                    >
                        Login
                    </Link>
                )}
            </nav>
        </header>
    );
};

// Memoize the Header component to prevent unnecessary re-renders
export default React.memo(Header);
