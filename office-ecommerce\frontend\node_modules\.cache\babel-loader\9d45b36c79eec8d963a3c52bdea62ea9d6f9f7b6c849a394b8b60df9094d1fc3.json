{"ast": null, "code": "// API service for frontend application\n// This handles communication with the backend API\nimport apiClient from './apiClient';\n\n// Enhanced Products API for admin functionality\nconst productsApi = {\n  // Get products with pagination and filtering\n  getProducts: async (params = {}) => {\n    try {\n      const response = await apiClient.get('/api/products', {\n        params\n      });\n      return response;\n    } catch (error) {\n      // Return empty data structure if backend is not available\n      return {\n        success: false,\n        data: {\n          products: [],\n          pagination: {\n            currentPage: 1,\n            totalPages: 0,\n            totalItems: 0,\n            itemsPerPage: 20\n          }\n        },\n        error: error.message\n      };\n    }\n  },\n  // Get product by ID with full details\n  getProductById: async id => {\n    try {\n      const response = await apiClient.get(`/api/products/${id}`);\n      return response;\n    } catch (error) {\n      return {\n        success: false,\n        message: 'Product not found',\n        error: error.message\n      };\n    }\n  },\n  // Create or update product\n  createOrUpdateProduct: async productData => {\n    const url = productData.ProductID ? `/api/products/${productData.ProductID}` : '/api/products';\n    const method = productData.ProductID ? 'PUT' : 'POST';\n    return await apiClient[method.toLowerCase()](url, productData);\n  },\n  // Delete product\n  deleteProduct: async productId => {\n    try {\n      const response = await apiClient.delete(`/api/products/${productId}`);\n      return response;\n    } catch (error) {\n      console.error('Error deleting product:', error);\n      throw error;\n    }\n  },\n  // Upload 3D model\n  uploadModel: async (productId, formData) => {\n    try {\n      const response = await apiClient.post(`/api/products/${productId}/models`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response;\n    } catch (error) {\n      console.error('Error uploading 3D model:', error);\n      throw error;\n    }\n  },\n  // Upload images\n  uploadImages: async (productId, formData) => {\n    try {\n      const response = await apiClient.post(`/api/products/${productId}/images`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response;\n    } catch (error) {\n      console.error('Error uploading images:', error);\n      throw error;\n    }\n  },\n  // Get categories\n  getCategories: async () => {\n    try {\n      const response = await apiClient.get('/api/products/categories');\n      return response;\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n      // Fallback to mock data if backend is not available\n      return {\n        success: true,\n        data: mockCategories\n      };\n    }\n  }\n};\n\n// Original API with authentication\nconst authApi = {\n  // Mock authentication with admin support\n  login: credentials => {\n    // Check for admin credentials\n    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {\n      return Promise.resolve({\n        success: true,\n        data: {\n          token: 'mock-admin-jwt-token',\n          user: {\n            id: 1,\n            firstName: 'Admin',\n            lastName: 'User',\n            email: credentials.email,\n            role: 'Admin'\n          }\n        }\n      });\n    }\n\n    // Check for employee credentials\n    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {\n      return Promise.resolve({\n        success: true,\n        data: {\n          token: 'mock-employee-jwt-token',\n          user: {\n            id: 2,\n            firstName: 'Manager',\n            lastName: 'User',\n            email: credentials.email,\n            role: 'Employee'\n          }\n        }\n      });\n    }\n\n    // Regular customer login\n    return Promise.resolve({\n      success: true,\n      data: {\n        token: 'mock-jwt-token',\n        user: {\n          id: 3,\n          firstName: 'Demo',\n          lastName: 'User',\n          email: credentials.email,\n          role: 'Customer'\n        }\n      }\n    });\n  },\n  // Mock registration (always succeeds)\n  register: userData => {\n    return Promise.resolve({\n      success: true,\n      data: {\n        token: 'mock-jwt-token',\n        user: {\n          id: 1,\n          name: userData.name,\n          email: userData.email\n        }\n      }\n    });\n  }\n};\n\n// Combine APIs\nconst combinedApi = {\n  ...api,\n  ...authApi\n};\nexport default combinedApi;\nexport { productsApi };", "map": {"version": 3, "names": ["apiClient", "productsApi", "getProducts", "params", "response", "get", "error", "success", "data", "products", "pagination", "currentPage", "totalPages", "totalItems", "itemsPerPage", "message", "getProductById", "id", "createOrUpdateProduct", "productData", "url", "ProductID", "method", "toLowerCase", "deleteProduct", "productId", "delete", "console", "uploadModel", "formData", "post", "headers", "uploadImages", "getCategories", "mockCategories", "authApi", "login", "credentials", "email", "password", "Promise", "resolve", "token", "user", "firstName", "lastName", "role", "register", "userData", "name", "combinedApi", "api"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/services/api.js"], "sourcesContent": ["// API service for frontend application\n// This handles communication with the backend API\nimport apiClient from './apiClient';\n\n\n\n// Enhanced Products API for admin functionality\nconst productsApi = {\n  // Get products with pagination and filtering\n  getProducts: async (params = {}) => {\n    try {\n      const response = await apiClient.get('/api/products', { params });\n      return response;\n    } catch (error) {\n      // Return empty data structure if backend is not available\n      return {\n        success: false,\n        data: {\n          products: [],\n          pagination: {\n            currentPage: 1,\n            totalPages: 0,\n            totalItems: 0,\n            itemsPerPage: 20\n          }\n        },\n        error: error.message\n      };\n    }\n  },\n\n  // Get product by ID with full details\n  getProductById: async (id) => {\n    try {\n      const response = await apiClient.get(`/api/products/${id}`);\n      return response;\n    } catch (error) {\n      return {\n        success: false,\n        message: 'Product not found',\n        error: error.message\n      };\n    }\n  },\n\n  // Create or update product\n  createOrUpdateProduct: async (productData) => {\n    const url = productData.ProductID\n      ? `/api/products/${productData.ProductID}`\n      : '/api/products';\n\n    const method = productData.ProductID ? 'PUT' : 'POST';\n    return await apiClient[method.toLowerCase()](url, productData);\n  },\n\n  // Delete product\n  deleteProduct: async (productId) => {\n    try {\n      const response = await apiClient.delete(`/api/products/${productId}`);\n      return response;\n    } catch (error) {\n      console.error('Error deleting product:', error);\n      throw error;\n    }\n  },\n\n  // Upload 3D model\n  uploadModel: async (productId, formData) => {\n    try {\n      const response = await apiClient.post(`/api/products/${productId}/models`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response;\n    } catch (error) {\n      console.error('Error uploading 3D model:', error);\n      throw error;\n    }\n  },\n\n  // Upload images\n  uploadImages: async (productId, formData) => {\n    try {\n      const response = await apiClient.post(`/api/products/${productId}/images`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response;\n    } catch (error) {\n      console.error('Error uploading images:', error);\n      throw error;\n    }\n  },\n\n  // Get categories\n  getCategories: async () => {\n    try {\n      const response = await apiClient.get('/api/products/categories');\n      return response;\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n      // Fallback to mock data if backend is not available\n      return {\n        success: true,\n        data: mockCategories\n      };\n    }\n  }\n};\n\n// Original API with authentication\nconst authApi = {\n  // Mock authentication with admin support\n  login: (credentials) => {\n    // Check for admin credentials\n    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {\n      return Promise.resolve({\n        success: true,\n        data: {\n          token: 'mock-admin-jwt-token',\n          user: {\n            id: 1,\n            firstName: 'Admin',\n            lastName: 'User',\n            email: credentials.email,\n            role: 'Admin'\n          }\n        }\n      });\n    }\n\n    // Check for employee credentials\n    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {\n      return Promise.resolve({\n        success: true,\n        data: {\n          token: 'mock-employee-jwt-token',\n          user: {\n            id: 2,\n            firstName: 'Manager',\n            lastName: 'User',\n            email: credentials.email,\n            role: 'Employee'\n          }\n        }\n      });\n    }\n\n    // Regular customer login\n    return Promise.resolve({\n      success: true,\n      data: {\n        token: 'mock-jwt-token',\n        user: {\n          id: 3,\n          firstName: 'Demo',\n          lastName: 'User',\n          email: credentials.email,\n          role: 'Customer'\n        }\n      }\n    });\n  },\n\n  // Mock registration (always succeeds)\n  register: (userData) => {\n    return Promise.resolve({\n      success: true,\n      data: {\n        token: 'mock-jwt-token',\n        user: {\n          id: 1,\n          name: userData.name,\n          email: userData.email\n        }\n      }\n    });\n  }\n};\n\n// Combine APIs\nconst combinedApi = {\n  ...api,\n  ...authApi\n};\n\nexport default combinedApi;\nexport { productsApi };\n"], "mappings": "AAAA;AACA;AACA,OAAOA,SAAS,MAAM,aAAa;;AAInC;AACA,MAAMC,WAAW,GAAG;EAClB;EACAC,WAAW,EAAE,MAAAA,CAAOC,MAAM,GAAG,CAAC,CAAC,KAAK;IAClC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAC,eAAe,EAAE;QAAEF;MAAO,CAAC,CAAC;MACjE,OAAOC,QAAQ;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd;MACA,OAAO;QACLC,OAAO,EAAE,KAAK;QACdC,IAAI,EAAE;UACJC,QAAQ,EAAE,EAAE;UACZC,UAAU,EAAE;YACVC,WAAW,EAAE,CAAC;YACdC,UAAU,EAAE,CAAC;YACbC,UAAU,EAAE,CAAC;YACbC,YAAY,EAAE;UAChB;QACF,CAAC;QACDR,KAAK,EAAEA,KAAK,CAACS;MACf,CAAC;IACH;EACF,CAAC;EAED;EACAC,cAAc,EAAE,MAAOC,EAAE,IAAK;IAC5B,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAC,iBAAiBY,EAAE,EAAE,CAAC;MAC3D,OAAOb,QAAQ;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd,OAAO;QACLC,OAAO,EAAE,KAAK;QACdQ,OAAO,EAAE,mBAAmB;QAC5BT,KAAK,EAAEA,KAAK,CAACS;MACf,CAAC;IACH;EACF,CAAC;EAED;EACAG,qBAAqB,EAAE,MAAOC,WAAW,IAAK;IAC5C,MAAMC,GAAG,GAAGD,WAAW,CAACE,SAAS,GAC7B,iBAAiBF,WAAW,CAACE,SAAS,EAAE,GACxC,eAAe;IAEnB,MAAMC,MAAM,GAAGH,WAAW,CAACE,SAAS,GAAG,KAAK,GAAG,MAAM;IACrD,OAAO,MAAMrB,SAAS,CAACsB,MAAM,CAACC,WAAW,CAAC,CAAC,CAAC,CAACH,GAAG,EAAED,WAAW,CAAC;EAChE,CAAC;EAED;EACAK,aAAa,EAAE,MAAOC,SAAS,IAAK;IAClC,IAAI;MACF,MAAMrB,QAAQ,GAAG,MAAMJ,SAAS,CAAC0B,MAAM,CAAC,iBAAiBD,SAAS,EAAE,CAAC;MACrE,OAAOrB,QAAQ;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAsB,WAAW,EAAE,MAAAA,CAAOH,SAAS,EAAEI,QAAQ,KAAK;IAC1C,IAAI;MACF,MAAMzB,QAAQ,GAAG,MAAMJ,SAAS,CAAC8B,IAAI,CAAC,iBAAiBL,SAAS,SAAS,EAAEI,QAAQ,EAAE;QACnFE,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,OAAO3B,QAAQ;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA0B,YAAY,EAAE,MAAAA,CAAOP,SAAS,EAAEI,QAAQ,KAAK;IAC3C,IAAI;MACF,MAAMzB,QAAQ,GAAG,MAAMJ,SAAS,CAAC8B,IAAI,CAAC,iBAAiBL,SAAS,SAAS,EAAEI,QAAQ,EAAE;QACnFE,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,OAAO3B,QAAQ;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA2B,aAAa,EAAE,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAM7B,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAC,0BAA0B,CAAC;MAChE,OAAOD,QAAQ;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD;MACA,OAAO;QACLC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE0B;MACR,CAAC;IACH;EACF;AACF,CAAC;;AAED;AACA,MAAMC,OAAO,GAAG;EACd;EACAC,KAAK,EAAGC,WAAW,IAAK;IACtB;IACA,IAAIA,WAAW,CAACC,KAAK,KAAK,sBAAsB,IAAID,WAAW,CAACE,QAAQ,KAAK,UAAU,EAAE;MACvF,OAAOC,OAAO,CAACC,OAAO,CAAC;QACrBlC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE;UACJkC,KAAK,EAAE,sBAAsB;UAC7BC,IAAI,EAAE;YACJ1B,EAAE,EAAE,CAAC;YACL2B,SAAS,EAAE,OAAO;YAClBC,QAAQ,EAAE,MAAM;YAChBP,KAAK,EAAED,WAAW,CAACC,KAAK;YACxBQ,IAAI,EAAE;UACR;QACF;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIT,WAAW,CAACC,KAAK,KAAK,wBAAwB,IAAID,WAAW,CAACE,QAAQ,KAAK,UAAU,EAAE;MACzF,OAAOC,OAAO,CAACC,OAAO,CAAC;QACrBlC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE;UACJkC,KAAK,EAAE,yBAAyB;UAChCC,IAAI,EAAE;YACJ1B,EAAE,EAAE,CAAC;YACL2B,SAAS,EAAE,SAAS;YACpBC,QAAQ,EAAE,MAAM;YAChBP,KAAK,EAAED,WAAW,CAACC,KAAK;YACxBQ,IAAI,EAAE;UACR;QACF;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,OAAON,OAAO,CAACC,OAAO,CAAC;MACrBlC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;QACJkC,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE;UACJ1B,EAAE,EAAE,CAAC;UACL2B,SAAS,EAAE,MAAM;UACjBC,QAAQ,EAAE,MAAM;UAChBP,KAAK,EAAED,WAAW,CAACC,KAAK;UACxBQ,IAAI,EAAE;QACR;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACAC,QAAQ,EAAGC,QAAQ,IAAK;IACtB,OAAOR,OAAO,CAACC,OAAO,CAAC;MACrBlC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;QACJkC,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE;UACJ1B,EAAE,EAAE,CAAC;UACLgC,IAAI,EAAED,QAAQ,CAACC,IAAI;UACnBX,KAAK,EAAEU,QAAQ,CAACV;QAClB;MACF;IACF,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,MAAMY,WAAW,GAAG;EAClB,GAAGC,GAAG;EACN,GAAGhB;AACL,CAAC;AAED,eAAee,WAAW;AAC1B,SAASjD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}