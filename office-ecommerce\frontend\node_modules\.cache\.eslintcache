[{"C:\\DesignXcel\\office-ecommerce\\frontend\\src\\index.js": "1", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\reportWebVitals.js": "2", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\App.js": "3", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CartContext.js": "4", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CurrencyContext.js": "5", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\LanguageContext.js": "6", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Login.js": "7", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Home.js": "8", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useAuth.js": "9", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductCatalog.js": "10", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductDetail.js": "11", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Gallery.js": "12", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Payment.js": "13", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\CheckoutPage.js": "14", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Account.js": "15", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\OrderSuccessPage.js": "16", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Cart.js": "17", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\About.js": "18", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\admin\\AdminDashboard.js": "19", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Footer.js": "20", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\demo\\ProductCardDemo.js": "21", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Header.js": "22", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\AdminRoute.js": "23", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\RoleBasedRoute.js": "24", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\ProtectedRoute.js": "25", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePermissions.js": "26", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartItem.js": "27", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\3d\\3DConfigurator.js": "28", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\paymentService.js": "29", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\products.js": "30", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CheckoutModal.js": "31", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\auth.js": "32", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useWebSocket.js": "33", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\AdminLogo.js": "34", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductCard.js": "35", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\payment\\PayMongoCheckout.js": "36", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductFilter.js": "37", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\ProfileManagement.js": "38", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\OrderHistory.js": "39", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\SecuritySettings.js": "40", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AddressBook.js": "41", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AccountPreferences.js": "42", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\modals\\ConfirmationModal.js": "43", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\OrderManagement.js": "44", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ProductManagement.js": "45", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\Analytics.js": "46", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\SupplierManagement.js": "47", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\DashboardOverview.js": "48", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Logo.js": "49", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\UnauthorizedAccess.js": "50", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartIcon.js": "51", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\CurrencyLanguageSelector.js": "52", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\InventoryManagement.js": "53", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\UserManagement.js": "54", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\icons\\AdminIcons.js": "55", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchInput.js": "56", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiConfig.js": "57", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiClient.js": "58", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\api.js": "59", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePrice.js": "60", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\websocketService.js": "61", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartSidebar.js": "62", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchSuggestions.js": "63", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\modals\\ProductDetailsModal.js": "64", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\modals\\ProductFormModal.js": "65", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\FileUploadZone.js": "66", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ThreeJSPreview.js": "67", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ProductManagementNew.js": "68", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductCard.js": "69", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductForm.js": "70", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\SearchFilters.js": "71", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\index.js": "72", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\App.js": "73", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\reportWebVitals.js": "74", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CartContext.js": "75", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CurrencyContext.js": "76", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useAuth.js": "77", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\LanguageContext.js": "78", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Login.js": "79", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\OrderSuccessPage.js": "80", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Payment.js": "81", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductCatalog.js": "82", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\About.js": "83", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Account.js": "84", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductDetail.js": "85", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Home.js": "86", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Cart.js": "87", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\CheckoutPage.js": "88", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Gallery.js": "89", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\admin\\AdminDashboard.js": "90", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Header.js": "91", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\demo\\ProductCardDemo.js": "92", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Footer.js": "93", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\ProtectedRoute.js": "94", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\RoleBasedRoute.js": "95", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\AdminRoute.js": "96", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePermissions.js": "97", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\products.js": "98", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\paymentService.js": "99", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\auth.js": "100", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductFilter.js": "101", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductCard.js": "102", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useWebSocket.js": "103", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\AdminLogo.js": "104", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\OrderHistory.js": "105", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\ProfileManagement.js": "106", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AccountPreferences.js": "107", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\modals\\ConfirmationModal.js": "108", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\SecuritySettings.js": "109", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CheckoutModal.js": "110", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AddressBook.js": "111", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\3d\\3DConfigurator.js": "112", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartItem.js": "113", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\OrderManagement.js": "114", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\payment\\PayMongoCheckout.js": "115", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\DashboardOverview.js": "116", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\UserManagement.js": "117", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\SupplierManagement.js": "118", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ProductManagementNew.js": "119", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\Analytics.js": "120", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\InventoryManagement.js": "121", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartIcon.js": "122", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\CurrencyLanguageSelector.js": "123", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Logo.js": "124", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\UnauthorizedAccess.js": "125", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\icons\\AdminIcons.js": "126", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchInput.js": "127", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\websocketService.js": "128", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\api.js": "129", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiClient.js": "130", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiConfig.js": "131", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePrice.js": "132", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartSidebar.js": "133", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchSuggestions.js": "134", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\SearchFilters.js": "135", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductCard.js": "136", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductForm.js": "137", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ThreeJSPreview.js": "138", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\FileUploadZone.js": "139"}, {"size": 535, "mtime": 1748532508000, "results": "140", "hashOfConfig": "141"}, {"size": 362, "mtime": 1748532510000, "results": "142", "hashOfConfig": "141"}, {"size": 7037, "mtime": 1752560617455, "results": "143", "hashOfConfig": "141"}, {"size": 5553, "mtime": 1748536576000, "results": "144", "hashOfConfig": "141"}, {"size": 4139, "mtime": 1751076622000, "results": "145", "hashOfConfig": "141"}, {"size": 6574, "mtime": 1751098548000, "results": "146", "hashOfConfig": "141"}, {"size": 22019, "mtime": 1751370338000, "results": "147", "hashOfConfig": "141"}, {"size": 9763, "mtime": 1751248890000, "results": "148", "hashOfConfig": "141"}, {"size": 4440, "mtime": 1752560617436, "results": "149", "hashOfConfig": "141"}, {"size": 18118, "mtime": 1751248890000, "results": "150", "hashOfConfig": "141"}, {"size": 14768, "mtime": 1751099198000, "results": "151", "hashOfConfig": "141"}, {"size": 7100, "mtime": 1750777438000, "results": "152", "hashOfConfig": "141"}, {"size": 13424, "mtime": 1748592600000, "results": "153", "hashOfConfig": "141"}, {"size": 23206, "mtime": 1751291576000, "results": "154", "hashOfConfig": "141"}, {"size": 5296, "mtime": 1751375904000, "results": "155", "hashOfConfig": "141"}, {"size": 11069, "mtime": 1751278342000, "results": "156", "hashOfConfig": "141"}, {"size": 9980, "mtime": 1751289364000, "results": "157", "hashOfConfig": "141"}, {"size": 11059, "mtime": 1750776056000, "results": "158", "hashOfConfig": "141"}, {"size": 8110, "mtime": 1752560617468, "results": "159", "hashOfConfig": "141"}, {"size": 8266, "mtime": 1748588852000, "results": "160", "hashOfConfig": "141"}, {"size": 5949, "mtime": 1748749522000, "results": "161", "hashOfConfig": "141"}, {"size": 19148, "mtime": 1751447640000, "results": "162", "hashOfConfig": "141"}, {"size": 1304, "mtime": 1751274452000, "results": "163", "hashOfConfig": "141"}, {"size": 1283, "mtime": 1751274444000, "results": "164", "hashOfConfig": "141"}, {"size": 809, "mtime": 1751274436000, "results": "165", "hashOfConfig": "141"}, {"size": 4827, "mtime": 1751274510000, "results": "166", "hashOfConfig": "141"}, {"size": 5314, "mtime": 1751292028000, "results": "167", "hashOfConfig": "141"}, {"size": 53614, "mtime": 1751251970000, "results": "168", "hashOfConfig": "141"}, {"size": 7098, "mtime": 1751277960000, "results": "169", "hashOfConfig": "141"}, {"size": 11351, "mtime": 1751114552000, "results": "170", "hashOfConfig": "141"}, {"size": 3375, "mtime": 1751249990000, "results": "171", "hashOfConfig": "141"}, {"size": 975, "mtime": 1752563029324, "results": "172", "hashOfConfig": "141"}, {"size": 5117, "mtime": 1752560617473, "results": "173", "hashOfConfig": "141"}, {"size": 2754, "mtime": 1751275216000, "results": "174", "hashOfConfig": "141"}, {"size": 9289, "mtime": 1751076836000, "results": "175", "hashOfConfig": "141"}, {"size": 10084, "mtime": 1751440302000, "results": "176", "hashOfConfig": "141"}, {"size": 5674, "mtime": 1748533678000, "results": "177", "hashOfConfig": "141"}, {"size": 10237, "mtime": 1751375976000, "results": "178", "hashOfConfig": "141"}, {"size": 13243, "mtime": 1751376032000, "results": "179", "hashOfConfig": "141"}, {"size": 20686, "mtime": 1751376374000, "results": "180", "hashOfConfig": "141"}, {"size": 19762, "mtime": 1751376110000, "results": "181", "hashOfConfig": "141"}, {"size": 13935, "mtime": 1751376296000, "results": "182", "hashOfConfig": "141"}, {"size": 3302, "mtime": 1751251038000, "results": "183", "hashOfConfig": "141"}, {"size": 23502, "mtime": 1751440302000, "results": "184", "hashOfConfig": "141"}, {"size": 17469, "mtime": 1751459722103, "results": "185", "hashOfConfig": "186"}, {"size": 8949, "mtime": 1751275712000, "results": "187", "hashOfConfig": "141"}, {"size": 2536, "mtime": 1751268692000, "results": "188", "hashOfConfig": "141"}, {"size": 8611, "mtime": 1751273494000, "results": "189", "hashOfConfig": "141"}, {"size": 2632, "mtime": 1749573724000, "results": "190", "hashOfConfig": "141"}, {"size": 3344, "mtime": 1751274470000, "results": "191", "hashOfConfig": "141"}, {"size": 2001, "mtime": 1751448750000, "results": "192", "hashOfConfig": "141"}, {"size": 5719, "mtime": 1751076762000, "results": "193", "hashOfConfig": "141"}, {"size": 11918, "mtime": 1751270770000, "results": "194", "hashOfConfig": "141"}, {"size": 3006, "mtime": 1751268708000, "results": "195", "hashOfConfig": "141"}, {"size": 11855, "mtime": 1752560617455, "results": "196", "hashOfConfig": "141"}, {"size": 9556, "mtime": 1751175720000, "results": "197", "hashOfConfig": "141"}, {"size": 3823, "mtime": 1751272136000, "results": "198", "hashOfConfig": "141"}, {"size": 6242, "mtime": 1751387088000, "results": "199", "hashOfConfig": "141"}, {"size": 6705, "mtime": 1751460330000, "results": "200", "hashOfConfig": "141"}, {"size": 2725, "mtime": 1751076778000, "results": "201", "hashOfConfig": "141"}, {"size": 6794, "mtime": 1752560617473, "results": "202", "hashOfConfig": "141"}, {"size": 5487, "mtime": 1748536714000, "results": "203", "hashOfConfig": "141"}, {"size": 4182, "mtime": 1751175562000, "results": "204", "hashOfConfig": "141"}, {"size": 14750, "mtime": 1751454906562, "results": "205", "hashOfConfig": "186"}, {"size": 16346, "mtime": 1751460354209, "results": "206", "hashOfConfig": "186"}, {"size": 6346, "mtime": 1751454744000, "results": "207", "hashOfConfig": "141"}, {"size": 6908, "mtime": 1751459962000, "results": "208", "hashOfConfig": "141"}, {"size": 10679, "mtime": 1751460404000, "results": "209", "hashOfConfig": "141"}, {"size": 6209, "mtime": 1751460516000, "results": "210", "hashOfConfig": "141"}, {"size": 17111, "mtime": 1752036839812, "results": "211", "hashOfConfig": "141"}, {"size": 5819, "mtime": 1752036805302, "results": "212", "hashOfConfig": "141"}, {"size": 535, "mtime": 1748532508000, "results": "213", "hashOfConfig": "214"}, {"size": 7288, "mtime": 1752550163170, "results": "215", "hashOfConfig": "214"}, {"size": 362, "mtime": 1748532510000, "results": "216", "hashOfConfig": "214"}, {"size": 5553, "mtime": 1748536576000, "results": "217", "hashOfConfig": "214"}, {"size": 4139, "mtime": 1751076622000, "results": "218", "hashOfConfig": "214"}, {"size": 4954, "mtime": 1752549460528, "results": "219", "hashOfConfig": "214"}, {"size": 6574, "mtime": 1751098548000, "results": "220", "hashOfConfig": "214"}, {"size": 22019, "mtime": 1751370338000, "results": "221", "hashOfConfig": "214"}, {"size": 11069, "mtime": 1751278342000, "results": "222", "hashOfConfig": "214"}, {"size": 13424, "mtime": 1748592600000, "results": "223", "hashOfConfig": "214"}, {"size": 18118, "mtime": 1751248890000, "results": "224", "hashOfConfig": "214"}, {"size": 11059, "mtime": 1750776056000, "results": "225", "hashOfConfig": "214"}, {"size": 5296, "mtime": 1751375904000, "results": "226", "hashOfConfig": "214"}, {"size": 14768, "mtime": 1751099198000, "results": "227", "hashOfConfig": "214"}, {"size": 9763, "mtime": 1751248890000, "results": "228", "hashOfConfig": "214"}, {"size": 9980, "mtime": 1751289364000, "results": "229", "hashOfConfig": "214"}, {"size": 23206, "mtime": 1751291576000, "results": "230", "hashOfConfig": "214"}, {"size": 7100, "mtime": 1750777438000, "results": "231", "hashOfConfig": "214"}, {"size": 8538, "mtime": 1752551742429, "results": "232", "hashOfConfig": "214"}, {"size": 19148, "mtime": 1751447640000, "results": "233", "hashOfConfig": "214"}, {"size": 5949, "mtime": 1748749522000, "results": "234", "hashOfConfig": "214"}, {"size": 8266, "mtime": 1748588852000, "results": "235", "hashOfConfig": "214"}, {"size": 809, "mtime": 1751274436000, "results": "236", "hashOfConfig": "214"}, {"size": 1283, "mtime": 1751274444000, "results": "237", "hashOfConfig": "214"}, {"size": 1304, "mtime": 1751274452000, "results": "238", "hashOfConfig": "214"}, {"size": 4827, "mtime": 1751274510000, "results": "239", "hashOfConfig": "214"}, {"size": 11351, "mtime": 1751114552000, "results": "240", "hashOfConfig": "214"}, {"size": 7098, "mtime": 1751277960000, "results": "241", "hashOfConfig": "214"}, {"size": 2764, "mtime": 1752550266258, "results": "242", "hashOfConfig": "214"}, {"size": 5674, "mtime": 1748533678000, "results": "243", "hashOfConfig": "214"}, {"size": 9289, "mtime": 1751076836000, "results": "244", "hashOfConfig": "214"}, {"size": 5687, "mtime": 1752551704438, "results": "245", "hashOfConfig": "214"}, {"size": 2754, "mtime": 1751275216000, "results": "246", "hashOfConfig": "214"}, {"size": 13243, "mtime": 1751376032000, "results": "247", "hashOfConfig": "214"}, {"size": 10237, "mtime": 1751375976000, "results": "248", "hashOfConfig": "214"}, {"size": 13935, "mtime": 1751376296000, "results": "249", "hashOfConfig": "214"}, {"size": 3302, "mtime": 1751251038000, "results": "250", "hashOfConfig": "214"}, {"size": 20686, "mtime": 1751376374000, "results": "251", "hashOfConfig": "214"}, {"size": 3375, "mtime": 1751249990000, "results": "252", "hashOfConfig": "214"}, {"size": 19762, "mtime": 1751376110000, "results": "253", "hashOfConfig": "214"}, {"size": 53614, "mtime": 1751251970000, "results": "254", "hashOfConfig": "214"}, {"size": 5314, "mtime": 1751292028000, "results": "255", "hashOfConfig": "214"}, {"size": 23502, "mtime": 1751440302000, "results": "256", "hashOfConfig": "214"}, {"size": 10084, "mtime": 1751440302000, "results": "257", "hashOfConfig": "214"}, {"size": 8611, "mtime": 1751273494000, "results": "258", "hashOfConfig": "214"}, {"size": 3006, "mtime": 1751268708000, "results": "259", "hashOfConfig": "214"}, {"size": 2536, "mtime": 1751268692000, "results": "260", "hashOfConfig": "214"}, {"size": 10679, "mtime": 1751460404000, "results": "261", "hashOfConfig": "214"}, {"size": 8949, "mtime": 1751275712000, "results": "262", "hashOfConfig": "214"}, {"size": 11918, "mtime": 1751270770000, "results": "263", "hashOfConfig": "214"}, {"size": 2001, "mtime": 1751448750000, "results": "264", "hashOfConfig": "214"}, {"size": 5719, "mtime": 1751076762000, "results": "265", "hashOfConfig": "214"}, {"size": 2632, "mtime": 1749573724000, "results": "266", "hashOfConfig": "214"}, {"size": 3344, "mtime": 1751274470000, "results": "267", "hashOfConfig": "214"}, {"size": 12092, "mtime": 1752550729643, "results": "268", "hashOfConfig": "214"}, {"size": 9556, "mtime": 1751175720000, "results": "269", "hashOfConfig": "214"}, {"size": 7053, "mtime": 1752551721433, "results": "270", "hashOfConfig": "214"}, {"size": 6705, "mtime": 1751460330000, "results": "271", "hashOfConfig": "214"}, {"size": 6242, "mtime": 1751387088000, "results": "272", "hashOfConfig": "214"}, {"size": 3823, "mtime": 1751272136000, "results": "273", "hashOfConfig": "214"}, {"size": 2725, "mtime": 1751076778000, "results": "274", "hashOfConfig": "214"}, {"size": 5487, "mtime": 1748536714000, "results": "275", "hashOfConfig": "214"}, {"size": 4182, "mtime": 1751175562000, "results": "276", "hashOfConfig": "214"}, {"size": 5819, "mtime": 1752036805302, "results": "277", "hashOfConfig": "214"}, {"size": 6209, "mtime": 1751460516000, "results": "278", "hashOfConfig": "214"}, {"size": 17111, "mtime": 1752036839812, "results": "279", "hashOfConfig": "214"}, {"size": 6908, "mtime": 1751459962000, "results": "280", "hashOfConfig": "214"}, {"size": 6346, "mtime": 1751454744000, "results": "281", "hashOfConfig": "214"}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "l36c5a", {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1hbkk32", {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1craelc", {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\index.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\reportWebVitals.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\App.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CartContext.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CurrencyContext.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\LanguageContext.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Login.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Home.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useAuth.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductCatalog.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductDetail.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Gallery.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Payment.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\CheckoutPage.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Account.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\OrderSuccessPage.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Cart.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\About.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\admin\\AdminDashboard.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Footer.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\demo\\ProductCardDemo.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Header.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\AdminRoute.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\RoleBasedRoute.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\ProtectedRoute.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePermissions.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartItem.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\3d\\3DConfigurator.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\paymentService.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\products.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CheckoutModal.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\auth.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useWebSocket.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\AdminLogo.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductCard.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\payment\\PayMongoCheckout.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductFilter.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\ProfileManagement.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\OrderHistory.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\SecuritySettings.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AddressBook.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AccountPreferences.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\modals\\ConfirmationModal.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\OrderManagement.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ProductManagement.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\Analytics.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\SupplierManagement.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\DashboardOverview.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Logo.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\UnauthorizedAccess.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartIcon.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\CurrencyLanguageSelector.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\InventoryManagement.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\UserManagement.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\icons\\AdminIcons.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchInput.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiConfig.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiClient.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\api.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePrice.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\websocketService.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartSidebar.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchSuggestions.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\modals\\ProductDetailsModal.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\modals\\ProductFormModal.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\FileUploadZone.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ThreeJSPreview.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ProductManagementNew.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductCard.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductForm.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\SearchFilters.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\index.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\App.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\reportWebVitals.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CartContext.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CurrencyContext.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useAuth.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\LanguageContext.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Login.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\OrderSuccessPage.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Payment.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductCatalog.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\About.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Account.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductDetail.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Home.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Cart.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\CheckoutPage.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Gallery.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\admin\\AdminDashboard.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Header.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\demo\\ProductCardDemo.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Footer.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\ProtectedRoute.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\RoleBasedRoute.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\AdminRoute.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePermissions.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\products.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\paymentService.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\auth.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductFilter.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductCard.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useWebSocket.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\AdminLogo.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\OrderHistory.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\ProfileManagement.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AccountPreferences.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\modals\\ConfirmationModal.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\SecuritySettings.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CheckoutModal.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AddressBook.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\3d\\3DConfigurator.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartItem.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\OrderManagement.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\payment\\PayMongoCheckout.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\DashboardOverview.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\UserManagement.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\SupplierManagement.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ProductManagementNew.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\Analytics.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\InventoryManagement.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartIcon.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\CurrencyLanguageSelector.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Logo.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\UnauthorizedAccess.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\icons\\AdminIcons.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchInput.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\websocketService.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\api.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiClient.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiConfig.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePrice.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartSidebar.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchSuggestions.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\SearchFilters.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductCard.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductForm.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ThreeJSPreview.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\FileUploadZone.js", [], []]