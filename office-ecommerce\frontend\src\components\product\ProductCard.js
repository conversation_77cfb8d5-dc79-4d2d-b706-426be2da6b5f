import React, { useState, useMemo, useCallback } from 'react';
import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';
import { useCart } from '../../contexts/CartContext';
import { usePrice } from '../../hooks/usePrice';
import CheckoutModal from '../cart/CheckoutModal';
import { CONFIGURATION_OPTIONS, PRODUCT_CONFIG } from '../../constants';
import { supportsAdvanced3D, calculateDiscountPercentage } from '../../utils';

/**
 * ProductCard component displays a product with image, details, pricing, and quick actions
 * @param {Object} product - Product object containing all product information
 * @param {string|number} product.id - Unique product identifier
 * @param {string} product.name - Product name
 * @param {number} product.price - Base product price
 * @param {number} [product.discountPrice] - Discounted price if applicable
 * @param {string[]} [product.images] - Array of product image URLs
 * @param {string} [product.categoryName] - Product category name
 * @param {boolean} [product.featured] - Whether product is featured
 */
const ProductCard = ({ product }) => {
    const { addToCart } = useCart();
    const { formatSinglePrice, formatPriceWithDiscount } = usePrice();
    const [showCheckoutModal, setShowCheckoutModal] = useState(false);
    const [quickConfig, setQuickConfig] = useState({
        color: 'default',
        material: 'default',
        size: 'standard'
    });
    const {
        id,
        name,
        price,
        discountPrice,
        images,
        categoryName,
        featured
    } = product;

    // Memoize price calculation to avoid recalculation on every render
    const calculateConfiguredPrice = useCallback((basePrice, config) => {
        let multiplier = 1;

        // Apply configuration multipliers from constants
        const colorConfig = CONFIGURATION_OPTIONS.COLORS[config.color];
        const materialConfig = CONFIGURATION_OPTIONS.MATERIALS[config.material];
        const sizeConfig = CONFIGURATION_OPTIONS.SIZES[config.size];

        if (colorConfig) multiplier *= colorConfig.multiplier;
        if (materialConfig) multiplier *= materialConfig.multiplier;
        if (sizeConfig) multiplier *= sizeConfig.multiplier;

        return basePrice * multiplier;
    }, []);

    // Memoize expensive calculations
    const productData = useMemo(() => {
        const baseDisplayPrice = discountPrice || price;
        const configuredPrice = calculateConfiguredPrice(baseDisplayPrice, quickConfig);
        const hasDiscount = discountPrice && discountPrice < price;
        const hasConfiguration = quickConfig.color !== 'default' || quickConfig.material !== 'default' || quickConfig.size !== 'standard';
        const primaryImage = images && images.length > 0 ? images[0] : PRODUCT_CONFIG.DEFAULT_IMAGE;

        return {
            baseDisplayPrice,
            configuredPrice,
            hasDiscount,
            hasConfiguration,
            primaryImage
        };
    }, [discountPrice, price, quickConfig, images, calculateConfiguredPrice]);

    // Memoize price formatting
    const formatPrice = useCallback((price) => {
        return formatSinglePrice(price);
    }, [formatSinglePrice]);

    const handleAddToCart = useCallback((e) => {
        e.preventDefault(); // Prevent navigation when clicking add to cart

        if (!product || !product.id) {
            console.error('ProductCard: Invalid product data');
            alert('Unable to add item to cart. Product information is missing.');
            return;
        }

        try {
            // Create product with quick configuration options
            const configuredProduct = {
                ...product,
                quickConfiguration: quickConfig,
                configuredPrice: calculateConfiguredPrice(product.price, quickConfig)
            };
            addToCart(configuredProduct, 1); // Add 1 item with quick config settings
            setShowCheckoutModal(true); // Show checkout modal
        } catch (error) {
            console.error('ProductCard: Failed to add item to cart:', error);
            alert('Failed to add item to cart. Please try again.');
        }
    }, [product, quickConfig, calculateConfiguredPrice, addToCart]);

    return (
        <div className="product-card">
            {featured && <div className="featured-badge">Featured</div>}
            {productData.hasDiscount && (
                <div className="discount-badge">
                    {calculateDiscountPercentage(price, discountPrice)}% OFF
                </div>
            )}

            <Link to={`/product/${id}`} className="product-link">
                <div className="product-image">
                    <img
                        src={productData.primaryImage}
                        alt={name}
                        onError={(e) => {
                            e.target.src = PRODUCT_CONFIG.DEFAULT_IMAGE;
                        }}
                    />
                </div>

                <div className="product-info">
                    <div className="product-category">{categoryName}</div>
                    <h3 className="product-name">{name}</h3>

                    <div className="product-pricing">
                        <span className="current-price">
                            {formatPrice(productData.hasConfiguration ? productData.configuredPrice : productData.baseDisplayPrice)}
                        </span>
                        {productData.hasConfiguration && (
                            <span className="base-price">{formatPrice(productData.baseDisplayPrice)}</span>
                        )}
                        {productData.hasDiscount && !productData.hasConfiguration && (
                            <span className="original-price">{formatPrice(price)}</span>
                        )}
                        {productData.hasConfiguration && (
                            <span className="config-indicator">Configured</span>
                        )}
                    </div>
                </div>
            </Link>

            <div className="product-actions">
                <Link to={`/product/${id}`} className="btn btn-primary btn-compact">
                    View Details
                </Link>
                <button
                    className="btn btn-secondary btn-compact"
                    onClick={handleAddToCart}
                >
                    Add to Cart
                </button>
            </div>

            {/* 3D Configuration Quick Actions */}
            <div className="config-actions">
                <button
                    className={`config-btn color-btn ${quickConfig.color !== 'default' ? 'active' : ''}`}
                    onClick={(e) => {
                        e.preventDefault();
                        const colors = ['default', 'dark-walnut', 'cherry', 'black', 'white'];
                        const currentIndex = colors.indexOf(quickConfig.color);
                        const nextColor = colors[(currentIndex + 1) % colors.length];
                        setQuickConfig(prev => ({ ...prev, color: nextColor }));
                    }}
                    title={`Color: ${quickConfig.color.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}`}
                >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                        <path d="M12 2a10 10 0 0 0 0 20 10 10 0 0 1 0-20z" fill="currentColor"/>
                    </svg>
                </button>

                <button
                    className={`config-btn material-btn ${quickConfig.material !== 'default' ? 'active' : ''}`}
                    onClick={(e) => {
                        e.preventDefault();
                        const materials = ['default', 'solid-wood', 'engineered-wood', 'metal', 'glass'];
                        const currentIndex = materials.indexOf(quickConfig.material);
                        const nextMaterial = materials[(currentIndex + 1) % materials.length];
                        setQuickConfig(prev => ({ ...prev, material: nextMaterial }));
                    }}
                    title={`Material: ${quickConfig.material.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}`}
                >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" strokeWidth="2"/>
                        <path d="M9 9h6v6H9z" fill="currentColor"/>
                    </svg>
                </button>

                <button
                    className={`config-btn size-btn ${quickConfig.size !== 'standard' ? 'active' : ''}`}
                    onClick={(e) => {
                        e.preventDefault();
                        const sizes = ['standard', 'compact', 'large', 'xl'];
                        const currentIndex = sizes.indexOf(quickConfig.size);
                        const nextSize = sizes[(currentIndex + 1) % sizes.length];
                        setQuickConfig(prev => ({ ...prev, size: nextSize }));
                    }}
                    title={`Size: ${quickConfig.size.toUpperCase()}`}
                >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                </button>

                <Link
                    to={`/product/${id}?configurator=true`}
                    className="config-btn full-config-btn"
                    title="Full 3D Configurator"
                >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M12 2L2 7L12 12L22 7L12 2Z" fill="currentColor"/>
                        <path d="M2 17L12 22L22 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M2 12L12 17L22 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                </Link>
            </div>

            {/* Checkout Modal */}
            <CheckoutModal
                isOpen={showCheckoutModal}
                onClose={() => setShowCheckoutModal(false)}
                product={product}
                quantity={1}
            />
        </div>
    );
};

// PropTypes validation
ProductCard.propTypes = {
    product: PropTypes.shape({
        id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
        name: PropTypes.string.isRequired,
        price: PropTypes.number.isRequired,
        discountPrice: PropTypes.number,
        images: PropTypes.arrayOf(PropTypes.string),
        categoryName: PropTypes.string,
        featured: PropTypes.bool
    }).isRequired
};

// Default props
ProductCard.defaultProps = {
    product: {
        images: [],
        categoryName: 'Uncategorized',
        featured: false
    }
};

// Memoize the component to prevent unnecessary re-renders
export default React.memo(ProductCard);
