{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\product\\\\ProductCard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useMemo, useCallback } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useCart } from '../../contexts/CartContext';\nimport { usePrice } from '../../hooks/usePrice';\nimport CheckoutModal from '../cart/CheckoutModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductCard = ({\n  product\n}) => {\n  _s();\n  const {\n    addToCart\n  } = useCart();\n  const {\n    formatSinglePrice,\n    formatPriceWithDiscount\n  } = usePrice();\n  const [showCheckoutModal, setShowCheckoutModal] = useState(false);\n  const [quickConfig, setQuickConfig] = useState({\n    color: 'default',\n    material: 'default',\n    size: 'standard'\n  });\n  const {\n    id,\n    name,\n    price,\n    discountPrice,\n    images,\n    categoryName,\n    featured\n  } = product;\n\n  // Memoize price calculation to avoid recalculation on every render\n  const calculateConfiguredPrice = useCallback((basePrice, config) => {\n    let multiplier = 1;\n\n    // Color price modifiers\n    if (config.color === 'cherry') multiplier += 0.15;else if (config.color === 'dark-walnut') multiplier += 0.10;else if (config.color === 'black' || config.color === 'white') multiplier += 0.05;\n\n    // Material price modifiers\n    if (config.material === 'solid-wood') multiplier += 0.25;else if (config.material === 'metal') multiplier += 0.20;else if (config.material === 'glass') multiplier += 0.15;\n\n    // Size price modifiers\n    if (config.size === 'large') multiplier += 0.20;else if (config.size === 'xl') multiplier += 0.35;else if (config.size === 'compact') multiplier -= 0.10;\n    return basePrice * multiplier;\n  }, []);\n\n  // Memoize expensive calculations\n  const productData = useMemo(() => {\n    const baseDisplayPrice = discountPrice || price;\n    const configuredPrice = calculateConfiguredPrice(baseDisplayPrice, quickConfig);\n    const hasDiscount = discountPrice && discountPrice < price;\n    const hasConfiguration = quickConfig.color !== 'default' || quickConfig.material !== 'default' || quickConfig.size !== 'standard';\n    const primaryImage = images && images.length > 0 ? images[0] : '/placeholder-image.jpg';\n    return {\n      baseDisplayPrice,\n      configuredPrice,\n      hasDiscount,\n      hasConfiguration,\n      primaryImage\n    };\n  }, [discountPrice, price, quickConfig, images, calculateConfiguredPrice]);\n\n  // Memoize price formatting\n  const formatPrice = useCallback(price => {\n    return formatSinglePrice(price);\n  }, [formatSinglePrice]);\n  const handleAddToCart = useCallback(e => {\n    e.preventDefault(); // Prevent navigation when clicking add to cart\n    try {\n      // Create product with quick configuration options\n      const configuredProduct = {\n        ...product,\n        quickConfiguration: quickConfig,\n        configuredPrice: calculateConfiguredPrice(product.price, quickConfig)\n      };\n      addToCart(configuredProduct, 1); // Add 1 item with quick config settings\n      setShowCheckoutModal(true); // Show checkout modal\n    } catch (error) {\n      alert('Failed to add item to cart. Please try again.');\n    }\n  }, [product, quickConfig, calculateConfiguredPrice, addToCart]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"product-card\",\n    children: [featured && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"featured-badge\",\n      children: \"Featured\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 26\n    }, this), productData.hasDiscount && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"discount-badge\",\n      children: [Math.round((price - discountPrice) / price * 100), \"% OFF\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(Link, {\n      to: `/product/${id}`,\n      className: \"product-link\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-image\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: productData.primaryImage,\n          alt: name,\n          onError: e => {\n            e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-category\",\n          children: categoryName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"product-name\",\n          children: name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-pricing\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"current-price\",\n            children: formatPrice(productData.hasConfiguration ? productData.configuredPrice : productData.baseDisplayPrice)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 25\n          }, this), productData.hasConfiguration && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"base-price\",\n            children: formatPrice(productData.baseDisplayPrice)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 29\n          }, this), productData.hasDiscount && !productData.hasConfiguration && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"original-price\",\n            children: formatPrice(price)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 29\n          }, this), productData.hasConfiguration && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"config-indicator\",\n            children: \"Configured\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-actions\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: `/product/${id}`,\n        className: \"btn btn-primary btn-compact\",\n        children: \"View Details\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-secondary btn-compact\",\n        onClick: handleAddToCart,\n        children: \"Add to Cart\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"config-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: `config-btn color-btn ${quickConfig.color !== 'default' ? 'active' : ''}`,\n        onClick: e => {\n          e.preventDefault();\n          const colors = ['default', 'dark-walnut', 'cherry', 'black', 'white'];\n          const currentIndex = colors.indexOf(quickConfig.color);\n          const nextColor = colors[(currentIndex + 1) % colors.length];\n          setQuickConfig(prev => ({\n            ...prev,\n            color: nextColor\n          }));\n        },\n        title: `Color: ${quickConfig.color.replace('-', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}`,\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"16\",\n          height: \"16\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M12 2a10 10 0 0 0 0 20 10 10 0 0 1 0-20z\",\n            fill: \"currentColor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `config-btn material-btn ${quickConfig.material !== 'default' ? 'active' : ''}`,\n        onClick: e => {\n          e.preventDefault();\n          const materials = ['default', 'solid-wood', 'engineered-wood', 'metal', 'glass'];\n          const currentIndex = materials.indexOf(quickConfig.material);\n          const nextMaterial = materials[(currentIndex + 1) % materials.length];\n          setQuickConfig(prev => ({\n            ...prev,\n            material: nextMaterial\n          }));\n        },\n        title: `Material: ${quickConfig.material.replace('-', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}`,\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"16\",\n          height: \"16\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n            x: \"3\",\n            y: \"3\",\n            width: \"18\",\n            height: \"18\",\n            rx: \"2\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M9 9h6v6H9z\",\n            fill: \"currentColor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `config-btn size-btn ${quickConfig.size !== 'standard' ? 'active' : ''}`,\n        onClick: e => {\n          e.preventDefault();\n          const sizes = ['standard', 'compact', 'large', 'xl'];\n          const currentIndex = sizes.indexOf(quickConfig.size);\n          const nextSize = sizes[(currentIndex + 1) % sizes.length];\n          setQuickConfig(prev => ({\n            ...prev,\n            size: nextSize\n          }));\n        },\n        title: `Size: ${quickConfig.size.toUpperCase()}`,\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"16\",\n          height: \"16\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: `/product/${id}?configurator=true`,\n        className: \"config-btn full-config-btn\",\n        title: \"Full 3D Configurator\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"16\",\n          height: \"16\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M12 2L2 7L12 12L22 7L12 2Z\",\n            fill: \"currentColor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M2 17L12 22L22 17\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M2 12L12 17L22 12\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(CheckoutModal, {\n      isOpen: showCheckoutModal,\n      onClose: () => setShowCheckoutModal(false),\n      product: product,\n      quantity: 1\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 9\n  }, this);\n};\n\n// Memoize the component to prevent unnecessary re-renders\n_s(ProductCard, \"GHPIJTGPtezl/lm+xhHmefToIH8=\", false, function () {\n  return [useCart, usePrice];\n});\n_c = ProductCard;\nexport default _c2 = /*#__PURE__*/React.memo(ProductCard);\nvar _c, _c2;\n$RefreshReg$(_c, \"ProductCard\");\n$RefreshReg$(_c2, \"%default%\");", "map": {"version": 3, "names": ["React", "useState", "useMemo", "useCallback", "Link", "useCart", "usePrice", "CheckoutModal", "jsxDEV", "_jsxDEV", "ProductCard", "product", "_s", "addToCart", "formatSinglePrice", "formatPriceWithDiscount", "showCheckoutModal", "setShowCheckoutModal", "quickConfig", "setQuickConfig", "color", "material", "size", "id", "name", "price", "discountPrice", "images", "categoryName", "featured", "calculateConfiguredPrice", "basePrice", "config", "multiplier", "productData", "baseDisplayPrice", "configuredPrice", "hasDiscount", "hasConfiguration", "primaryImage", "length", "formatPrice", "handleAddToCart", "e", "preventDefault", "configuredProduct", "quickConfiguration", "error", "alert", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Math", "round", "to", "src", "alt", "onError", "target", "onClick", "colors", "currentIndex", "indexOf", "nextColor", "prev", "title", "replace", "l", "toUpperCase", "width", "height", "viewBox", "fill", "cx", "cy", "r", "stroke", "strokeWidth", "d", "materials", "nextMaterial", "x", "y", "rx", "sizes", "nextSize", "strokeLinecap", "strokeLinejoin", "isOpen", "onClose", "quantity", "_c", "_c2", "memo", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/product/ProductCard.js"], "sourcesContent": ["import React, { useState, useMemo, useCallback } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useCart } from '../../contexts/CartContext';\nimport { usePrice } from '../../hooks/usePrice';\nimport CheckoutModal from '../cart/CheckoutModal';\n\nconst ProductCard = ({ product }) => {\n    const { addToCart } = useCart();\n    const { formatSinglePrice, formatPriceWithDiscount } = usePrice();\n    const [showCheckoutModal, setShowCheckoutModal] = useState(false);\n    const [quickConfig, setQuickConfig] = useState({\n        color: 'default',\n        material: 'default',\n        size: 'standard'\n    });\n    const {\n        id,\n        name,\n        price,\n        discountPrice,\n        images,\n        categoryName,\n        featured\n    } = product;\n\n    // Memoize price calculation to avoid recalculation on every render\n    const calculateConfiguredPrice = useCallback((basePrice, config) => {\n        let multiplier = 1;\n\n        // Color price modifiers\n        if (config.color === 'cherry') multiplier += 0.15;\n        else if (config.color === 'dark-walnut') multiplier += 0.10;\n        else if (config.color === 'black' || config.color === 'white') multiplier += 0.05;\n\n        // Material price modifiers\n        if (config.material === 'solid-wood') multiplier += 0.25;\n        else if (config.material === 'metal') multiplier += 0.20;\n        else if (config.material === 'glass') multiplier += 0.15;\n\n        // Size price modifiers\n        if (config.size === 'large') multiplier += 0.20;\n        else if (config.size === 'xl') multiplier += 0.35;\n        else if (config.size === 'compact') multiplier -= 0.10;\n\n        return basePrice * multiplier;\n    }, []);\n\n    // Memoize expensive calculations\n    const productData = useMemo(() => {\n        const baseDisplayPrice = discountPrice || price;\n        const configuredPrice = calculateConfiguredPrice(baseDisplayPrice, quickConfig);\n        const hasDiscount = discountPrice && discountPrice < price;\n        const hasConfiguration = quickConfig.color !== 'default' || quickConfig.material !== 'default' || quickConfig.size !== 'standard';\n        const primaryImage = images && images.length > 0 ? images[0] : '/placeholder-image.jpg';\n\n        return {\n            baseDisplayPrice,\n            configuredPrice,\n            hasDiscount,\n            hasConfiguration,\n            primaryImage\n        };\n    }, [discountPrice, price, quickConfig, images, calculateConfiguredPrice]);\n\n    // Memoize price formatting\n    const formatPrice = useCallback((price) => {\n        return formatSinglePrice(price);\n    }, [formatSinglePrice]);\n\n    const handleAddToCart = useCallback((e) => {\n        e.preventDefault(); // Prevent navigation when clicking add to cart\n        try {\n            // Create product with quick configuration options\n            const configuredProduct = {\n                ...product,\n                quickConfiguration: quickConfig,\n                configuredPrice: calculateConfiguredPrice(product.price, quickConfig)\n            };\n            addToCart(configuredProduct, 1); // Add 1 item with quick config settings\n            setShowCheckoutModal(true); // Show checkout modal\n        } catch (error) {\n            alert('Failed to add item to cart. Please try again.');\n        }\n    }, [product, quickConfig, calculateConfiguredPrice, addToCart]);\n\n    return (\n        <div className=\"product-card\">\n            {featured && <div className=\"featured-badge\">Featured</div>}\n            {productData.hasDiscount && (\n                <div className=\"discount-badge\">\n                    {Math.round(((price - discountPrice) / price) * 100)}% OFF\n                </div>\n            )}\n\n            <Link to={`/product/${id}`} className=\"product-link\">\n                <div className=\"product-image\">\n                    <img\n                        src={productData.primaryImage}\n                        alt={name}\n                        onError={(e) => {\n                            e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop';\n                        }}\n                    />\n                </div>\n\n                <div className=\"product-info\">\n                    <div className=\"product-category\">{categoryName}</div>\n                    <h3 className=\"product-name\">{name}</h3>\n\n                    <div className=\"product-pricing\">\n                        <span className=\"current-price\">\n                            {formatPrice(productData.hasConfiguration ? productData.configuredPrice : productData.baseDisplayPrice)}\n                        </span>\n                        {productData.hasConfiguration && (\n                            <span className=\"base-price\">{formatPrice(productData.baseDisplayPrice)}</span>\n                        )}\n                        {productData.hasDiscount && !productData.hasConfiguration && (\n                            <span className=\"original-price\">{formatPrice(price)}</span>\n                        )}\n                        {productData.hasConfiguration && (\n                            <span className=\"config-indicator\">Configured</span>\n                        )}\n                    </div>\n                </div>\n            </Link>\n\n            <div className=\"product-actions\">\n                <Link to={`/product/${id}`} className=\"btn btn-primary btn-compact\">\n                    View Details\n                </Link>\n                <button\n                    className=\"btn btn-secondary btn-compact\"\n                    onClick={handleAddToCart}\n                >\n                    Add to Cart\n                </button>\n            </div>\n\n            {/* 3D Configuration Quick Actions */}\n            <div className=\"config-actions\">\n                <button\n                    className={`config-btn color-btn ${quickConfig.color !== 'default' ? 'active' : ''}`}\n                    onClick={(e) => {\n                        e.preventDefault();\n                        const colors = ['default', 'dark-walnut', 'cherry', 'black', 'white'];\n                        const currentIndex = colors.indexOf(quickConfig.color);\n                        const nextColor = colors[(currentIndex + 1) % colors.length];\n                        setQuickConfig(prev => ({ ...prev, color: nextColor }));\n                    }}\n                    title={`Color: ${quickConfig.color.replace('-', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}`}\n                >\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                        <path d=\"M12 2a10 10 0 0 0 0 20 10 10 0 0 1 0-20z\" fill=\"currentColor\"/>\n                    </svg>\n                </button>\n\n                <button\n                    className={`config-btn material-btn ${quickConfig.material !== 'default' ? 'active' : ''}`}\n                    onClick={(e) => {\n                        e.preventDefault();\n                        const materials = ['default', 'solid-wood', 'engineered-wood', 'metal', 'glass'];\n                        const currentIndex = materials.indexOf(quickConfig.material);\n                        const nextMaterial = materials[(currentIndex + 1) % materials.length];\n                        setQuickConfig(prev => ({ ...prev, material: nextMaterial }));\n                    }}\n                    title={`Material: ${quickConfig.material.replace('-', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}`}\n                >\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                        <path d=\"M9 9h6v6H9z\" fill=\"currentColor\"/>\n                    </svg>\n                </button>\n\n                <button\n                    className={`config-btn size-btn ${quickConfig.size !== 'standard' ? 'active' : ''}`}\n                    onClick={(e) => {\n                        e.preventDefault();\n                        const sizes = ['standard', 'compact', 'large', 'xl'];\n                        const currentIndex = sizes.indexOf(quickConfig.size);\n                        const nextSize = sizes[(currentIndex + 1) % sizes.length];\n                        setQuickConfig(prev => ({ ...prev, size: nextSize }));\n                    }}\n                    title={`Size: ${quickConfig.size.toUpperCase()}`}\n                >\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                </button>\n\n                <Link\n                    to={`/product/${id}?configurator=true`}\n                    className=\"config-btn full-config-btn\"\n                    title=\"Full 3D Configurator\"\n                >\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M12 2L2 7L12 12L22 7L12 2Z\" fill=\"currentColor\"/>\n                        <path d=\"M2 17L12 22L22 17\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <path d=\"M2 12L12 17L22 12\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                </Link>\n            </div>\n\n            {/* Checkout Modal */}\n            <CheckoutModal\n                isOpen={showCheckoutModal}\n                onClose={() => setShowCheckoutModal(false)}\n                product={product}\n                quantity={1}\n            />\n        </div>\n    );\n};\n\n// Memoize the component to prevent unnecessary re-renders\nexport default React.memo(ProductCard);\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AAC7D,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,OAAOC,aAAa,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjC,MAAM;IAAEC;EAAU,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC/B,MAAM;IAAES,iBAAiB;IAAEC;EAAwB,CAAC,GAAGT,QAAQ,CAAC,CAAC;EACjE,MAAM,CAACU,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC;IAC3CmB,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE;EACV,CAAC,CAAC;EACF,MAAM;IACFC,EAAE;IACFC,IAAI;IACJC,KAAK;IACLC,aAAa;IACbC,MAAM;IACNC,YAAY;IACZC;EACJ,CAAC,GAAGlB,OAAO;;EAEX;EACA,MAAMmB,wBAAwB,GAAG3B,WAAW,CAAC,CAAC4B,SAAS,EAAEC,MAAM,KAAK;IAChE,IAAIC,UAAU,GAAG,CAAC;;IAElB;IACA,IAAID,MAAM,CAACZ,KAAK,KAAK,QAAQ,EAAEa,UAAU,IAAI,IAAI,CAAC,KAC7C,IAAID,MAAM,CAACZ,KAAK,KAAK,aAAa,EAAEa,UAAU,IAAI,IAAI,CAAC,KACvD,IAAID,MAAM,CAACZ,KAAK,KAAK,OAAO,IAAIY,MAAM,CAACZ,KAAK,KAAK,OAAO,EAAEa,UAAU,IAAI,IAAI;;IAEjF;IACA,IAAID,MAAM,CAACX,QAAQ,KAAK,YAAY,EAAEY,UAAU,IAAI,IAAI,CAAC,KACpD,IAAID,MAAM,CAACX,QAAQ,KAAK,OAAO,EAAEY,UAAU,IAAI,IAAI,CAAC,KACpD,IAAID,MAAM,CAACX,QAAQ,KAAK,OAAO,EAAEY,UAAU,IAAI,IAAI;;IAExD;IACA,IAAID,MAAM,CAACV,IAAI,KAAK,OAAO,EAAEW,UAAU,IAAI,IAAI,CAAC,KAC3C,IAAID,MAAM,CAACV,IAAI,KAAK,IAAI,EAAEW,UAAU,IAAI,IAAI,CAAC,KAC7C,IAAID,MAAM,CAACV,IAAI,KAAK,SAAS,EAAEW,UAAU,IAAI,IAAI;IAEtD,OAAOF,SAAS,GAAGE,UAAU;EACjC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,WAAW,GAAGhC,OAAO,CAAC,MAAM;IAC9B,MAAMiC,gBAAgB,GAAGT,aAAa,IAAID,KAAK;IAC/C,MAAMW,eAAe,GAAGN,wBAAwB,CAACK,gBAAgB,EAAEjB,WAAW,CAAC;IAC/E,MAAMmB,WAAW,GAAGX,aAAa,IAAIA,aAAa,GAAGD,KAAK;IAC1D,MAAMa,gBAAgB,GAAGpB,WAAW,CAACE,KAAK,KAAK,SAAS,IAAIF,WAAW,CAACG,QAAQ,KAAK,SAAS,IAAIH,WAAW,CAACI,IAAI,KAAK,UAAU;IACjI,MAAMiB,YAAY,GAAGZ,MAAM,IAAIA,MAAM,CAACa,MAAM,GAAG,CAAC,GAAGb,MAAM,CAAC,CAAC,CAAC,GAAG,wBAAwB;IAEvF,OAAO;MACHQ,gBAAgB;MAChBC,eAAe;MACfC,WAAW;MACXC,gBAAgB;MAChBC;IACJ,CAAC;EACL,CAAC,EAAE,CAACb,aAAa,EAAED,KAAK,EAAEP,WAAW,EAAES,MAAM,EAAEG,wBAAwB,CAAC,CAAC;;EAEzE;EACA,MAAMW,WAAW,GAAGtC,WAAW,CAAEsB,KAAK,IAAK;IACvC,OAAOX,iBAAiB,CAACW,KAAK,CAAC;EACnC,CAAC,EAAE,CAACX,iBAAiB,CAAC,CAAC;EAEvB,MAAM4B,eAAe,GAAGvC,WAAW,CAAEwC,CAAC,IAAK;IACvCA,CAAC,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC;IACpB,IAAI;MACA;MACA,MAAMC,iBAAiB,GAAG;QACtB,GAAGlC,OAAO;QACVmC,kBAAkB,EAAE5B,WAAW;QAC/BkB,eAAe,EAAEN,wBAAwB,CAACnB,OAAO,CAACc,KAAK,EAAEP,WAAW;MACxE,CAAC;MACDL,SAAS,CAACgC,iBAAiB,EAAE,CAAC,CAAC,CAAC,CAAC;MACjC5B,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC,OAAO8B,KAAK,EAAE;MACZC,KAAK,CAAC,+CAA+C,CAAC;IAC1D;EACJ,CAAC,EAAE,CAACrC,OAAO,EAAEO,WAAW,EAAEY,wBAAwB,EAAEjB,SAAS,CAAC,CAAC;EAE/D,oBACIJ,OAAA;IAAKwC,SAAS,EAAC,cAAc;IAAAC,QAAA,GACxBrB,QAAQ,iBAAIpB,OAAA;MAAKwC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAAC;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,EAC1DpB,WAAW,CAACG,WAAW,iBACpB5B,OAAA;MAAKwC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,GAC1BK,IAAI,CAACC,KAAK,CAAE,CAAC/B,KAAK,GAAGC,aAAa,IAAID,KAAK,GAAI,GAAG,CAAC,EAAC,OACzD;IAAA;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACR,eAED7C,OAAA,CAACL,IAAI;MAACqD,EAAE,EAAE,YAAYlC,EAAE,EAAG;MAAC0B,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAChDzC,OAAA;QAAKwC,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC1BzC,OAAA;UACIiD,GAAG,EAAExB,WAAW,CAACK,YAAa;UAC9BoB,GAAG,EAAEnC,IAAK;UACVoC,OAAO,EAAGjB,CAAC,IAAK;YACZA,CAAC,CAACkB,MAAM,CAACH,GAAG,GAAG,mFAAmF;UACtG;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN7C,OAAA;QAAKwC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACzBzC,OAAA;UAAKwC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAEtB;QAAY;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtD7C,OAAA;UAAIwC,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAE1B;QAAI;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAExC7C,OAAA;UAAKwC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5BzC,OAAA;YAAMwC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC1BT,WAAW,CAACP,WAAW,CAACI,gBAAgB,GAAGJ,WAAW,CAACE,eAAe,GAAGF,WAAW,CAACC,gBAAgB;UAAC;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrG,CAAC,EACNpB,WAAW,CAACI,gBAAgB,iBACzB7B,OAAA;YAAMwC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAET,WAAW,CAACP,WAAW,CAACC,gBAAgB;UAAC;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACjF,EACApB,WAAW,CAACG,WAAW,IAAI,CAACH,WAAW,CAACI,gBAAgB,iBACrD7B,OAAA;YAAMwC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAET,WAAW,CAAChB,KAAK;UAAC;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAC9D,EACApB,WAAW,CAACI,gBAAgB,iBACzB7B,OAAA;YAAMwC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACtD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEP7C,OAAA;MAAKwC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5BzC,OAAA,CAACL,IAAI;QAACqD,EAAE,EAAE,YAAYlC,EAAE,EAAG;QAAC0B,SAAS,EAAC,6BAA6B;QAAAC,QAAA,EAAC;MAEpE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACP7C,OAAA;QACIwC,SAAS,EAAC,+BAA+B;QACzCa,OAAO,EAAEpB,eAAgB;QAAAQ,QAAA,EAC5B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGN7C,OAAA;MAAKwC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC3BzC,OAAA;QACIwC,SAAS,EAAE,wBAAwB/B,WAAW,CAACE,KAAK,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;QACrF0C,OAAO,EAAGnB,CAAC,IAAK;UACZA,CAAC,CAACC,cAAc,CAAC,CAAC;UAClB,MAAMmB,MAAM,GAAG,CAAC,SAAS,EAAE,aAAa,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC;UACrE,MAAMC,YAAY,GAAGD,MAAM,CAACE,OAAO,CAAC/C,WAAW,CAACE,KAAK,CAAC;UACtD,MAAM8C,SAAS,GAAGH,MAAM,CAAC,CAACC,YAAY,GAAG,CAAC,IAAID,MAAM,CAACvB,MAAM,CAAC;UAC5DrB,cAAc,CAACgD,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE/C,KAAK,EAAE8C;UAAU,CAAC,CAAC,CAAC;QAC3D,CAAE;QACFE,KAAK,EAAE,UAAUlD,WAAW,CAACE,KAAK,CAACiD,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAEC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,EAAG;QAAArB,QAAA,eAE9FzC,OAAA;UAAK+D,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAAAzB,QAAA,gBACvDzC,OAAA;YAAQmE,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,CAAC,EAAC,IAAI;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC;UAAG;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eACtE7C,OAAA;YAAMwE,CAAC,EAAC,0CAA0C;YAACN,IAAI,EAAC;UAAc;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAET7C,OAAA;QACIwC,SAAS,EAAE,2BAA2B/B,WAAW,CAACG,QAAQ,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;QAC3FyC,OAAO,EAAGnB,CAAC,IAAK;UACZA,CAAC,CAACC,cAAc,CAAC,CAAC;UAClB,MAAMsC,SAAS,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,iBAAiB,EAAE,OAAO,EAAE,OAAO,CAAC;UAChF,MAAMlB,YAAY,GAAGkB,SAAS,CAACjB,OAAO,CAAC/C,WAAW,CAACG,QAAQ,CAAC;UAC5D,MAAM8D,YAAY,GAAGD,SAAS,CAAC,CAAClB,YAAY,GAAG,CAAC,IAAIkB,SAAS,CAAC1C,MAAM,CAAC;UACrErB,cAAc,CAACgD,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE9C,QAAQ,EAAE8D;UAAa,CAAC,CAAC,CAAC;QACjE,CAAE;QACFf,KAAK,EAAE,aAAalD,WAAW,CAACG,QAAQ,CAACgD,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAEC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,EAAG;QAAArB,QAAA,eAEpGzC,OAAA;UAAK+D,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAAAzB,QAAA,gBACvDzC,OAAA;YAAM2E,CAAC,EAAC,GAAG;YAACC,CAAC,EAAC,GAAG;YAACb,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACa,EAAE,EAAC,GAAG;YAACP,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC;UAAG;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eACvF7C,OAAA;YAAMwE,CAAC,EAAC,aAAa;YAACN,IAAI,EAAC;UAAc;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAET7C,OAAA;QACIwC,SAAS,EAAE,uBAAuB/B,WAAW,CAACI,IAAI,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;QACpFwC,OAAO,EAAGnB,CAAC,IAAK;UACZA,CAAC,CAACC,cAAc,CAAC,CAAC;UAClB,MAAM2C,KAAK,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC;UACpD,MAAMvB,YAAY,GAAGuB,KAAK,CAACtB,OAAO,CAAC/C,WAAW,CAACI,IAAI,CAAC;UACpD,MAAMkE,QAAQ,GAAGD,KAAK,CAAC,CAACvB,YAAY,GAAG,CAAC,IAAIuB,KAAK,CAAC/C,MAAM,CAAC;UACzDrB,cAAc,CAACgD,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE7C,IAAI,EAAEkE;UAAS,CAAC,CAAC,CAAC;QACzD,CAAE;QACFpB,KAAK,EAAE,SAASlD,WAAW,CAACI,IAAI,CAACiD,WAAW,CAAC,CAAC,EAAG;QAAArB,QAAA,eAEjDzC,OAAA;UAAK+D,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAAAzB,QAAA,eACvDzC,OAAA;YAAMwE,CAAC,EAAC,wCAAwC;YAACF,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACS,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC;UAAO;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAET7C,OAAA,CAACL,IAAI;QACDqD,EAAE,EAAE,YAAYlC,EAAE,oBAAqB;QACvC0B,SAAS,EAAC,4BAA4B;QACtCmB,KAAK,EAAC,sBAAsB;QAAAlB,QAAA,eAE5BzC,OAAA;UAAK+D,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAAAzB,QAAA,gBACvDzC,OAAA;YAAMwE,CAAC,EAAC,4BAA4B;YAACN,IAAI,EAAC;UAAc;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eAC1D7C,OAAA;YAAMwE,CAAC,EAAC,mBAAmB;YAACF,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACS,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC;UAAO;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eAChH7C,OAAA;YAAMwE,CAAC,EAAC,mBAAmB;YAACF,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACS,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC;UAAO;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/G;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN7C,OAAA,CAACF,aAAa;MACVoF,MAAM,EAAE3E,iBAAkB;MAC1B4E,OAAO,EAAEA,CAAA,KAAM3E,oBAAoB,CAAC,KAAK,CAAE;MAC3CN,OAAO,EAAEA,OAAQ;MACjBkF,QAAQ,EAAE;IAAE;MAAA1C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;;AAED;AAAA1C,EAAA,CAhNMF,WAAW;EAAA,QACSL,OAAO,EAC0BC,QAAQ;AAAA;AAAAwF,EAAA,GAF7DpF,WAAW;AAiNjB,eAAAqF,GAAA,gBAAe/F,KAAK,CAACgG,IAAI,CAACtF,WAAW,CAAC;AAAC,IAAAoF,EAAA,EAAAC,GAAA;AAAAE,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}