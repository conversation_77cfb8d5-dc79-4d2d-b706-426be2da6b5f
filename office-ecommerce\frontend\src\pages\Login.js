import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { usePermissions } from '../hooks/usePermissions';
import '../styles/pages.css';

const Login = () => {
    const [isLogin, setIsLogin] = useState(true);
    const [formData, setFormData] = useState({
        email: '',
        password: '',
        firstName: '',
        lastName: '',
        phone: ''
    });
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    
    const { login, register } = useAuth();
    const navigate = useNavigate();
    const location = useLocation();

    // Get the intended destination from location state
    const from = location.state?.from?.pathname || '/';

    const handleChange = (e) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value
        });
    };

    const getRedirectPath = (user, intendedPath) => {
        // If user was trying to access admin area, check permissions
        if (intendedPath.startsWith('/admin')) {
            return (user.role === 'Admin' || user.role === 'Employee') ? intendedPath : '/';
        }

        // Default redirect based on role
        if (intendedPath === '/') {
            return (user.role === 'Admin' || user.role === 'Employee') ? '/admin' : '/';
        }

        return intendedPath;
    };

    const handleLogin = async () => {
        const result = await login(formData.email, formData.password);
        if (result.success) {
            const redirectTo = getRedirectPath(result.user, from);
            navigate(redirectTo, { replace: true });
        } else {
            setError(result.error);
        }
    };

    const handleRegistration = async () => {
        const result = await register(formData);
        if (result.success) {
            navigate('/', { replace: true });
        } else {
            setError(result.error);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError('');

        try {
            if (isLogin) {
                await handleLogin();
            } else {
                await handleRegistration();
            }
        } catch (err) {
            setError(err.message || 'An error occurred');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="auth-page-modern">
            {/* Hero Section */}
            <div className="auth-hero">
                <div className="container">
                    <div className="auth-hero-content">
                        <div className="auth-brand">
                            <div className="brand-icon">
                                <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                                    <path d="M3 21h18" stroke="#F0B21B" strokeWidth="2"/>
                                    <path d="M5 21V7l8-4v18" stroke="#F0B21B" strokeWidth="2"/>
                                    <path d="M19 21V11l-6-4" stroke="#F0B21B" strokeWidth="2"/>
                                    <path d="M9 9v.01" stroke="#F0B21B" strokeWidth="2"/>
                                    <path d="M9 12v.01" stroke="#F0B21B" strokeWidth="2"/>
                                    <path d="M9 15v.01" stroke="#F0B21B" strokeWidth="2"/>
                                </svg>
                            </div>
                            <h1>DesignXcel</h1>
                            <p>Premium Office Furniture Solutions</p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="auth-main">
                <div className="container">
                    <div className="auth-layout">
                        {/* Left Side - Form */}
                        <div className="auth-form-container">
                            <div className="auth-card">
                                <div className="auth-header-modern">
                                    <div className="auth-icon">
                                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="#808080" strokeWidth="2"/>
                                            <circle cx="12" cy="7" r="4" stroke="#808080" strokeWidth="2"/>
                                        </svg>
                                    </div>
                                    <h2>{isLogin ? 'Welcome Back' : 'Create Account'}</h2>
                                    <p className="auth-subtitle">
                                        {isLogin
                                            ? 'Sign in to access your account and continue shopping'
                                            : 'Join us for a personalized shopping experience'
                                        }
                                    </p>
                                    <div className="header-underline"></div>
                                </div>

                                <form onSubmit={handleSubmit} className="auth-form-modern">
                                    {error && (
                                        <div className="error-message-modern">
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                                <circle cx="12" cy="12" r="10" stroke="#e74c3c" strokeWidth="2"/>
                                                <path d="M15 9l-6 6M9 9l6 6" stroke="#e74c3c" strokeWidth="2"/>
                                            </svg>
                                            <span>{error}</span>
                                        </div>
                                    )}

                                    {!isLogin && (
                                        <>
                                            <div className="form-row-modern">
                                                <div className="form-group-modern">
                                                    <label>First Name</label>
                                                    <div className="input-wrapper">
                                                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                                                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="#808080" strokeWidth="1.5"/>
                                                            <circle cx="12" cy="7" r="4" stroke="#808080" strokeWidth="1.5"/>
                                                        </svg>
                                                        <input
                                                            type="text"
                                                            name="firstName"
                                                            placeholder="Enter your first name"
                                                            value={formData.firstName}
                                                            onChange={handleChange}
                                                            required
                                                        />
                                                    </div>
                                                </div>
                                                <div className="form-group-modern">
                                                    <label>Last Name</label>
                                                    <div className="input-wrapper">
                                                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                                                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="#808080" strokeWidth="1.5"/>
                                                            <circle cx="12" cy="7" r="4" stroke="#808080" strokeWidth="1.5"/>
                                                        </svg>
                                                        <input
                                                            type="text"
                                                            name="lastName"
                                                            placeholder="Enter your last name"
                                                            value={formData.lastName}
                                                            onChange={handleChange}
                                                            required
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="form-group-modern">
                                                <label>Phone Number</label>
                                                <div className="input-wrapper">
                                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                                                        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z" stroke="#808080" strokeWidth="1.5"/>
                                                    </svg>
                                                    <input
                                                        type="tel"
                                                        name="phone"
                                                        placeholder="Enter your phone number"
                                                        value={formData.phone}
                                                        onChange={handleChange}
                                                    />
                                                </div>
                                            </div>
                                        </>
                                    )}

                                    <div className="form-group-modern">
                                        <label>Email Address</label>
                                        <div className="input-wrapper">
                                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" stroke="#808080" strokeWidth="1.5"/>
                                                <polyline points="22,6 12,13 2,6" stroke="#808080" strokeWidth="1.5"/>
                                            </svg>
                                            <input
                                                type="email"
                                                name="email"
                                                placeholder="Enter your email address"
                                                value={formData.email}
                                                onChange={handleChange}
                                                required
                                            />
                                        </div>
                                    </div>

                                    <div className="form-group-modern">
                                        <label>Password</label>
                                        <div className="input-wrapper">
                                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                                                <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="#808080" strokeWidth="1.5"/>
                                                <circle cx="12" cy="16" r="1" fill="#808080"/>
                                                <path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="#808080" strokeWidth="1.5"/>
                                            </svg>
                                            <input
                                                type="password"
                                                name="password"
                                                placeholder="Enter your password"
                                                value={formData.password}
                                                onChange={handleChange}
                                                required
                                            />
                                        </div>
                                    </div>

                                    {isLogin && (
                                        <div className="form-options-modern">
                                            <Link to="/forgot-password" className="forgot-link-modern">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                    <circle cx="12" cy="12" r="10" stroke="#F0B21B" strokeWidth="1.5"/>
                                                    <path d="M12 6v6l4 2" stroke="#F0B21B" strokeWidth="1.5"/>
                                                </svg>
                                                Forgot Password?
                                            </Link>
                                        </div>
                                    )}

                                    <button type="submit" className="auth-submit-btn-modern" disabled={loading}>
                                        <div className="btn-content">
                                            {loading ? (
                                                <>
                                                    <div className="loading-spinner"></div>
                                                    <span>Please wait...</span>
                                                </>
                                            ) : (
                                                <>
                                                    <span>{isLogin ? 'Sign In' : 'Create Account'}</span>
                                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                                        <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                    </svg>
                                                </>
                                            )}
                                        </div>
                                    </button>
                                </form>

                                {!isLogin && (
                                    <div className="terms-notice-modern">
                                        <div className="terms-icon">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M9 12l2 2 4-4" stroke="#F0B21B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                <circle cx="12" cy="12" r="9" stroke="#F0B21B" strokeWidth="1.5"/>
                                            </svg>
                                        </div>
                                        <p>
                                            By creating an account, you agree to our{' '}
                                            <Link to="/terms">Terms of Service</Link> and{' '}
                                            <Link to="/privacy">Privacy Policy</Link>
                                        </p>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Right Side - Switch */}
                        <div className="auth-switch-container">
                            <div className="auth-switch-card">
                                <div className="switch-icon">
                                    <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
                                        {isLogin ? (
                                            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2M9 7a4 4 0 1 0 8 0 4 4 0 0 0-8 0M22 11l-3-3m0 0l-3 3m3-3h-6" stroke="#F0B21B" strokeWidth="2"/>
                                        ) : (
                                            <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4M10 17l5-5-5-5M15 12H3" stroke="#F0B21B" strokeWidth="2"/>
                                        )}
                                    </svg>
                                </div>
                                <h3>{isLogin ? 'New to DesignXcel?' : 'Already have an account?'}</h3>
                                <p>
                                    {isLogin
                                        ? 'Join thousands of satisfied customers who trust us for their office furniture needs. Create an account to unlock exclusive benefits and personalized recommendations.'
                                        : 'Welcome back! Sign in to access your saved items, order history, and continue your seamless shopping experience with us.'
                                    }
                                </p>
                                <div className="switch-features">
                                    <div className="feature-item">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                            <path d="M9 12l2 2 4-4" stroke="#F0B21B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            <circle cx="12" cy="12" r="9" stroke="#F0B21B" strokeWidth="1.5"/>
                                        </svg>
                                        <span>{isLogin ? 'Personalized recommendations' : 'Quick checkout process'}</span>
                                    </div>
                                    <div className="feature-item">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                            <path d="M9 12l2 2 4-4" stroke="#F0B21B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            <circle cx="12" cy="12" r="9" stroke="#F0B21B" strokeWidth="1.5"/>
                                        </svg>
                                        <span>{isLogin ? 'Exclusive member discounts' : 'Order tracking & history'}</span>
                                    </div>
                                    <div className="feature-item">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                            <path d="M9 12l2 2 4-4" stroke="#F0B21B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            <circle cx="12" cy="12" r="9" stroke="#F0B21B" strokeWidth="1.5"/>
                                        </svg>
                                        <span>{isLogin ? 'Priority customer support' : 'Saved favorites & wishlist'}</span>
                                    </div>
                                </div>
                                <button
                                    className="switch-btn-modern"
                                    onClick={() => {
                                        setIsLogin(!isLogin);
                                        setError('');
                                        setFormData({
                                            email: '',
                                            password: '',
                                            firstName: '',
                                            lastName: '',
                                            phone: ''
                                        });
                                    }}
                                >
                                    <span>{isLogin ? 'Create Account' : 'Sign In'}</span>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                        <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Login;