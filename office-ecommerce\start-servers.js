const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Office E-commerce Development Servers...\n');

// Start Backend Server
console.log('📡 Starting Backend Server...');
const backendProcess = spawn('node', ['server.js'], {
    cwd: path.join(__dirname, 'backend'),
    stdio: 'inherit',
    shell: true
});

backendProcess.on('error', (error) => {
    console.error('❌ Backend server error:', error);
});

// Wait a bit before starting frontend
setTimeout(() => {
    console.log('🌐 Starting Frontend Server...');
    const frontendProcess = spawn('npm', ['start'], {
        cwd: path.join(__dirname, 'frontend'),
        stdio: 'inherit',
        shell: true
    });

    frontendProcess.on('error', (error) => {
        console.error('❌ Frontend server error:', error);
    });
}, 3000);

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down servers...');
    process.exit(0);
});

console.log('\n✅ Servers are starting...');
console.log('📡 Backend: http://localhost:5001');
console.log('🌐 Frontend: http://localhost:3000');
console.log('\nPress Ctrl+C to stop servers');
