{"ast": null, "code": "/**\n * Application Constants\n * Centralized location for all application constants\n */\n\n// API Configuration\nexport const API_CONFIG = {\n  BASE_URL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',\n  TIMEOUT: 30000,\n  RETRY_ATTEMPTS: 3\n};\n\n// Product Configuration\nexport const PRODUCT_CONFIG = {\n  IMAGES_PER_PRODUCT: 5,\n  MAX_IMAGE_SIZE: 5 * 1024 * 1024,\n  // 5MB\n  SUPPORTED_IMAGE_FORMATS: ['jpg', 'jpeg', 'png', 'webp'],\n  DEFAULT_IMAGE: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',\n  FALLBACK_IMAGE: '/placeholder-image.jpg'\n};\n\n// Pagination\nexport const PAGINATION = {\n  DEFAULT_PAGE_SIZE: 12,\n  MAX_PAGE_SIZE: 50,\n  PAGE_SIZE_OPTIONS: [6, 12, 24, 48]\n};\n\n// Price Configuration\nexport const PRICE_CONFIG = {\n  CURRENCY: 'USD',\n  LOCALE: 'en-US',\n  DECIMAL_PLACES: 2,\n  MIN_PRICE: 0,\n  MAX_PRICE: 999999\n};\n\n// Configuration Options\nexport const CONFIGURATION_OPTIONS = {\n  COLORS: {\n    default: {\n      name: 'Default',\n      multiplier: 1\n    },\n    cherry: {\n      name: 'Cherry Wood',\n      multiplier: 1.15\n    },\n    'dark-walnut': {\n      name: 'Dark Walnut',\n      multiplier: 1.10\n    },\n    black: {\n      name: 'Black',\n      multiplier: 1.05\n    },\n    white: {\n      name: 'White',\n      multiplier: 1.05\n    }\n  },\n  MATERIALS: {\n    default: {\n      name: 'Standard',\n      multiplier: 1\n    },\n    'solid-wood': {\n      name: 'Solid Wood',\n      multiplier: 1.25\n    },\n    metal: {\n      name: 'Metal',\n      multiplier: 1.20\n    },\n    glass: {\n      name: 'Glass',\n      multiplier: 1.15\n    }\n  },\n  SIZES: {\n    standard: {\n      name: 'Standard',\n      multiplier: 1\n    },\n    compact: {\n      name: 'Compact',\n      multiplier: 0.90\n    },\n    large: {\n      name: 'Large',\n      multiplier: 1.20\n    },\n    xl: {\n      name: 'Extra Large',\n      multiplier: 1.35\n    }\n  }\n};\n\n// 3D Configurable Product Types\nexport const CONFIGURABLE_PRODUCT_TYPES = ['table', 'desk', 'chair', 'cabinet', 'storage', 'workstation', 'shelf'];\n\n// User Roles\nexport const USER_ROLES = {\n  ADMIN: 'admin',\n  CUSTOMER: 'customer',\n  MANAGER: 'manager'\n};\n\n// Order Status\nexport const ORDER_STATUS = {\n  PENDING: 'pending',\n  CONFIRMED: 'confirmed',\n  PROCESSING: 'processing',\n  SHIPPED: 'shipped',\n  DELIVERED: 'delivered',\n  CANCELLED: 'cancelled'\n};\n\n// Payment Status\nexport const PAYMENT_STATUS = {\n  PENDING: 'pending',\n  COMPLETED: 'completed',\n  FAILED: 'failed',\n  REFUNDED: 'refunded'\n};\n\n// Local Storage Keys\nexport const STORAGE_KEYS = {\n  AUTH_TOKEN: 'auth_token',\n  USER_DATA: 'user_data',\n  CART_DATA: 'cart_data',\n  LANGUAGE: 'selected_language',\n  CURRENCY: 'selected_currency',\n  THEME: 'theme_preference'\n};\n\n// Validation Rules\nexport const VALIDATION = {\n  EMAIL_REGEX: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  PHONE_REGEX: /^[\\+]?[1-9][\\d]{0,15}$/,\n  PASSWORD_MIN_LENGTH: 8,\n  NAME_MIN_LENGTH: 2,\n  NAME_MAX_LENGTH: 50\n};\n\n// Error Messages\nexport const ERROR_MESSAGES = {\n  NETWORK_ERROR: 'Network error. Please check your connection.',\n  UNAUTHORIZED: 'You are not authorized to perform this action.',\n  FORBIDDEN: 'Access denied.',\n  NOT_FOUND: 'The requested resource was not found.',\n  SERVER_ERROR: 'Internal server error. Please try again later.',\n  VALIDATION_ERROR: 'Please check your input and try again.',\n  TIMEOUT_ERROR: 'Request timed out. Please try again.'\n};\n\n// Success Messages\nexport const SUCCESS_MESSAGES = {\n  LOGIN_SUCCESS: 'Login successful!',\n  LOGOUT_SUCCESS: 'Logged out successfully.',\n  REGISTRATION_SUCCESS: 'Account created successfully!',\n  PROFILE_UPDATED: 'Profile updated successfully.',\n  ITEM_ADDED_TO_CART: 'Item added to cart.',\n  ORDER_PLACED: 'Order placed successfully!',\n  PASSWORD_CHANGED: 'Password changed successfully.'\n};\n\n// Animation Durations (in milliseconds)\nexport const ANIMATION = {\n  FAST: 150,\n  NORMAL: 300,\n  SLOW: 500,\n  VERY_SLOW: 1000\n};\n\n// Breakpoints (matching CSS)\nexport const BREAKPOINTS = {\n  MOBILE: 480,\n  TABLET: 768,\n  DESKTOP: 1024,\n  LARGE_DESKTOP: 1200\n};\n\n// Feature Flags\nexport const FEATURES = {\n  ENABLE_3D_CONFIGURATOR: true,\n  ENABLE_WISHLIST: true,\n  ENABLE_REVIEWS: true,\n  ENABLE_CHAT_SUPPORT: false,\n  ENABLE_ANALYTICS: true\n};\n\n// Contact Information\nexport const CONTACT_INFO = {\n  EMAIL: '<EMAIL>',\n  PHONE: '(02) 413-6682',\n  ADDRESS: '#1 Binakla Street Cor Biak na Bato Brgy, Manresa, Quezon City',\n  BUSINESS_HOURS: 'Mon-Fri: 9AM-6PM, Sat: 10AM-4PM'\n};\n\n// Social Media Links\nexport const SOCIAL_LINKS = {\n  FACEBOOK: 'https://facebook.com/designexcellence',\n  INSTAGRAM: 'https://instagram.com/designexcellence',\n  TWITTER: 'https://twitter.com/designexcellence'\n};\n\n// Default Export\nexport default {\n  API_CONFIG,\n  PRODUCT_CONFIG,\n  PAGINATION,\n  PRICE_CONFIG,\n  CONFIGURATION_OPTIONS,\n  CONFIGURABLE_PRODUCT_TYPES,\n  USER_ROLES,\n  ORDER_STATUS,\n  PAYMENT_STATUS,\n  STORAGE_KEYS,\n  VALIDATION,\n  ERROR_MESSAGES,\n  SUCCESS_MESSAGES,\n  ANIMATION,\n  BREAKPOINTS,\n  FEATURES,\n  CONTACT_INFO,\n  SOCIAL_LINKS\n};", "map": {"version": 3, "names": ["API_CONFIG", "BASE_URL", "process", "env", "REACT_APP_API_URL", "TIMEOUT", "RETRY_ATTEMPTS", "PRODUCT_CONFIG", "IMAGES_PER_PRODUCT", "MAX_IMAGE_SIZE", "SUPPORTED_IMAGE_FORMATS", "DEFAULT_IMAGE", "FALLBACK_IMAGE", "PAGINATION", "DEFAULT_PAGE_SIZE", "MAX_PAGE_SIZE", "PAGE_SIZE_OPTIONS", "PRICE_CONFIG", "CURRENCY", "LOCALE", "DECIMAL_PLACES", "MIN_PRICE", "MAX_PRICE", "CONFIGURATION_OPTIONS", "COLORS", "default", "name", "multiplier", "cherry", "black", "white", "MATERIALS", "metal", "glass", "SIZES", "standard", "compact", "large", "xl", "CONFIGURABLE_PRODUCT_TYPES", "USER_ROLES", "ADMIN", "CUSTOMER", "MANAGER", "ORDER_STATUS", "PENDING", "CONFIRMED", "PROCESSING", "SHIPPED", "DELIVERED", "CANCELLED", "PAYMENT_STATUS", "COMPLETED", "FAILED", "REFUNDED", "STORAGE_KEYS", "AUTH_TOKEN", "USER_DATA", "CART_DATA", "LANGUAGE", "THEME", "VALIDATION", "EMAIL_REGEX", "PHONE_REGEX", "PASSWORD_MIN_LENGTH", "NAME_MIN_LENGTH", "NAME_MAX_LENGTH", "ERROR_MESSAGES", "NETWORK_ERROR", "UNAUTHORIZED", "FORBIDDEN", "NOT_FOUND", "SERVER_ERROR", "VALIDATION_ERROR", "TIMEOUT_ERROR", "SUCCESS_MESSAGES", "LOGIN_SUCCESS", "LOGOUT_SUCCESS", "REGISTRATION_SUCCESS", "PROFILE_UPDATED", "ITEM_ADDED_TO_CART", "ORDER_PLACED", "PASSWORD_CHANGED", "ANIMATION", "FAST", "NORMAL", "SLOW", "VERY_SLOW", "BREAKPOINTS", "MOBILE", "TABLET", "DESKTOP", "LARGE_DESKTOP", "FEATURES", "ENABLE_3D_CONFIGURATOR", "ENABLE_WISHLIST", "ENABLE_REVIEWS", "ENABLE_CHAT_SUPPORT", "ENABLE_ANALYTICS", "CONTACT_INFO", "EMAIL", "PHONE", "ADDRESS", "BUSINESS_HOURS", "SOCIAL_LINKS", "FACEBOOK", "INSTAGRAM", "TWITTER"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/constants/index.js"], "sourcesContent": ["/**\n * Application Constants\n * Centralized location for all application constants\n */\n\n// API Configuration\nexport const API_CONFIG = {\n  BASE_URL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',\n  TIMEOUT: 30000,\n  RETRY_ATTEMPTS: 3\n};\n\n// Product Configuration\nexport const PRODUCT_CONFIG = {\n  IMAGES_PER_PRODUCT: 5,\n  MAX_IMAGE_SIZE: 5 * 1024 * 1024, // 5MB\n  SUPPORTED_IMAGE_FORMATS: ['jpg', 'jpeg', 'png', 'webp'],\n  DEFAULT_IMAGE: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',\n  FALLBACK_IMAGE: '/placeholder-image.jpg'\n};\n\n// Pagination\nexport const PAGINATION = {\n  DEFAULT_PAGE_SIZE: 12,\n  MAX_PAGE_SIZE: 50,\n  PAGE_SIZE_OPTIONS: [6, 12, 24, 48]\n};\n\n// Price Configuration\nexport const PRICE_CONFIG = {\n  CURRENCY: 'USD',\n  LOCALE: 'en-US',\n  DECIMAL_PLACES: 2,\n  MIN_PRICE: 0,\n  MAX_PRICE: 999999\n};\n\n// Configuration Options\nexport const CONFIGURATION_OPTIONS = {\n  COLORS: {\n    default: { name: 'Default', multiplier: 1 },\n    cherry: { name: 'Cherry Wood', multiplier: 1.15 },\n    'dark-walnut': { name: 'Dark Walnut', multiplier: 1.10 },\n    black: { name: 'Black', multiplier: 1.05 },\n    white: { name: 'White', multiplier: 1.05 }\n  },\n  MATERIALS: {\n    default: { name: 'Standard', multiplier: 1 },\n    'solid-wood': { name: 'Solid Wood', multiplier: 1.25 },\n    metal: { name: 'Metal', multiplier: 1.20 },\n    glass: { name: 'Glass', multiplier: 1.15 }\n  },\n  SIZES: {\n    standard: { name: 'Standard', multiplier: 1 },\n    compact: { name: 'Compact', multiplier: 0.90 },\n    large: { name: 'Large', multiplier: 1.20 },\n    xl: { name: 'Extra Large', multiplier: 1.35 }\n  }\n};\n\n// 3D Configurable Product Types\nexport const CONFIGURABLE_PRODUCT_TYPES = [\n  'table', 'desk', 'chair', 'cabinet', 'storage', 'workstation', 'shelf'\n];\n\n// User Roles\nexport const USER_ROLES = {\n  ADMIN: 'admin',\n  CUSTOMER: 'customer',\n  MANAGER: 'manager'\n};\n\n// Order Status\nexport const ORDER_STATUS = {\n  PENDING: 'pending',\n  CONFIRMED: 'confirmed',\n  PROCESSING: 'processing',\n  SHIPPED: 'shipped',\n  DELIVERED: 'delivered',\n  CANCELLED: 'cancelled'\n};\n\n// Payment Status\nexport const PAYMENT_STATUS = {\n  PENDING: 'pending',\n  COMPLETED: 'completed',\n  FAILED: 'failed',\n  REFUNDED: 'refunded'\n};\n\n// Local Storage Keys\nexport const STORAGE_KEYS = {\n  AUTH_TOKEN: 'auth_token',\n  USER_DATA: 'user_data',\n  CART_DATA: 'cart_data',\n  LANGUAGE: 'selected_language',\n  CURRENCY: 'selected_currency',\n  THEME: 'theme_preference'\n};\n\n// Validation Rules\nexport const VALIDATION = {\n  EMAIL_REGEX: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  PHONE_REGEX: /^[\\+]?[1-9][\\d]{0,15}$/,\n  PASSWORD_MIN_LENGTH: 8,\n  NAME_MIN_LENGTH: 2,\n  NAME_MAX_LENGTH: 50\n};\n\n// Error Messages\nexport const ERROR_MESSAGES = {\n  NETWORK_ERROR: 'Network error. Please check your connection.',\n  UNAUTHORIZED: 'You are not authorized to perform this action.',\n  FORBIDDEN: 'Access denied.',\n  NOT_FOUND: 'The requested resource was not found.',\n  SERVER_ERROR: 'Internal server error. Please try again later.',\n  VALIDATION_ERROR: 'Please check your input and try again.',\n  TIMEOUT_ERROR: 'Request timed out. Please try again.'\n};\n\n// Success Messages\nexport const SUCCESS_MESSAGES = {\n  LOGIN_SUCCESS: 'Login successful!',\n  LOGOUT_SUCCESS: 'Logged out successfully.',\n  REGISTRATION_SUCCESS: 'Account created successfully!',\n  PROFILE_UPDATED: 'Profile updated successfully.',\n  ITEM_ADDED_TO_CART: 'Item added to cart.',\n  ORDER_PLACED: 'Order placed successfully!',\n  PASSWORD_CHANGED: 'Password changed successfully.'\n};\n\n// Animation Durations (in milliseconds)\nexport const ANIMATION = {\n  FAST: 150,\n  NORMAL: 300,\n  SLOW: 500,\n  VERY_SLOW: 1000\n};\n\n// Breakpoints (matching CSS)\nexport const BREAKPOINTS = {\n  MOBILE: 480,\n  TABLET: 768,\n  DESKTOP: 1024,\n  LARGE_DESKTOP: 1200\n};\n\n// Feature Flags\nexport const FEATURES = {\n  ENABLE_3D_CONFIGURATOR: true,\n  ENABLE_WISHLIST: true,\n  ENABLE_REVIEWS: true,\n  ENABLE_CHAT_SUPPORT: false,\n  ENABLE_ANALYTICS: true\n};\n\n// Contact Information\nexport const CONTACT_INFO = {\n  EMAIL: '<EMAIL>',\n  PHONE: '(02) 413-6682',\n  ADDRESS: '#1 Binakla Street Cor Biak na Bato Brgy, Manresa, Quezon City',\n  BUSINESS_HOURS: 'Mon-Fri: 9AM-6PM, Sat: 10AM-4PM'\n};\n\n// Social Media Links\nexport const SOCIAL_LINKS = {\n  FACEBOOK: 'https://facebook.com/designexcellence',\n  INSTAGRAM: 'https://instagram.com/designexcellence',\n  TWITTER: 'https://twitter.com/designexcellence'\n};\n\n// Default Export\nexport default {\n  API_CONFIG,\n  PRODUCT_CONFIG,\n  PAGINATION,\n  PRICE_CONFIG,\n  CONFIGURATION_OPTIONS,\n  CONFIGURABLE_PRODUCT_TYPES,\n  USER_ROLES,\n  ORDER_STATUS,\n  PAYMENT_STATUS,\n  STORAGE_KEYS,\n  VALIDATION,\n  ERROR_MESSAGES,\n  SUCCESS_MESSAGES,\n  ANIMATION,\n  BREAKPOINTS,\n  FEATURES,\n  CONTACT_INFO,\n  SOCIAL_LINKS\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA,OAAO,MAAMA,UAAU,GAAG;EACxBC,QAAQ,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;EACtEC,OAAO,EAAE,KAAK;EACdC,cAAc,EAAE;AAClB,CAAC;;AAED;AACA,OAAO,MAAMC,cAAc,GAAG;EAC5BC,kBAAkB,EAAE,CAAC;EACrBC,cAAc,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI;EAAE;EACjCC,uBAAuB,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;EACvDC,aAAa,EAAE,mFAAmF;EAClGC,cAAc,EAAE;AAClB,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,iBAAiB,EAAE,EAAE;EACrBC,aAAa,EAAE,EAAE;EACjBC,iBAAiB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACnC,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1BC,QAAQ,EAAE,KAAK;EACfC,MAAM,EAAE,OAAO;EACfC,cAAc,EAAE,CAAC;EACjBC,SAAS,EAAE,CAAC;EACZC,SAAS,EAAE;AACb,CAAC;;AAED;AACA,OAAO,MAAMC,qBAAqB,GAAG;EACnCC,MAAM,EAAE;IACNC,OAAO,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,UAAU,EAAE;IAAE,CAAC;IAC3CC,MAAM,EAAE;MAAEF,IAAI,EAAE,aAAa;MAAEC,UAAU,EAAE;IAAK,CAAC;IACjD,aAAa,EAAE;MAAED,IAAI,EAAE,aAAa;MAAEC,UAAU,EAAE;IAAK,CAAC;IACxDE,KAAK,EAAE;MAAEH,IAAI,EAAE,OAAO;MAAEC,UAAU,EAAE;IAAK,CAAC;IAC1CG,KAAK,EAAE;MAAEJ,IAAI,EAAE,OAAO;MAAEC,UAAU,EAAE;IAAK;EAC3C,CAAC;EACDI,SAAS,EAAE;IACTN,OAAO,EAAE;MAAEC,IAAI,EAAE,UAAU;MAAEC,UAAU,EAAE;IAAE,CAAC;IAC5C,YAAY,EAAE;MAAED,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAK,CAAC;IACtDK,KAAK,EAAE;MAAEN,IAAI,EAAE,OAAO;MAAEC,UAAU,EAAE;IAAK,CAAC;IAC1CM,KAAK,EAAE;MAAEP,IAAI,EAAE,OAAO;MAAEC,UAAU,EAAE;IAAK;EAC3C,CAAC;EACDO,KAAK,EAAE;IACLC,QAAQ,EAAE;MAAET,IAAI,EAAE,UAAU;MAAEC,UAAU,EAAE;IAAE,CAAC;IAC7CS,OAAO,EAAE;MAAEV,IAAI,EAAE,SAAS;MAAEC,UAAU,EAAE;IAAK,CAAC;IAC9CU,KAAK,EAAE;MAAEX,IAAI,EAAE,OAAO;MAAEC,UAAU,EAAE;IAAK,CAAC;IAC1CW,EAAE,EAAE;MAAEZ,IAAI,EAAE,aAAa;MAAEC,UAAU,EAAE;IAAK;EAC9C;AACF,CAAC;;AAED;AACA,OAAO,MAAMY,0BAA0B,GAAG,CACxC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,OAAO,CACvE;;AAED;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,UAAU;EACpBC,OAAO,EAAE;AACX,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1BC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,WAAW;EACtBC,UAAU,EAAE,YAAY;EACxBC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,WAAW;EACtBC,SAAS,EAAE;AACb,CAAC;;AAED;AACA,OAAO,MAAMC,cAAc,GAAG;EAC5BN,OAAO,EAAE,SAAS;EAClBO,SAAS,EAAE,WAAW;EACtBC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE;AACZ,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1BC,UAAU,EAAE,YAAY;EACxBC,SAAS,EAAE,WAAW;EACtBC,SAAS,EAAE,WAAW;EACtBC,QAAQ,EAAE,mBAAmB;EAC7BzC,QAAQ,EAAE,mBAAmB;EAC7B0C,KAAK,EAAE;AACT,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,WAAW,EAAE,4BAA4B;EACzCC,WAAW,EAAE,wBAAwB;EACrCC,mBAAmB,EAAE,CAAC;EACtBC,eAAe,EAAE,CAAC;EAClBC,eAAe,EAAE;AACnB,CAAC;;AAED;AACA,OAAO,MAAMC,cAAc,GAAG;EAC5BC,aAAa,EAAE,8CAA8C;EAC7DC,YAAY,EAAE,gDAAgD;EAC9DC,SAAS,EAAE,gBAAgB;EAC3BC,SAAS,EAAE,uCAAuC;EAClDC,YAAY,EAAE,gDAAgD;EAC9DC,gBAAgB,EAAE,wCAAwC;EAC1DC,aAAa,EAAE;AACjB,CAAC;;AAED;AACA,OAAO,MAAMC,gBAAgB,GAAG;EAC9BC,aAAa,EAAE,mBAAmB;EAClCC,cAAc,EAAE,0BAA0B;EAC1CC,oBAAoB,EAAE,+BAA+B;EACrDC,eAAe,EAAE,+BAA+B;EAChDC,kBAAkB,EAAE,qBAAqB;EACzCC,YAAY,EAAE,4BAA4B;EAC1CC,gBAAgB,EAAE;AACpB,CAAC;;AAED;AACA,OAAO,MAAMC,SAAS,GAAG;EACvBC,IAAI,EAAE,GAAG;EACTC,MAAM,EAAE,GAAG;EACXC,IAAI,EAAE,GAAG;EACTC,SAAS,EAAE;AACb,CAAC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAG;EACzBC,MAAM,EAAE,GAAG;EACXC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,IAAI;EACbC,aAAa,EAAE;AACjB,CAAC;;AAED;AACA,OAAO,MAAMC,QAAQ,GAAG;EACtBC,sBAAsB,EAAE,IAAI;EAC5BC,eAAe,EAAE,IAAI;EACrBC,cAAc,EAAE,IAAI;EACpBC,mBAAmB,EAAE,KAAK;EAC1BC,gBAAgB,EAAE;AACpB,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1BC,KAAK,EAAE,6BAA6B;EACpCC,KAAK,EAAE,eAAe;EACtBC,OAAO,EAAE,+DAA+D;EACxEC,cAAc,EAAE;AAClB,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1BC,QAAQ,EAAE,uCAAuC;EACjDC,SAAS,EAAE,wCAAwC;EACnDC,OAAO,EAAE;AACX,CAAC;;AAED;AACA,eAAe;EACb3G,UAAU;EACVO,cAAc;EACdM,UAAU;EACVI,YAAY;EACZM,qBAAqB;EACrBgB,0BAA0B;EAC1BC,UAAU;EACVI,YAAY;EACZO,cAAc;EACdI,YAAY;EACZM,UAAU;EACVM,cAAc;EACdQ,gBAAgB;EAChBQ,SAAS;EACTK,WAAW;EACXK,QAAQ;EACRM,YAAY;EACZK;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}